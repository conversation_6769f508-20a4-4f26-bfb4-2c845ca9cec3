#ddev-generated
# If you remove the ddev-generated line above you
# are responsible for maintaining this file. DDEV will not then
# update it, for example if you add `additional_hostnames`, etc.

http:
  routers:
    comave-magento-elasticsearch-9200-http:
      entrypoints:
        - http-9200
      rule: HostRegexp(`^comave-magento\.ddev\.site$`)|| HostRegexp(`^foodcomave\.ddev\.site$`)|| HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-elasticsearch-9200"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "comave-magento-redirectHttps"
    comave-magento-rabbitmq-15672-http:
      entrypoints:
        - http-15672
      rule: HostRegexp(`^comave-magento\.ddev\.site$`)|| HostRegexp(`^foodcomave\.ddev\.site$`)|| HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-rabbitmq-15672"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "comave-magento-redirectHttps"
    comave-magento-web-80-http:
      entrypoints:
        - http-80
      rule: HostRegexp(`^comave-magento\.ddev\.site$`)|| HostRegexp(`^foodcomave\.ddev\.site$`)|| HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-web-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "comave-magento-redirectHttps"
    comave-magento-web-8025-http:
      entrypoints:
        - http-8025
      rule: HostRegexp(`^comave-magento\.ddev\.site$`)|| HostRegexp(`^foodcomave\.ddev\.site$`)|| HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-web-8025"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "comave-magento-redirectHttps"
    comave-magento-xhgui-80-http:
      entrypoints:
        - http-8143
      rule: HostRegexp(`^comave-magento\.ddev\.site$`)|| HostRegexp(`^foodcomave\.ddev\.site$`)|| HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-xhgui-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "comave-magento-redirectHttps"
    
    
    comave-magento-elasticsearch-9200-https:
      entrypoints:
        - http-9201
      rule: HostRegexp(`^comave-magento\.ddev\.site$`) || HostRegexp(`^foodcomave\.ddev\.site$`) || HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-elasticsearch-9200"
      ruleSyntax: v3
      
      tls: true
      
    
    comave-magento-rabbitmq-15672-https:
      entrypoints:
        - http-15673
      rule: HostRegexp(`^comave-magento\.ddev\.site$`) || HostRegexp(`^foodcomave\.ddev\.site$`) || HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-rabbitmq-15672"
      ruleSyntax: v3
      
      tls: true
      
    
    
    comave-magento-web-80-https:
      entrypoints:
        - http-443
      rule: HostRegexp(`^comave-magento\.ddev\.site$`) || HostRegexp(`^foodcomave\.ddev\.site$`) || HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-web-80"
      ruleSyntax: v3
      
      tls: true
      
    comave-magento-web-8025-https:
      entrypoints:
        - http-8026
      rule: HostRegexp(`^comave-magento\.ddev\.site$`) || HostRegexp(`^foodcomave\.ddev\.site$`) || HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-web-8025"
      ruleSyntax: v3
      
      tls: true
      
    
    comave-magento-xhgui-80-https:
      entrypoints:
        - http-8142
      rule: HostRegexp(`^comave-magento\.ddev\.site$`) || HostRegexp(`^foodcomave\.ddev\.site$`) || HostRegexp(`^vensta\.ddev\.site$`)
      
      service: "comave-magento-xhgui-80"
      ruleSyntax: v3
      
      tls: true
      
    

  middlewares:
    comave-magento-redirectHttps:
      redirectScheme:
        scheme: https
        permanent: true

  services:
    comave-magento-elasticsearch-9200:
      loadbalancer:
        servers:
          - url: http://ddev-comave-magento-elasticsearch:9200
        
    
    comave-magento-rabbitmq-15672:
      loadbalancer:
        servers:
          - url: http://ddev-comave-magento-rabbitmq:15672
        
    
    comave-magento-web-80:
      loadbalancer:
        servers:
          - url: http://ddev-comave-magento-web:80
        
    comave-magento-web-8025:
      loadbalancer:
        servers:
          - url: http://ddev-comave-magento-web:8025
        
    
    
    comave-magento-xhgui-80:
      loadbalancer:
        servers:
          - url: http://ddev-comave-magento-xhgui:80
        
    
    

tls:
  certificates:
    - certFile: /mnt/ddev-global-cache/traefik/certs/comave-magento.crt
      keyFile: /mnt/ddev-global-cache/traefik/certs/comave-magento.key