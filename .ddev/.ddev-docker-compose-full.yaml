name: ddev-comave-magento
networks:
    ddev_default:
        external: true
        name: ddev_default
    default:
        labels:
            com.ddev.platform: ddev
        name: ddev-comave-magento_default
services:
    db:
        build:
            args:
                BASE_IMAGE: ddev/ddev-dbserver-mariadb-10.4:v1.24.6
                gid: "20"
                uid: "501"
                username: chaaibiyouness
            context: /Users/<USER>/comave_magento/.ddev/.dbimageBuild
            dockerfile: Dockerfile
        cap_add:
            - SYS_NICE
        command: []
        container_name: ddev-comave-magento-db
        environment:
            BITNAMI_VOLUME_DIR: ""
            COLUMNS: "356"
            DDEV_DATABASE: mariadb:10.4
            DDEV_DATABASE_FAMILY: mysql
            DDEV_GOARCH: arm64
            DDEV_GOOS: darwin
            DDEV_HOSTNAME: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
            DDEV_PHP_VERSION: "8.3"
            DDEV_PRIMARY_URL: https://comave-magento.ddev.site
            DDEV_PRIMARY_URL_PORT: "443"
            DDEV_PRIMARY_URL_WITHOUT_PORT: https://comave-magento.ddev.site
            DDEV_PROJECT: comave-magento
            DDEV_PROJECT_TYPE: magento2
            DDEV_ROUTER_HTTP_PORT: "80"
            DDEV_ROUTER_HTTPS_PORT: "443"
            DDEV_SCHEME: https
            DDEV_SITENAME: comave-magento
            DDEV_TLD: ddev.site
            DOCKER_IP: 127.0.0.1
            HOST_DOCKER_INTERNAL_IP: ""
            IS_DDEV_PROJECT: "true"
            LINES: "33"
            MYSQL_HISTFILE: /mnt/ddev-global-cache/mysqlhistory/comave-magento-db/mysql_history
            PGDATABASE: db
            PGHOST: 127.0.0.1
            PGPASSWORD: db
            PGUSER: db
            POSTGRES_DB: db
            POSTGRES_PASSWORD: db
            POSTGRES_USER: db
            TZ: Africa/Casablanca
            USER: chaaibiyouness
        healthcheck:
            interval: 1s
            retries: 70
            start_interval: 1s
            start_period: 2m0s
            timeout: 1m10s
        hostname: comave-magento-db
        image: ddev/ddev-dbserver-mariadb-10.4:v1.24.6-comave-magento-built
        labels:
            com.ddev.app-type: magento2
            com.ddev.approot: /Users/<USER>/comave_magento
            com.ddev.platform: ddev
            com.ddev.site-name: comave-magento
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              published: "59776"
              target: 3306
        restart: "no"
        stop_grace_period: 1m0s
        user: "501:20"
        volumes:
            - source: database
              target: /var/lib/mysql
              type: volume
              volume:
                nocopy: true
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.ddev
              target: /mnt/ddev_config
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.ddev/db_snapshots
              target: /mnt/snapshots
              type: bind
            - source: ddev-global-cache
              target: /mnt/ddev-global-cache
              type: volume
              volume: {}
        working_dir: /home/<USER>
    elasticsearch:
        container_name: ddev-comave-magento-elasticsearch
        environment:
            ES_JAVA_OPTS: -Xms512m -Xmx512m
            HTTP_EXPOSE: 9200:9200
            HTTPS_EXPOSE: 9201:9200
            VIRTUAL_HOST: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
            bootstrap.memory_lock: "true"
            cluster.name: docker-cluster
            discovery.type: single-node
        expose:
            - "9200"
            - "9300"
        healthcheck:
            test:
                - CMD-SHELL
                - curl --fail -s elasticsearch:9200
        hostname: comave-magento-elasticsearch
        image: elasticsearch:7.17.14
        labels:
            com.ddev.approot: /Users/<USER>/comave_magento
            com.ddev.site-name: comave-magento
        networks:
            ddev_default: null
            default: null
        volumes:
            - source: elasticsearch
              target: /usr/share/elasticsearch/data
              type: volume
              volume: {}
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.ddev
              target: /mnt/ddev_config
              type: bind
    rabbitmq:
        container_name: ddev-comave-magento-rabbitmq
        environment:
            HTTP_EXPOSE: "15672"
            HTTPS_EXPOSE: 15673:15672
            RABBITMQ_DEFAULT_PASS: rabbitmq
            RABBITMQ_DEFAULT_USER: rabbitmq
            RABBITMQ_DEFAULT_VHOST: /
            RABBITMQ_ERLANG_COOKIE: SWQOKODSQALRPCLNMEQG
            VIRTUAL_HOST: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
        expose:
            - "15672"
        hostname: comave-magento-rabbitmq
        image: rabbitmq:3-management-alpine
        labels:
            com.ddev.approot: /Users/<USER>/comave_magento
            com.ddev.site-name: comave-magento
        networks:
            ddev_default: null
            default: null
        volumes:
            - source: rabbitmq
              target: /var/lib/rabbitmq/mnesia
              type: volume
              volume: {}
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.ddev
              target: /mnt/ddev_config
              type: bind
    web:
        build:
            args:
                BASE_IMAGE: ddev/ddev-webserver:v1.24.6
                DDEV_DATABASE: mariadb:10.4
                DDEV_PHP_VERSION: "8.3"
                gid: "20"
                uid: "501"
                username: chaaibiyouness
            context: /Users/<USER>/comave_magento/.ddev/.webimageBuild
            dockerfile: Dockerfile
        cap_add:
            - SYS_PTRACE
        command:
            - /pre-start.sh
        container_name: ddev-comave-magento-web
        depends_on:
            rabbitmq:
                condition: service_started
                required: true
                restart: true
        environment:
            COLUMNS: "356"
            COREPACK_ENABLE_DOWNLOAD_PROMPT: "0"
            COREPACK_HOME: /mnt/ddev-global-cache/corepack
            DDEV_APPROOT: /var/www/html
            DDEV_COMPOSER_ROOT: /var/www/html
            DDEV_DATABASE: mariadb:10.4
            DDEV_DATABASE_FAMILY: mysql
            DDEV_DOCROOT: pub
            DDEV_FILES_DIR: /var/www/html/pub/media
            DDEV_FILES_DIRS: /var/www/html/pub/media
            DDEV_GOARCH: arm64
            DDEV_GOOS: darwin
            DDEV_HOSTNAME: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
            DDEV_MUTAGEN_ENABLED: "true"
            DDEV_PHP_VERSION: "8.3"
            DDEV_PRIMARY_URL: https://comave-magento.ddev.site
            DDEV_PRIMARY_URL_PORT: "443"
            DDEV_PRIMARY_URL_WITHOUT_PORT: https://comave-magento.ddev.site
            DDEV_PROJECT: comave-magento
            DDEV_PROJECT_TYPE: magento2
            DDEV_ROUTER_HTTP_PORT: "80"
            DDEV_ROUTER_HTTPS_PORT: "443"
            DDEV_SCHEME: https
            DDEV_SITENAME: comave-magento
            DDEV_TLD: ddev.site
            DDEV_VERSION: v1.24.6
            DDEV_WEB_ENTRYPOINT: /mnt/ddev_config/web-entrypoint.d
            DDEV_WEBSERVER_TYPE: nginx-fpm
            DDEV_XDEBUG_ENABLED: "false"
            DDEV_XHPROF_MODE: prepend
            DEPLOY_NAME: local
            DOCKER_IP: 127.0.0.1
            DOCROOT: pub
            DRUSH_OPTIONS_URI: https://comave-magento.ddev.site
            HOST_DOCKER_INTERNAL_IP: ""
            HTTP_EXPOSE: 80:80,8025:8025
            HTTPS_EXPOSE: 443:80,8026:8025
            IS_DDEV_PROJECT: "true"
            LINES: "33"
            MYSQL_HISTFILE: /mnt/ddev-global-cache/mysqlhistory/comave-magento-web/mysql_history
            NODE_EXTRA_CA_CERTS: /mnt/ddev-global-cache/mkcert/rootCA.pem
            PGDATABASE: db
            PGHOST: db
            PGPASSWORD: db
            PGUSER: db
            PHP_IDE_CONFIG: serverName=comave-magento.ddev.site
            SSH_AUTH_SOCK: /home/<USER>/socket
            START_SCRIPT_TIMEOUT: "30"
            TZ: Africa/Casablanca
            USER: chaaibiyouness
            VIRTUAL_HOST: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
            npm_config_cache: /mnt/ddev-global-cache/npm
        external_links:
            - ddev-router:comave-magento.ddev.site
            - ddev-router:foodcomave.ddev.site
            - ddev-router:vensta.ddev.site
        healthcheck:
            interval: 1s
            retries: 70
            start_interval: 1s
            start_period: 2m0s
            timeout: 1m10s
        hostname: comave-magento-web
        image: ddev/ddev-webserver:v1.24.6-comave-magento-built
        labels:
            com.ddev.app-type: magento2
            com.ddev.approot: /Users/<USER>/comave_magento
            com.ddev.platform: ddev
            com.ddev.site-name: comave-magento
        links:
            - rabbitmq:rabbitmq
        networks:
            ddev_default: null
            default: null
        ports:
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 80
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 443
            - host_ip: 127.0.0.1
              mode: ingress
              protocol: tcp
              target: 8025
        restart: "no"
        user: "501:20"
        volumes:
            - source: project_mutagen
              target: /var/www
              type: volume
              volume:
                nocopy: true
            - source: project_mutagen
              target: /tmp/project_mutagen
              type: volume
              volume:
                nocopy: true
            - bind:
                create_host_path: true
              read_only: true
              source: /Users/<USER>/comave_magento/.ddev
              target: /mnt/ddev_config
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.ddev/xhprof
              target: /usr/local/bin/xhprof
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/pub/media
              target: /var/www/html/pub/media
              type: bind
            - bind:
                create_host_path: true
              source: /Users/<USER>/comave_magento/.git
              target: /var/www/html/.git
              type: bind
            - source: ddev-global-cache
              target: /mnt/ddev-global-cache
              type: volume
              volume: {}
            - source: ddev-ssh-agent_socket_dir
              target: /home/<USER>
              type: volume
              volume: {}
        working_dir: /var/www/html/
    xhgui:
        container_name: ddev-comave-magento-xhgui
        depends_on:
            db:
                condition: service_started
                required: true
        environment:
            DDEV_DATABASE_FAMILY: mysql
            HTTP_EXPOSE: 8143:80
            HTTPS_EXPOSE: 8142:80
            TZ: Africa/Casablanca
            VIRTUAL_HOST: comave-magento.ddev.site,foodcomave.ddev.site,vensta.ddev.site
            XHGUI_PDO_PASS: db
            XHGUI_PDO_USER: db
            XHGUI_SAVE_HANDLER: pdo
        image: ddev/ddev-xhgui:v1.24.6
        labels:
            com.ddev.approot: /Users/<USER>/comave_magento
            com.ddev.site-name: comave-magento
        links:
            - db
        networks:
            ddev_default: null
            default: null
        profiles:
            - xhgui
        restart: "no"
volumes:
    database:
        external: true
        name: comave-magento-mariadb
    ddev-global-cache:
        external: true
        name: ddev-global-cache
    ddev-ssh-agent_socket_dir:
        external: true
        name: ddev-ssh-agent_socket_dir
    elasticsearch:
        name: ddev-comave-magento_elasticsearch
    project_mutagen:
        external: true
        name: comave-magento_project_mutagen
    rabbitmq:
        name: comave-magento_rabbitmq
