diff --git a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/NotifyMe.php b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/NotifyMe.php
index f83237d..0a08db0 100644
--- a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/NotifyMe.php
+++ b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/NotifyMe.php
@@ -114,48 +114,79 @@ class NotifyMe implements ResolverInterface
         array $value = null,
         array $args = null
     ) : Value {
-        if ((!$context->getUserId()) || $context->getUserType() == UserContextInterface::USER_TYPE_GUEST) {
+        if ((!$context->getUserId()) || $context->getUserType() === UserContextInterface::USER_TYPE_GUEST) {
             throw new GraphQlAuthorizationException(
                 __(
                     'Current customer does not have access to the resource "%1"',
                     [\Magento\Customer\Model\Customer::ENTITY]
                 )
             );
+        } else if ($context->getUserType() == UserContextInterface::USER_TYPE_ADMIN && empty($args['email'])) {
+            throw new GraphQlAuthorizationException(
+                __(
+                    'The email field is required for the current admin context.',
+                    [\Magento\Customer\Model\Customer::ENTITY]
+                )
+            );
         }
 
         try {
-            $customerData = $this->getCustomerData($context->getUserId());
+            $customerData = $this->getCustomerData($context, $args);
+
+            if (
+                $context->getUserType() === UserContextInterface::USER_TYPE_CUSTOMER &&
+                !empty($args['email']) && $customerData['email'] !== $args['email']
+            ) {
+                throw new GraphQlAuthorizationException(
+                    __(
+                        'The email field should not be included in the customer context.',
+                        [\Magento\Customer\Model\Customer::ENTITY]
+                    )
+                );
+            }
+
             return $this->subscribeToNotification($customerData, $args);
 
         } catch (NoSuchEntityException $exception) {
             throw new GraphQlNoSuchEntityException(__($exception->getMessage()));
         } catch (LocalizedException $exception) {
-            throw new GraphQlNoSuchEntityException(__($e->getMessage()));
+            throw new GraphQlNoSuchEntityException(__($exception->getMessage()));
         }
     }
 
     /**
      * Get Customer data function
      *
-     * @param int $customerId
+     * @param \Magento\GraphQl\Model\Query\Context $context
+     * @param  array $args
      * @return array
      * @throws NoSuchEntityException|LocalizedException
      */
-    private function getCustomerData($customerId) : array
+    private function getCustomerData($context, $args) : array
     {
+        $customerData = [];
+
         try {
-            $customerData = [];
-            $customerColl = $this->customerModel->create()->getCollection()
-                ->addFieldToFilter("entity_id", ["eq" => $customerId]);
-            foreach ($customerColl as $customer) {
-                array_push($customerData, $customer->getData());
+            $collection = $this->customerModel->create()->getCollection();
+
+            if ($context->getUserType() === UserContextInterface::USER_TYPE_ADMIN) {
+                $collection->addFieldToFilter("email", ["eq" => $args['email']]);
+            } else {
+                $collection->addFieldToFilter("entity_id", ["eq" => $context->getUserId()]);
+            }
+
+            $customer = $collection->getFirstItem();
+
+            if ($customer->getId()) {
+                $customerData = $customer->getData();
             }
-            return isset($customerData[0])?$customerData[0] : [];
         } catch (NoSuchEntityException $e) {
             return [];
         } catch (LocalizedException $e) {
             throw new NoSuchEntityException(__($e->getMessage()));
         }
+
+        return $customerData;
     }
 
     /**
@@ -205,7 +236,8 @@ class NotifyMe implements ResolverInterface
                 $storeEmail,
                 $notifyAdmin,
                 $notifyCustomer,
-                $args
+                $args,
+                $customerData
             );
             
         } catch (\Exception $e) {
@@ -229,9 +261,10 @@ class NotifyMe implements ResolverInterface
      * @param string $notifyAdmin
      * @param string $notifyCustomer
      * @param array $args
+     * @param mixed $customerData
      * @return \Magento\Framework\GraphQl\Query\Resolver\ValueFactory
      */
-    private function refractor($senderName, $senderEmail, $storeName, $storeEmail, $notifyAdmin, $notifyCustomer, $args)
+    private function refractor($senderName, $senderEmail, $storeName, $storeEmail, $notifyAdmin, $notifyCustomer, $args, $customerData)
     {
         $senderInfoCustomer = [];
         $receiverInfoCustomer = [];
@@ -240,7 +273,7 @@ class NotifyMe implements ResolverInterface
         $productIds = [];
         $id = $args['mageproductId'];
         $productCollection = $this->product->load($id);
-        $email = $args['email'];
+        $email = !empty($args['email']) ? $args['email'] : $customerData['email'];
 
         // getting website code for current website
         $websiteCode = $this->_storeManager->getStore()->getWebsite()->getCode();
diff --git a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/SubscribedProductList.php b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/SubscribedProductList.php
index f97e5b4..a6fcdf0 100644
--- a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/SubscribedProductList.php
+++ b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/SubscribedProductList.php
@@ -81,10 +81,30 @@ class SubscribedProductList implements ResolverInterface
                     [\Magento\Customer\Model\Customer::ENTITY]
                 )
             );
+        } else if ($context->getUserType() == UserContextInterface::USER_TYPE_ADMIN && empty($args['email'])) {
+            throw new GraphQlAuthorizationException(
+                __(
+                    'The email field is required for the current admin context.',
+                    [\Magento\Customer\Model\Customer::ENTITY]
+                )
+            );
         }
 
         try {
-            $customerData = $this->getCustomerData($context->getUserId());
+            $customerData = $this->getCustomerData($context, $args);
+
+            if (
+                $context->getUserType() === UserContextInterface::USER_TYPE_CUSTOMER &&
+                !empty($args['email']) && $customerData['email'] !== $args['email']
+            ) {
+                throw new GraphQlAuthorizationException(
+                    __(
+                        'The email field should not be included in the customer context.',
+                        [\Magento\Customer\Model\Customer::ENTITY]
+                    )
+                );
+            }
+
             $result = $this->getSubscribedProductList($customerData, $args);
             $result = function () use ($result) {
                 return !empty($result) ? $result : [];
@@ -95,32 +115,43 @@ class SubscribedProductList implements ResolverInterface
         } catch (NoSuchEntityException $exception) {
             throw new GraphQlNoSuchEntityException(__($exception->getMessage()));
         } catch (LocalizedException $exception) {
-            throw new GraphQlNoSuchEntityException(__($e->getMessage()));
+            throw new GraphQlNoSuchEntityException(__($exception->getMessage()));
         }
     }
 
     /**
-     * Get Customer Data function
+     * Get Customer data function
      *
-     * @param int $customerId
+     * @param \Magento\GraphQl\Model\Query\Context $context
+     * @param  array $args
      * @return array
      * @throws NoSuchEntityException|LocalizedException
      */
-    private function getCustomerData($customerId) : array
+    private function getCustomerData($context, $args) : array
     {
+        $customerData = [];
+
         try {
-            $customerData = [];
-            $customerColl = $this->customerModel->create()->getCollection()
-                ->addFieldToFilter("entity_id", ["eq" => $customerId]);
-            foreach ($customerColl as $customer) {
-                array_push($customerData, $customer->getData());
+            $collection = $this->customerModel->create()->getCollection();
+
+            if ($context->getUserType() === UserContextInterface::USER_TYPE_ADMIN) {
+                $collection->addFieldToFilter("email", ["eq" => $args['email']]);
+            } else {
+                $collection->addFieldToFilter("entity_id", ["eq" => $context->getUserId()]);
+            }
+
+            $customer = $collection->getFirstItem();
+
+            if ($customer->getId()) {
+                $customerData = $customer->getData();
             }
-            return isset($customerData[0]) ? $customerData[0] : [];
         } catch (NoSuchEntityException $e) {
             return [];
         } catch (LocalizedException $e) {
             throw new NoSuchEntityException(__($e->getMessage()));
         }
+
+        return $customerData;
     }
 
     /**
diff --git a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/Unsubscribe.php b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/Unsubscribe.php
index 1949ed4..8d40f8b 100644
--- a/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/Unsubscribe.php
+++ b/src/app/code/Webkul/OutOfStockNotification/Model/Resolver/Unsubscribe.php
@@ -93,19 +93,37 @@ class Unsubscribe implements ResolverInterface
         array $value = null,
         array $args = null
     ) : Value {
-        if ($context->getUserType() != UserContextInterface::USER_TYPE_ADMIN) {
-            if ((!$context->getUserId()) || $context->getUserType() == UserContextInterface::USER_TYPE_GUEST) {
+        if ((!$context->getUserId()) || $context->getUserType() == UserContextInterface::USER_TYPE_GUEST) {
+            throw new GraphQlAuthorizationException(
+                __(
+                    'Current customer does not have access to the resource "%1"',
+                    [\Magento\Customer\Model\Customer::ENTITY]
+                )
+            );
+        } else if ($context->getUserType() == UserContextInterface::USER_TYPE_ADMIN && empty($args['email'])) {
+            throw new GraphQlAuthorizationException(
+                __(
+                    'The email field is required for the current admin context.',
+                    [\Magento\Customer\Model\Customer::ENTITY]
+                )
+            );
+        }
+
+        try {
+            $customerData = $this->getCustomerData($context, $args);
+
+            if (
+                $context->getUserType() === UserContextInterface::USER_TYPE_CUSTOMER &&
+                !empty($args['email']) && $customerData['email'] !== $args['email']
+            ) {
                 throw new GraphQlAuthorizationException(
                     __(
-                        'Current customer does not have access to the resource "%1"',
+                        'The email field should not be included in the customer context.',
                         [\Magento\Customer\Model\Customer::ENTITY]
                     )
                 );
             }
-        }
-       
-        try {
-            $customerData = $this->getCustomerData($context->getUserId());
+
             $result = $this->unsubscribeProduct($context, $customerData, $args);
             $result = function () use ($result) {
                 return !empty($result) ? $result : [];
@@ -121,27 +139,38 @@ class Unsubscribe implements ResolverInterface
     }
 
     /**
-     * Get Customer Data function
+     * Get Customer data function
      *
-     * @param array $customerId
+     * @param \Magento\GraphQl\Model\Query\Context $context
+     * @param  array $args
      * @return array
      * @throws NoSuchEntityException|LocalizedException
      */
-    private function getCustomerData($customerId) : array
+    private function getCustomerData($context, $args) : array
     {
+        $customerData = [];
+
         try {
-            $customerData = [];
-            $customerColl = $this->customerModel->create()->getCollection()
-                                ->addFieldToFilter("entity_id", ["eq" => $customerId]);
-            foreach ($customerColl as $customer) {
-                array_push($customerData, $customer->getData());
+            $collection = $this->customerModel->create()->getCollection();
+
+            if ($context->getUserType() === UserContextInterface::USER_TYPE_ADMIN) {
+                $collection->addFieldToFilter("email", ["eq" => $args['email']]);
+            } else {
+                $collection->addFieldToFilter("entity_id", ["eq" => $context->getUserId()]);
+            }
+
+            $customer = $collection->getFirstItem();
+
+            if ($customer->getId()) {
+                $customerData = $customer->getData();
             }
-            return isset($customerData[0]) ? $customerData[0] : [];
         } catch (NoSuchEntityException $e) {
             return [];
         } catch (LocalizedException $e) {
             throw new NoSuchEntityException(__($e->getMessage()));
         }
+
+        return $customerData;
     }
 
     /**
@@ -154,34 +183,34 @@ class Unsubscribe implements ResolverInterface
      */
     public function unsubscribeProduct($context, $customerData, $args) : array
     {
-        
         $productId = $args['id'];
         $customerEmail = $this->_customerModel->getCustomer()->getEmail();
-        $model = $this->productFactory->create()->getCollection()
-            ->addFieldToFilter('product_id', ['eq' => $productId])->getFirstItem();
+        $collection = $this->productFactory->create()
+            ->getCollection()
+            ->addFieldToFilter('product_id', ['eq' => $productId]);
+
+        if ($context->getUserType() === UserContextInterface::USER_TYPE_ADMIN) {
+            $collection->addFieldToFilter('email', ['eq' => $args['email']]);
+        }
+        else if ($context->getUserType() === UserContextInterface::USER_TYPE_CUSTOMER) {
+            $collection->addFieldToFilter('email', ['eq' => $customerData['email']]);
+        }
+
+        $model = $collection->getFirstItem();
         $notifyEmail = $model->getEmail();
-        if (isset($customerEmail, $notifyEmail) && $customerEmail == $notifyEmail) {
-            $model->delete();
-          
-            return [
-                'status' => 'success',
-                'msg' => __("Subscription of the Product deleted Successfully")
-            ];
-           
-        } elseif ($context->getUserType() == UserContextInterface::USER_TYPE_ADMIN) {
+
+        if (isset($customerEmail, $notifyEmail) && $customerEmail === $notifyEmail) {
             $model->delete();
-          
+
             return [
                 'status' => 'success',
                 'msg' => __("Subscription of the Product deleted Successfully")
             ];
-        } else {
-       
-            return [
-                'status' => "error",
-                'msg' => __("Unable to unsubscribe. Please check if you are subscribed to this product.")
-            ];
-              
         }
+
+        return [
+            'status' => "error",
+            'msg' => __("Unable to unsubscribe. Please check if you are subscribed to this product.")
+        ];
     }
 }
diff --git a/src/app/code/Webkul/OutOfStockNotification/etc/schema.graphqls b/src/app/code/Webkul/OutOfStockNotification/etc/schema.graphqls
index f72a524..4aaf902 100644
--- a/src/app/code/Webkul/OutOfStockNotification/etc/schema.graphqls
+++ b/src/app/code/Webkul/OutOfStockNotification/etc/schema.graphqls
@@ -10,13 +10,16 @@ type Query {
 
 	unsubscribe(
 	id: Int!
+	email: String
 	): Unsubscribe
 	@resolver(
 	class: "Webkul\\OutOfStockNotification\\Model\\Resolver\\Unsubscribe"
 	)
 	@cache(cacheable: false)
 
-	getsubscriptionproducts: ProductCollection
+	getsubscriptionproducts(
+	email: String
+	): ProductCollection
 	@resolver(
 	class: "Webkul\\OutOfStockNotification\\Model\\Resolver\\SubscribedProductList"
 	)
