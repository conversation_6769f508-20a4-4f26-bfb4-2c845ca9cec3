<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_FirebaseOTPLogin
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
<!--        <css src="Webkul_FirebaseOTPLogin::css/otp.css" />-->
    </head>
    <referenceContainer name="after.body.start">
        <block class="Magento\Framework\View\Element\Template" name="otp_modal_popup_template" template="Webkul_FirebaseOTPLogin::otpModalPopupTemplate.phtml">
        </block>
        <block class="Magento\Framework\View\Element\Template" name="otp_loader" template="Webkul_FirebaseOTPLogin::otpLoaderTemplate.phtml">
        </block>
        <block class="Magento\Framework\View\Element\Template" name="guest_otp_loader" template="Webkul_FirebaseOTPLogin::firebaseOtpLoaderTemplate.phtml" ifconfig="firebase_otp/generalsettings/otp_enable">
            <arguments>
                <argument name="view_model" xsi:type="object">Webkul\FirebaseOTPLogin\ViewModel\HelperViewModel</argument>
            </arguments>
        </block>
    </referenceContainer>

</page>
