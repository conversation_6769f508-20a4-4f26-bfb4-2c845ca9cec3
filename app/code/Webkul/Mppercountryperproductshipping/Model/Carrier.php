<?php
/**
 * Webkul Software
 *
 * @category Webkul
 * @package Webkul_Mppercountryperproductshipping
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license https://store.webkul.com/license.html
 */
namespace Webkul\Mppercountryperproductshipping\Model;

use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Shipping\Model\Rate\Result;
use Magento\Framework\Session\SessionManager;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Quote\Model\Quote\Item\OptionFactory;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\AddressFactory;
use Webkul\MarketplaceBaseShipping\Model\ShippingSettingRepository;

/**
 * Marketplace Percountry Perproduct shipping.
 *
 */
class Carrier extends \Webkul\MarketplaceBaseShipping\Model\Carrier\AbstractCarrier implements
    \Magento\Shipping\Model\Carrier\CarrierInterface
{
    /**
     * Code of the carrier.
     *
     * @var string
     */
    public const CODE = 'mppercountry';
    /**
     * Code of the carrier.
     *
     * @var string
     */
    protected $_code = self::CODE;
    /**
     * Rate request data.
     *
     * @var \Magento\Quote\Model\Quote\Address\RateRequest|null
     */
    protected $_request = null;

    /**
     * Rate result data.
     *
     * @var Result|null
     */
    protected $_result = null;
    /**
     * @var ObjectManagerInterface
     */
    protected $_objectManager = null;
    /**
     * @var SessionManager
     */
    protected $_coreSession;
    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;
    /**
     * @var \Webkul\Mppercountryperproductshipping\Helper\Data
     */
    protected $_currentHelper;
    /**
     * @var \Magento\Shipping\Model\Rate\ResultFactory
     */
    protected $_rateResultFactory;

    /**
     * @var \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory
     */
    protected $_rateMethodFactory;
    /**
     * Raw rate request data
     *
     * @var \Magento\Framework\DataObject|null
     */
    protected $_rawRequest = null;

    /**
     * Raw rate request data
     *
     * @var \Magento\Framework\DataObject|null
     */
    protected $baseRequest = null;
    /**
     * Raw rate request data
     *
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $_requestInterface = null;

    /**
     * Constructor function
     *
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Shipping\Model\Rate\ResultFactory $rateResultFactory
     * @param \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param SessionManager $coreSession
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Directory\Model\CurrencyFactory $currencyFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\Locale\FormatInterface $localeFormat
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\Framework\App\RequestInterface $requestInterface
     * @param PriceCurrencyInterface $priceCurrency
     * @param OptionFactory $optionFactory
     * @param CustomerFactory $customerFactory
     * @param AddressFactory $addressFactory
     * @param \Webkul\Marketplace\Model\ProductFactory $mpProductFactory
     * @param \Magento\Catalog\Model\ProductFactory $productFactory
     * @param \Webkul\Marketplace\Model\SaleslistFactory $saleslistFactory
     * @param ShippingSettingRepository $shippingSettingRepository
     * @param \Webkul\Mppercountryperproductshipping\Helper\Data $currentHelper
     * @param \Webkul\Mppercountryperproductshipping\Block\Adminhtml\RegionRule\Create $regionRule
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Shipping\Model\Rate\ResultFactory $rateResultFactory,
        \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        SessionManager $coreSession,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Directory\Model\CurrencyFactory $currencyFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Locale\FormatInterface $localeFormat,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Framework\App\RequestInterface $requestInterface,
        PriceCurrencyInterface $priceCurrency,
        OptionFactory $optionFactory,
        CustomerFactory $customerFactory,
        AddressFactory $addressFactory,
        \Webkul\Marketplace\Model\ProductFactory $mpProductFactory,
        \Magento\Catalog\Model\ProductFactory $productFactory,
        \Webkul\Marketplace\Model\SaleslistFactory $saleslistFactory,
        ShippingSettingRepository $shippingSettingRepository,
        \Webkul\Mppercountryperproductshipping\Helper\Data $currentHelper,
        \Webkul\Mppercountryperproductshipping\Block\Adminhtml\RegionRule\Create $regionRule,
        \Magento\Framework\Serialize\SerializerInterface $serializer,
        array $data = []
    ) {
        $this->_objectManager = $objectManager;
        $this->_currentHelper = $currentHelper;
        $this->regionRule = $regionRule;
        $this->requestInterface = $requestInterface;
        $this->serializer = $serializer;
        parent::__construct(
            $scopeConfig,
            $rateErrorFactory,
            $logger,
            $rateResultFactory,
            $rateMethodFactory,
            $regionFactory,
            $coreSession,
            $checkoutSession,
            $customerRepository,
            $customerSession,
            $currencyFactory,
            $storeManager,
            $localeFormat,
            $jsonHelper,
            $requestInterface,
            $priceCurrency,
            $optionFactory,
            $customerFactory,
            $addressFactory,
            $mpProductFactory,
            $productFactory,
            $saleslistFactory,
            $shippingSettingRepository,
            $data
        );
    }

    /**
     * Collect and get rates.
     *
     * @param RateRequest $request
     *
     * @return \Magento\Quote\Model\Quote\Address\RateResult\Error|bool|Result
     */
    public function collectRates(\Magento\Quote\Model\Quote\Address\RateRequest $request)
    {
        $this->baseRequest = $request;
        if (!$this->getConfigFlag('active') || $this->isMultiShippingActive()) {
            return false;
        }
        $this->setRequest($request);

        $shippingpricedetail = $this->getShippingPricedetail($this->_rawRequest);
        $result = $this->_rateResultFactory->create();
        if (isset($shippingpricedetail['error']['error']) && $shippingpricedetail['error']['error'] == 1) {
            $error = $this->_rateErrorFactory->create();
            $error->setCarrier($this->_code);
            $error->setCarrierTitle($this->getConfigData('title'));
            $error->setErrorMessage($this->getConfigData('specificerrmsg'));
            $result->append($error);
        } else {
            $rate = $this->_rateMethodFactory->create();
            $rate->setCarrier($this->_code);
            $rate->setCarrierTitle($this->getConfigData('title'));
            $rate->setMethod($this->_code);
            $rate->setMethodTitle($this->getConfigData('method_title'));
            $rate->setCost($shippingpricedetail['handlingfee']);
            $rate->setPrice($shippingpricedetail['handlingfee']);
            $result->append($rate);
        }
        return $result;
    }

    /**
     * Calculate Price For Bundle function
     *
     * @param calculatePriceForBundle $item
     * @param calculatePriceForBundle $price
     * @param calculatePriceForBundle $destCountryId
     * @param calculatePriceForBundle $_product
     * @param calculatePriceForBundle $shipdetail
     * @param calculatePriceForBundle $debugData
     */
    private function calculatePriceForBundle($item, $price, $destCountryId, $_product, $shipdetail, $debugData)
    {
        $totalcountries = array();
        $requestModule = $this->requestInterface->getModuleName();
        $basedOn = $this->getConfigData('shippingappliedon');

        foreach ($item->getChildren() as $child1) {
            if ($item->getProduct()->isVirtual()) {
                continue;
            }
            $product = $this->productFactory->create()->load($child1->getProductId());
            $mpshippingcharge = $this->_currentHelper->getShippingCharges($product->getId());
            $newprice = '';
            $flag = 0;
            $countryPrice = '';
            $regionPrice = '';
            $rulePrice = 0;
            if($mpshippingcharge){
                $totalcountries = explode('/', $mpshippingcharge);
            }
            foreach ($totalcountries as $price) {
                $sepratecountry = explode(',', $price);
                if ($sepratecountry[0] == "C") {
                    if ($destCountryId == $sepratecountry[1]) {
                        $newprice = $countryPrice = $sepratecountry[2];
                        $flag = 1;
                    }
                } elseif ($sepratecountry[0] == "R") {
                    $regionData = $this->getRegionDataByCountryCode($destCountryId);
                    $regionRuleBlock = $this->regionRule;
                    $regionCollectionData = $regionRuleBlock
                        ->getRegionDataByRegionCode($regionData);
                    $regionRuleData = $regionCollectionData->getData();

                    $countryDetails = $this->_currentHelper
                        ->getAllowedCountriesFromCollection($regionRuleData);
                    foreach ($countryDetails as $countryInfo) {
                        if (($countryInfo['country'] == $destCountryId) && ($regionData == $sepratecountry[1])) {
                            $newprice = $regionPrice = $sepratecountry[2];
                            $flag = 1;
                        }
                    }

                    if ($regionData == $sepratecountry[1] && $flag == 0) {
                        $newprice = $regionPrice = $sepratecountry[2];
                        $flag = 1;
                    }
                }
            }

            if ($newprice == '' && $flag == 0) {
                $regionCode = $this->getRegionDataByCountryCode($destCountryId);
                $regionRuleBlock = $this->regionRule;
                $regionCollectionData = $regionRuleBlock->getRegionDataByRegionCode($regionCode);
                $regionData = $regionCollectionData->getData();

                $countryDetails = $this->_currentHelper
                    ->getAllowedCountriesFromCollection($regionData);
                if (!empty($countryDetails)) {
                    foreach ($countryDetails as $countryInfo) {
                        if ($countryInfo['country'] == $destCountryId) {
                            $newprice = $rulePrice = $countryInfo['price'];
                            $flag = 1;
                        }
                    }
                }
            }

            $newprice = ($countryPrice != '') ? $countryPrice : $regionPrice;
            if (($newprice == '' || $newprice <= 0) && $rulePrice >0) {
                $newprice = $rulePrice;
            }

            if ($newprice == '' && $flag == 0) {
                if ($this->getConfigData('default_amount') == '') {
                    $debugData['error'] = 1;
                }
                $newprice = floatval($this->getConfigData('default_amount'));
            }
            $qty = $child1->getQty();
            if ($requestModule == 'multishipping') {
                $qty = $shipdetail['qty'];
            }
            $temp_Price = $temp_Price + (float)$newprice * $qty;
        }
        if ($basedOn == 0) {
            $price = $temp_Price;
        } else {
            $flag = 0;
            $totalQuantity = $item->getQty();
            if ($requestModule == 'multishipping') {
                $totalQuantity = $shipdetail['qty'];
            }
            $mpshippingcharge = $_product->getMpShippingCountryCharge();
            $newprice = '';
            $countryPrice = '';
            $regionPrice = '';
            $rulePrice = 0;
            if($mpshippingcharge){
                $totalcountries = explode('/', $mpshippingcharge);
            }
            foreach ($totalcountries as $pric) {
                $sepratecountry = explode(',', $pric);
                if ($sepratecountry[0] == "C") {
                    if ($destCountryId == $sepratecountry[1]) {
                        $newprice = $countryPrice = $sepratecountry[2];
                        $flag = 1;
                    }
                } elseif ($sepratecountry[0] == "R") {
                    $regionData = $this->getRegionDataByCountryCode($destCountryId);
                    $regionRuleBlock = $this->regionRule;
                    $regionCollectionData = $regionRuleBlock->getRegionDataByRegionCode($regionData);
                    $regionRuleData = $regionCollectionData->getData();

                    $countryDetails = $this->_currentHelper
                        ->getAllowedCountriesFromCollection($regionRuleData);
                    foreach ($countryDetails as $countryInfo) {
                        if (($countryInfo['country'] == $destCountryId) && ($regionData == $sepratecountry[1])) {
                            $newprice = $regionPrice = $sepratecountry[2];
                            $flag = 1;
                        }
                    }

                    if ($regionData == $sepratecountry[1] && $flag == 0) {
                        $newprice = $regionPrice = $sepratecountry[2];
                        $flag = 1;
                    }
                }
            }

            if ($newprice == '' && $flag == 0) {
                $regionCode = $this->getRegionDataByCountryCode($destCountryId);
                $regionRuleBlock = $this->regionRule;
                $regionCollectionData = $regionRuleBlock->getRegionDataByRegionCode($regionCode);
                $regionData = $regionCollectionData->getData();

                $countryDetails = $this->_currentHelper
                    ->getAllowedCountriesFromCollection($regionData);
                if (!empty($countryDetails)) {
                    foreach ($countryDetails as $countryInfo) {
                        if ($countryInfo['country'] == $destCountryId) {
                            $newprice = $rulePrice = $countryInfo['price'];
                            $flag = 1;
                        }
                    }
                }
            }

            $newprice = ($countryPrice != '') ? $countryPrice : $regionPrice;
            if (($newprice == '' || $newprice <= 0) && $rulePrice >0) {
                $newprice = $rulePrice;
            }

            if ($newprice == '' && $flag == 0) {
                if ($this->getConfigData('default_amount') == '') {
                    $debugData['error'] = 1;
                }
                $temp_Price =  (floatval($this->getConfigData('default_amount')) * $totalQuantity);
            } else {
                $temp_Price =  ($newprice * $totalQuantity);
            }
            $price = $temp_Price;
        }

        return $price;
    }

    /**
     * Calculate Price For Other function
     *
     * @param calculatePriceForOther $item
     * @param calculatePriceForOther $price
     * @param calculatePriceForOther $destCountryId
     * @param calculatePriceForOther $_product
     * @param calculatePriceForOther $shipdetail
     * @param calculatePriceForOther $debugData
     */
    private function calculatePriceForOther($item, $price, $destCountryId, $_product, $shipdetail, $debugData)
    {
        $totalcountries = array();
        $assignProductId = $this->_checkAssignProduct($item);
        $requestModule = $this->requestInterface->getModuleName();
        $basedOn = $this->getConfigData('shippingappliedon');
        $productId = $_product->getId();
        $flag = 0;
        if ($_product->getTypeId() == 'configurable' && $basedOn == 0) {
            if ($option = $item->getOptionByCode('simple_product')) {
                $productId = $option->getProduct()->getId();
            }
        }
        if ($assignProductId) {
            $mpassignModel = $this->_objectManager
                ->create(\Webkul\MpAssignProduct\Model\Items::class)
                ->load($assignProductId);

            if (!empty($mpassignModel)) {
                $mpshippingcharge = $mpassignModel->getData('shipping_country_charge');
            }
        } else {
            $mpshippingcharge = $this->_currentHelper->getShippingCharges($productId);
        }

        $newprice = '';
        $countryPrice = '';
        $regionPrice = '';
        $rulePrice = 0;
        if($mpshippingcharge){
            $totalcountries = explode('/', $mpshippingcharge);
        }
        foreach ($totalcountries as $pric) {
            $sepratecountry = explode(',', $pric);
            if ($sepratecountry[0] == "C") {
                if ($destCountryId == $sepratecountry[1]) {
                    $newprice = $countryPrice = $sepratecountry[2];
                    $flag = 1;
                }
            } elseif ($sepratecountry[0] == "R") {
                $regionData = $this->getRegionDataByCountryCode($destCountryId);
                $regionRuleBlock = $this->regionRule;
                $regionCollectionData = $regionRuleBlock->getRegionDataByRegionCode($regionData);
                $regionRuleData = $regionCollectionData->getData();

                if (!empty($regionRuleData)) {
                    $countryDetails = $this->_currentHelper
                        ->getAllowedCountriesFromCollection($regionRuleData);

                    foreach ($countryDetails as $countryInfo) {
                        if (($countryInfo['country'] == $destCountryId) && ($regionData == $sepratecountry[1])) {
                            $newprice = $regionPrice = $sepratecountry[2];
                            $flag = 1;
                        }
                    }
                }

                if ($regionData == $sepratecountry[1] && $flag == 0) {
                    $newprice = $regionPrice = $sepratecountry[2];
                    $flag = 1;
                }
            }
        }

        if ($newprice == '' && $flag == 0) {
            $regionCode = $this->getRegionDataByCountryCode($destCountryId);
            $regionRuleBlock = $this->regionRule;
            $regionCollectionData = $regionRuleBlock->getRegionDataByRegionCode($regionCode);
            $regionData = $regionCollectionData->getData();
            $countryDetails = $this->_currentHelper
                ->getAllowedCountriesFromCollection($regionData);

            if (!empty($countryDetails)) {
                foreach ($countryDetails as $countryInfo) {
                    if ($countryInfo['country'] == $destCountryId) {
                        $newprice = $rulePrice = $countryInfo['price'];
                        $flag = 1;
                    }
                }
            }
        }

        $newprice = ($countryPrice != '') ? $countryPrice : $regionPrice;
        if (($newprice == '' || $newprice <= 0) && $rulePrice > 0) {
            $newprice = $rulePrice;
        }

        $qty = $item->getQty();
        if ($requestModule == 'multishipping') {
            $qty = $shipdetail['qty'];
        }
        if ($newprice == '' && $flag == 0) {
            if ($this->getConfigData('default_amount') == '') {
                $debugData['error'] = 1;
            }
            $price = $price + (floatval($this->getConfigData('default_amount')) * $qty);
        } else {
            $price = $price + ($newprice * $qty);
        }

        return $price;
    }

    /**
     * Calculate the rate according to per country per product.
     *
     * @param RateRequest $request
     * @return void
     */
    public function getShippingPricedetail(RateRequest $request)
    {
        $r = $request;
        $submethod = $debugData = [];
        $shippinginfo = [];
        $handling = 0;
        $temp_Price = 0;
        $allItems = $request->getAllItems();

        foreach ($r->getShippingDetails() as $shipdetail) {
            $requestModule = $this->requestInterface->getModuleName();
            $price = 0;
            $itemsArray = array();
            if($shipdetail['item_id']){
                $itemsArray = explode(',', $shipdetail['item_id']);
            }

            if (count($allItems) == 0) {
                $allItems = $r->getAllItems();
            }
            foreach ($allItems as $item) {
                if (!in_array($item->getId(), $itemsArray)) {
                    continue;
                }
                $productData = [];
                $flag = 0;
                if ($item->getProduct()->isVirtual() || $item->getParentItem()) {
                    continue;
                }

                $assignProductId = $this->_checkAssignProduct($item);
                $productId = $item->getProductId();
                $_product = $this->productFactory->create()->load($item->getProductId());
                $basedOn = $this->getConfigData('shippingappliedon');
                if ($_product->getTypeId() == 'bundle') {
                    $price = $this->calculatePriceForBundle(
                        $item,
                        $price,
                        $r->getDestCountryId(),
                        $_product,
                        $shipdetail,
                        $debugData
                    );
                } else {
                    $price = $this->calculatePriceForOther(
                        $item,
                        $price,
                        $r->getDestCountryId(),
                        $_product,
                        $shipdetail,
                        $debugData
                    );
                }
            }
            $handling = $handling + $price;
            $submethod = [
                $this->_code => [
                    'method' => $this->getConfigData('title'),
                    'cost' => $price,
                    'base_amount' => $price,
                    'error' => 0
                ]
            ];
            array_push(
                $shippinginfo,
                [
                    'seller_id' => $shipdetail['seller_id'],
                    'methodcode' => $this->_code,
                    'shipping_ammount' => $price,
                    'product_name' => $shipdetail['product_name'],
                    'submethod' => $submethod,
                    'item_ids' => $shipdetail['item_id']
                ]
            );
        }

        $debugData['result'] = $shippinginfo;
        $result = ['handlingfee' => $handling, 'shippinginfo' => $shippinginfo, 'error' => $debugData];
        $shippingAll = $this->_coreSession->getShippingInfo();
        $shippingAll[$this->_code] = $result['shippinginfo'];
        $this->setShippingInformation($shippingAll);
        return $result;
    }

    /**
     * Get Allowed Methods function
     *
     * @return array
     */
    public function getAllowedMethods()
    {
        return [$this->_code => $this->getConfigData('name')];
    }

    /**
     * Load Model function
     *
     * @param _loadModel $id
     * @param _loadModel $model
     */
    protected function _loadModel($id, $model)
    {
        return $this->_objectManager->create($model)->load($id);
    }

    /**
     * Get Region Data By Country Code function
     *
     * @param getRegionDataByCountryCode $countryCode
     */
    public function getRegionDataByCountryCode($countryCode)
    {
        $regionRuleBlock = $this->regionRule;
        $countryWithRegionData = $regionRuleBlock->getAllCountryWithRegion();

        $currentRegion = [];
        $currentRegionCode = '';
        $countries = \Zend_Locale::getTranslationList('Territory', 'en_US', 2); // For translation
        foreach ($countryWithRegionData as $data) {
            if ($data['country'] == $countries[$countryCode]) {
                $currentRegion = $data;
                break;
            }
        }
        if (!empty($currentRegion['continent'])) {
            $allRegions = $regionRuleBlock->getRegion()['region'];
            foreach ($allRegions as $region) {
                if ($region['label'] == $currentRegion['continent']) {
                    $currentRegionCode = $region['value'];
                    break;
                }
            }
        }
        return $currentRegionCode;
    }

    /**
     * Check if carrier has shipping label option available
     *
     * @return bool
     */
    public function isShippingLabelsAvailable()
    {
        return false;
    }

    /**
     * Check Assign Product function
     *
     * @param _checkAssignProduct $item
     */
    protected function _checkAssignProduct($item)
    {
        $mpassignproductId = 0;
        $itemOptionFactory = $this->quoteOptionFactory->create();
        $itemOption = $itemOptionFactory->getCollection()
            ->addFieldToFilter('product_id', ['eq' => $item->getProductId()])
            ->addFieldToFilter('code', ['eq' => 'info_buyRequest'])
            ->getFirstItem();

        $optionValue = $itemOption->getId() ? $itemOption->getValue() : '';

        if ($optionValue !== '') {
            $temp = [];
            if ($this->_validJson($optionValue)) {
                $temp = json_decode($optionValue, true);
            } else {
                $temp = $this->serializer->unserialize($optionValue);
            }
            $mpassignproductId = isset($temp['mpassignproduct_id']) ? $temp['mpassignproduct_id'] : 0;
        }

        return $mpassignproductId;
    }

    /**
     * Validates JSON
     *
     * @param _validJson $string
     * @return json
     */
    private function _validJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}
