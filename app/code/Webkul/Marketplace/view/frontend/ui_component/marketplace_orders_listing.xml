<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">marketplace_orders_listing.marketplace_orders_listing_data_source</item>
            <item name="deps" xsi:type="string">marketplace_orders_listing.marketplace_orders_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">orders_columns</item>
    </argument>
    <dataSource name="marketplace_orders_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Webkul\Marketplace\Ui\DataProvider\OrdersHistoryDataProvider</argument>
            <argument name="name" xsi:type="string">marketplace_orders_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="cacheRequests" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                      <item name="saveUrl" xsi:type="url" path="marketplace/mui_bookmark/save"/>
                        <item name="deleteUrl" xsi:type="url" path="marketplace/mui_bookmark/delete"/>
                        <item name="namespace" xsi:type="string">marketplace_orders_listing</item>
                    </item>
                </item>
            </argument>
        </bookmark>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="statefull" xsi:type="array">
                        <item name="applied" xsi:type="boolean">false</item>
                    </item>
                    <item name="params" xsi:type="array">
                        <item name="filters_modifier" xsi:type="array" />
                    </item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">marketplace_orders_listing.marketplace_orders_listing.orders_columns.orderIds</item>
                    <item name="indexField" xsi:type="string">order_id</item>
                </item>
            </argument>
            <action name="massinvoice">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">massinvoice</item>
                        <item name="label" xsi:type="string" translate="true">Print Invoice Slip</item>
                        <item name="url" xsi:type="url" path="marketplace/order_ui/printinvoice"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Print Invoice Slip</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to print invoice slip of selected order(s)?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="massship">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">massship</item>
                        <item name="label" xsi:type="string" translate="true">Print Shipping Slip</item>
                        <item name="url" xsi:type="url" path="marketplace/order_ui/printshipping"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Print Shipping Slip</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to print shipping slip of selected order(s)?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="orders_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">ordersHistoryGrid</item>
                        <item name="target" xsi:type="string">selectOrders</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="orderIds">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">order_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <!--<column name="magerealorder_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">Internal Order#</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>-->
        <column name="seller_order_number">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">Order#</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Purchased On</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
            </argument>
        </column>
        <column name="magepro_name" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\OrderHistoryProDetails">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Products</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>
        <column name="actual_seller_amount" class="Magento\Sales\Ui\Component\Listing\Column\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Base Total</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>
        <column name="purchased_actual_seller_amount" class="Magento\Sales\Ui\Component\Listing\Column\PurchasedPrice">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Purchased Total</item>
                    <item name="sortOrder" xsi:type="number">45</item>
                </item>
            </argument>
        </column>
        <column name="customer_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Customer</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>
        <column name="status">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\OrderListStatus</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/selectLink</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Webkul\Marketplace\Ui\Component\Listing\Columns\OrderviewAction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">order_id</item>
                    <item name="viewUrlPath" xsi:type="string">marketplace/order/view</item>
                    <item name="urlEntityParamName" xsi:type="string">id</item>
                    <item name="label" xsi:type="string" translate="true">View</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
