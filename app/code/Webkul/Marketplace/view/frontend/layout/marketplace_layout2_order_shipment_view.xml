<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="marketplace_styles"/>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">View Shipment Details</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Webkul\Marketplace\Block\Order\View"  name="marketplace_order_shipment_view" template="Webkul_Marketplace::order/shipment/view.phtml" cacheable="false">
                <block class="Webkul\Marketplace\Block\Order\Shipment\Items" name="marketplace_order_shipment_items" template="Webkul_Marketplace::order/shipment/items.phtml">
                    <block class="Magento\Theme\Block\Html\Pager" name="marketplace_order_item_pager"/>
                </block>
            </block>
        </referenceContainer>
        <referenceBlock name="marketplace_order_shipment_view">
            <arguments>
                <argument name="view_model" xsi:type="object">Webkul\Marketplace\ViewModel\HelperViewModel</argument>
                <argument name="view_model_customer_email" xsi:type="object">Comave\MaskedEmail\ViewModel\CustomerEmail</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="marketplace_order_shipment_items">
            <arguments>
                <argument name="view_model" xsi:type="object">Webkul\Marketplace\ViewModel\HelperViewModel</argument>
                <argument name="view_model_customer_email" xsi:type="object">Comave\MaskedEmail\ViewModel\CustomerEmail</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
