<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 *
 * /** @var \Webkul\Marketplace\Block\Marketplace $block
 * /** @var \Webkul\Marketplace\ViewModel\HelperViewModel $viewModel
 */
$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$pageLayout = $helper->getPageLayout();
if ($pageLayout == 1) {
    echo $block->getChildHtml('landingPagelayout1');
} elseif ($pageLayout == 2) {
    echo $block->getChildHtml('landingPagelayout2');
} else {
    echo $block->getChildHtml('landingPagelayout3');
}
