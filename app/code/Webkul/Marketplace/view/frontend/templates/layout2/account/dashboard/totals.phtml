<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var $block \Webkul\Marketplace\Block\Account\Dashboard */
?>
<?php
    $getTopSaleProducts = $block->getTopSaleProducts();
    $totalOrders = $block->getTotalOrders();
    $approvedOrders = $block->getPendingOrders();
    $processOrders = $block->getProcessingOrders();
    $completeOrders = $block->getCompletedOrders();
    $borderApprove = 2;
    $borderProcess = 2;
    $borderComplete = 2;
if ($totalOrders) {
    $approve = ($approvedOrders*100)/$totalOrders;
    $process = ($processOrders*100)/$totalOrders;
    $complete = ($completeOrders*100)/$totalOrders;
} else {
    $approve = 0;
    $process = 0;
    $complete = 0;
}
if (!$approve) {
    $borderApprove = 0;
}
if (!$process) {
    $borderProcess = 0;
}
if (!$complete) {
    $borderComplete = 0;
}
    $totalOrdersK = $totalOrders / 1000;
if ((int)$totalOrdersK) {
    $totalOrdersToDisplay = $totalOrdersK."K";
} else {
    $totalOrdersToDisplay = $totalOrders;
}
    $approvedOrdersK = $approvedOrders / 1000;
if ((int)$approvedOrdersK) {
    $approvedOrdersToDisplay = $approvedOrdersK."K";
} else {
    $approvedOrdersToDisplay = $approvedOrders;
}
    $processOrdersK = $processOrders / 1000;
if ((int)$processOrdersK) {
    $processOrdersToDisplay = $processOrdersK."K";
} else {
    $processOrdersToDisplay = $processOrders;
}
    $completeOrdersK = $completeOrders / 1000;
if ((int)$completeOrdersK) {
    $completeOrdersToDisplay = $completeOrdersK."K";
} else {
    $completeOrdersToDisplay = $completeOrders;
}
    $totalProducts = $block->getTotalProducts();
    $totalProductsK = $totalProducts / 1000;
if ((int)$totalProductsK) {
    $totalProductToDisplay = $totalProductsK."K";
} else {
    $totalProductToDisplay = $totalProducts;
}
    $totalCustomers = $block->getTotalCustomers();
    $totalCustomersK = $totalCustomers / 1000;
if ((int)$totalCustomersK) {
    $totalCustomersToDisplay = $totalCustomersK."K";
} else {
    $totalCustomersToDisplay = $totalCustomers;
}
    $currentMonthCustomers = $block->getTotalCustomersCurrentMonth();
    $currentMonthCustomersK = $currentMonthCustomers / 1000;
if ((int)$currentMonthCustomersK) {
    $currentMonthCustomersToDisplay = $currentMonthCustomersK."K";
} else {
    $currentMonthCustomersToDisplay = $currentMonthCustomers;
}
    $lastMonthCustomers = $block->getTotalCustomersLastMonth();
    $lastMonthCustomersK = $lastMonthCustomers / 1000;
if ((int)$lastMonthCustomersK) {
    $lastMonthCustomersToDisplay = $lastMonthCustomersK."K";
} else {
    $lastMonthCustomersToDisplay = $lastMonthCustomers;
}
?>
<div class="wk-mp-dashboard-total-container">
    <div class="wk-mp-dashboard-total wk-mp-dashboard-total-order wk-mp-dashboard-report-block">
        <div class="wk-mp-dashboard-border">
            <div class="wk-mp-dashboard-total-left">
                <div class="wk-dashboard-total-left-bottom">
                    <?= /* @noEscape */ __('Order(s)') ?>
                </div>
                <div class="wk-dashboard-sales-value wk-dashboard-sales-font">
                    <span class="price">
                        <?=/* @noEscape */ $totalOrdersToDisplay ?>
                    </span>
                </div>
            </div>
            <div class="wk-mp-dashboard-total-right">
                <div class="wk-mp-dashboard-total-progress-bar">
                    <small>
                        <span class="wk-mp-float-left">
                            <?= /* @noEscape */ __('Order to be approved') ?>
                        </span>
                        <span class="wk-mp-float-right">
                            <?= /* @noEscape */ $approvedOrdersToDisplay ?>
                        </span>
                        <span
                            class="wk-mp-progress-color-bar wk-mp-approve-color-bar"
                            style="width:<?= /* @noEscape */ $approve; ?>%;
                             border-width:<?= /* @noEscape */ $borderApprove?>px;"></span>
                    </small>
                </div>
                <div class="wk-mp-dashboard-total-progress-bar">
                    <small>
                        <span class="wk-mp-float-left">
                            <?= /* @noEscape */ __('Order to be shipped') ?>
                        </span>
                        <span class="wk-mp-float-right">
                            <?= /* @noEscape */ $processOrdersToDisplay ?>
                        </span>
                        <span
                            class="wk-mp-progress-color-bar wk-mp-process-color-bar"
                            style="width:<?= /* @noEscape */ $process; ?>%;
                             border-width:<?= /* @noEscape */ $borderProcess?>px;"></span>
                    </small>
                </div>
                <div class="wk-mp-dashboard-total-progress-bar">
                    <small>
                        <span class="wk-mp-float-left">
                            <?= /* @noEscape */ __('Complete') ?>
                        </span>
                        <span class="wk-mp-float-right">
                            <?= /* @noEscape */ $completeOrdersToDisplay ?>
                        </span>
                        <span
                            class="wk-mp-progress-color-bar wk-mp-complete-color-bar"
                            style="width:<?= /* @noEscape */ $complete; ?>%;
                            border-width:<?= /* @noEscape */ $borderComplete?>px;"></span>
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="wk-mp-dashboard-total wk-mp-dashboard-total-product wk-mp-dashboard-report-block">
        <div class="wk-mp-dashboard-border">
            <div class="wk-mp-dashboard-total-left">
                <span class="wk-dashboard-total-left-bottom">
                    <?= /* @noEscape */ __('Product(s)') ?>
                </span>
                <strong class="wk-dashboard-sales-value wk-dashboard-sales-font">
                    <span class="price">
                        <?= /* @noEscape */ $totalProductToDisplay ?>
                    </span>
                </strong>
            </div>
            <div class="wk-mp-dashboard-total-right">
                <div class="wk-dashboard-products-top wk-dashboard-sales-value">
                    <small><?= /* @noEscape */ __('Top Selling Products') ?></small>
                </div>
                <small class="wk-mp-products-bottom wk-mp-reports-products-list">
                    <ul>
                        <?php foreach ($getTopSaleProducts as $key => $value) { ?>
                            <li>
                                <span class="wk-mp-float-left">
                                    <?php if(($value['url'])): ?>
                                        <a href="<?= $escaper->escapeUrl($value['url'])?>">
                                    <?php endif; ?>
                                    <?= $escaper->escapeHtml($value['name'])?></a>
                                </span>
                                <span class="wk-mp-float-right">
                                    <?= /* @noEscape */ __('%1 Sales', $value['qty']) ?>
                                </span>
                            </li>
                        <?php } ?>
                    </ul>
                </small>
            </div>
        </div>
    </div>
    <div class="wk-mp-dashboard-total wk-mp-dashboard-total-customer wk-mp-dashboard-report-block">
        <div class="wk-mp-dashboard-border">
            <div class="wk-mp-dashboard-total-left">
                <span class="wk-dashboard-total-left-bottom">
                    <?= /* @noEscape */ __('Customer(s)') ?>
                </span>
                <strong class="wk-dashboard-sales-value wk-dashboard-sales-font">
                    <span class="price">
                        <?= /* @noEscape */ $totalCustomersToDisplay; ?>
                    </span>
                </strong>
            </div>
            <div class="wk-mp-dashboard-total-right">
                <div class="wk-mp-dashboard-customers-top">
                    <small>
                        <?= /* @noEscape */ __('This month customer count') ?>
                            : <strong><?= /* @noEscape */ $currentMonthCustomersToDisplay; ?></strong>
                    </small>
                </div>
                <div class="wk-mp-dashboard-customers-bottom">
                    <small>
                        <?= /* @noEscape */ __('Last month customer count') ?>
                        : <strong><?= /* @noEscape */ $lastMonthCustomersToDisplay; ?></strong>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
