<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var $block \Webkul\Marketplace\Block\Product\Create */
/** @var $escaper \Magento\Framework\Escaper */
$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$allowedSets = $helper->getAllowedSets();
$allowedProducts = $helper->getAllowedProductTypes();
$totalSets = count($allowedSets);
$totalProductTypes = count($allowedProducts);

$blockName = $block->getLayout()->createBlock('Comave\Club\Block\Banner');
$website = $blockName->getWebName();
?>
<?php if (!$helper->isProfileCompleted() && $helper->getSellerProfileDisplayFlag()): ?>
    <div class="wk-profile-alert">
        <strong><?= $escaper->escapeHtml(__('Warning'))?>!</strong>
        <span><?= $escaper->escapeHtml(__('Please Complete'))?></span>
        <a href="<?= $escaper->escapeUrl($block
        ->getUrl('marketplace/account/editprofile', ['_secure' => $block->getRequest()->isSecure()]))?>"
        style="color: inherit;">
            <?= $escaper->escapeHtml(__('Your Profile'))?>
        </a>
        <span class="wk-close">X</span>
    </div>
<?php endif; ?>
<form action="<?= $escaper->escapeUrl($block->getUrl('marketplace/product/create', ['_secure' => $block
->getRequest()->isSecure()])) ?>" enctype="multipart/form-data"
method="post" id="form-customer-product-new" data-mage-init='{"validation":{}}'>
    <div class="wk-mp-design">
        <fieldset class="fieldset info wk-mp-fieldset">
            <legend class="legend">
                <span><?= $escaper->escapeHtml(__('Add New Product')) ?></span>
                <button class="button wk-mp-btn" title="Continue" type="submit">
                    <span><span><?= $escaper->escapeHtml(__('Continue')) ?></span></span>
                </button>
            </legend>
            <?= $block->getBlockHtml('formkey')?>
            <?= $block->getBlockHtml('seller.formkey')?>
            <?php if ($totalSets > 0): ?>
                <?php if ($totalSets > 1): ?>
                    <div class="field required">
                        <label class="label"><?= $escaper->escapeHtml(__('Attribute Set')) ?>:</label>
                        <div class="tooltip">
                            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select attribute set for product ')) ?></span>
                        </div>
                        <div class="control">
                            <select name="set" class="required-entry">
                            <?php foreach ($allowedSets as $set): ?>
                                <option value="<?= $escaper->escapeHtml($set['value']) ?>">
                                <?= $escaper->escapeHtml($set['label'])?></option>
                            <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                <?php else: ?>
                    <input type="hidden" name="set" value="<?= /* @noEscape */ $allowedSets[0]['value'] ?>" />
                <?php endif; ?>
            <?php endif; ?>
            <?php if ($totalProductTypes > 0): ?>
                <?php if ($totalProductTypes > 1): ?>
                    <div class="field required">
                        <label class="label"><?= $escaper->escapeHtml(__('Product Type')) ?>:</label>
                        <div class="tooltip">
                            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select product type to create product ')) ?></span>
                        </div>
                        <div class="control">
                            <select name="type" class="required-entry">
                            <?php foreach ($allowedProducts as $type): ?>
                                <option value="<?= $escaper->escapeHtml($type['value']) ?>">
                                <?= $escaper->escapeHtml($type['label'])?></option>
                            <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                <?php else: ?>
                    <input type="hidden" name="type" value="<?= /* @noEscape */ $allowedProducts[0]['value'] ?>" />
                <?php endif; ?>
            <?php endif; ?>
            <?= $block->getChildHtml(); ?>
        </fieldset>
    </div>
</form>
<div class="buttons-set">
    <p class="required">* <?= $escaper->escapeHtml(__('Required Fields')) ?></p>
    <p class="back-link">
        <a href="javascript:;" onclick="javascript: window.history.back();" class="left">&laquo;
        <?= $escaper->escapeHtml(__('Back')) ?></a>
    </p>
</div>
<script type='text/javascript'>
    require(['jquery'], function($) {
      $('.wk-close').click(function(e) {
        $('.wk-profile-alert').hide();
      });
    })
</script>
