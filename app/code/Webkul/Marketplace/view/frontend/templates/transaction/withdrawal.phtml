<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var $block \Webkul\Marketplace\Block\Transaction\History */
?>
<div class="wk-mp-design">
    <div class="wk-mp-tr-amount-wrapper">
        <div class="wk-mp-tr-amount-total">
            <div class="wk-mp-tr-amount-total-title">
                <div class="wk-mp-tr-txt-style"><?= $block->escapeHtml(__('Statement'))?></div>
                <div class="wk-mp-tr-amount-style">
                    <?= /* @noEscape */ $block->getFormatedPrice($block->getTotalSellerSale());?>
                </div>
                <div class="wk-mp-tr-last-style">
                    <?= $block->escapeHtml(__('Total Seller earning'))?>
                </div>
                <div class="wk-mp-tr-last-style">
                    (<?= $block->escapeHtml(__('base currency'))?>)
                </div>
            </div>
            <div class="wk-mp-tr-amount-total-desc">
                <div>
                    <div class="wk-mp-tr-amount-style">
                        <?= /* @noEscape */ "+ ".$block->getFormatedPrice($block->getTotalSale());?>
                    </div>
                    <div class="wk-mp-tr-last-style">
                        <?= $block->escapeHtml(__('Total Sale'))?>
                    </div>
                </div>
                <div>
                    <div class="wk-mp-tr-amount-style">
                        <?= /* @noEscape */ "- ".$block->getFormatedPrice($block->getTotalTax());?>
                    </div>
                    <div class="wk-mp-tr-last-style">
                        <?= $block->escapeHtml(__('Tax'))?>
                    </div>
                </div>
                <div>
                    <div class="wk-mp-tr-amount-style">
                        <?= /* @noEscape */ "- ".$block->getFormatedPrice($block->getTotalCommission());?>
                    </div>
                    <div class="wk-mp-tr-last-style">
                        <?= $block->escapeHtml(__('Commission'))?>
                    </div>
                </div>
            </div>
        </div>
        <div class="wk-mp-tr-payout-total">
            <center>
                <div class="wk-mp-tr-payout-style">
                    <?= /* @noEscape */ $block->getFormatedPrice($block->getTotalPayout());?>
                </div>
                <div class="wk-mp-tr-last-style">
                    <?= $block->escapeHtml(__('Total Payout'))?>
                </div>
                <div class="wk-mp-tr-last-style">
                    (<?= $block->escapeHtml(__('base currency'))?>)
                </div>
            </center>
        </div>
        <div class="wk-mp-tr-remain-total">
            <form action="<?= $block->escapeUrl($block->getUrl('marketplace/withdrawal/request'));?>" method="post">
                <?= $block->getBlockHtml('formkey')?>
                <?= $block->getBlockHtml('seller.formkey')?>
                <input type="hidden" name="is_requested" value="1"/>
                <div class="wk-mp-tr-remain-title">
                    <div class="wk-mp-tr-txt-style"><?= $block->escapeHtml(__('Withdrawal'))?></div>
                    <div class="wk-mp-tr-amount-style">
                        <?= /* @noEscape */ $block->getFormatedPrice($block->getRemainTotal());?>
                    </div>
                    <div class="wk-mp-tr-last-style">
                        <?= $block->escapeHtml(__('Remaining Payout'))?>
                    </div>
                    <div class="wk-mp-tr-last-style">
                        (<?= $block->escapeHtml(__('base currency'))?>)
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
