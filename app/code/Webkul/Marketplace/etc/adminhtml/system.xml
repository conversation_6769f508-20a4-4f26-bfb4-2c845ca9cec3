<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="webkul" translate="label" sortOrder="3000" class="wk-config-tab-class">
            <label>Webkul</label>
        </tab>
        <section id="marketplace" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Marketplace</label>
            <!-- Assign section to tab to extension -->
            <tab>webkul</tab>
            <resource>Webkul_Marketplace::config_marketplace</resource>
            <!-- create group for fields in section -->
            <group id="general_settings" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="admin_name" translate="label comment" sortOrder="0" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin Name</label>
                    <validate>required-entry</validate>
                    <comment>It will be used in marketplace transactional emails and other places where admin name is required.</comment>
                </field>
                <field id="adminemail" translate="label comment" sortOrder="1" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin Email-id</label>
                    <validate>validate-email</validate>
                    <comment>It will be used in email templates and other places where admin email is required. If empty then support email will be used.</comment>
                </field>
                <field id="percent" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Global Commission Rate</label>
                    <validate>required-entry validate-number validate-digits-range digits-range-0-100</validate>
                    <comment>Should be integer value like 20</comment>
                </field>
                <field id="deduct_discount" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Deduct Discount Amount From</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\CommissionWithDiscount</source_model>
                    <comment>If discount amount applies in a order then commission will be calculated on the basis of selected option.</comment>
                </field>
                <field id="order_manage" translate="label comment" sortOrder="4" type="select" showInDefault="1">
                    <label>Allow Seller to Manage Orders</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="tax_manage" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Move Product Tax to Seller Account</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="seller_approval" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Approval Required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="captcha" translate="label comment" sortOrder="6" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Set Captcha Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>captcha will be visible on contact seller/admin popup.</comment>
                </field>
                <field id="google_analytic" translate="label" sortOrder="7" type="select" showInDefault="1" showInWebsite="1">
                    <label>Allow Seller For Google Analytic</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="layout_settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Dashboard Layout Settings</label>
                <field id="is_separate_panel" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow seller separate dashboard</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="logo" translate="label" type="image" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller dashboard logo image</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size with same width and height ratio. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/logo</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/logo</upload_dir>
                    <depends>
                        <field id="layout_settings">1</field>
                    </depends>
                </field>
            </group>
            <group id="seller_flag" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Flags</label>
                <field id="status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="seller_flag_label" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Text</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                </field>
                <field id="guest_status" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Guests Can Flag</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                </field>
                <field id="reason" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reason</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\Reasonstatus</source_model>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                    <comment>If this field is set to 'Yes, Required', make sure some reasons are created and enabled, First 5 reasons will show on flag creation page.</comment>
                </field>
                <field id="other_reason" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Accept Other Reasons</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="status">1</field>
                        <field id="reason">1</field>
                    </depends>
                </field>
                <field id="other_reason_label" translate="label comment" sortOrder="6" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Other Field's Placeholder</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="status">1</field>
                        <field id="other_reason">1</field>
                        <field id="reason">1</field>
                    </depends>
                </field>
            </group>
            <group id="product_settings" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Product's Settings</label>
                <field id="attributesetid" translate="label comment" sortOrder="1" type="multiselect" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Attribute Set ID</label>
                    <validate>required-entry</validate>
                    <source_model>Magento\Catalog\Model\Product\AttributeSet\Options</source_model>
                </field>
                <field id="product_approval" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Approval Required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="product_edit_approval" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Update Approval Required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="is_admin_view_category_tree" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Category tree view like admin product category tree</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="categoryids" translate="label comment" sortOrder="5" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allowed Categories for seller to add products</label>
                    <comment>e.g Please enter category ids by comma(,) separated</comment>
                </field>
                <field id="allow_for_seller" translate="label comment" sortOrder="8" type="multiselect" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Type For Seller</label>
                    <validate>required-entry</validate>
                    <source_model>Webkul\Marketplace\Model\Config\Source\Producttype</source_model>
                </field>
                <field id="sku_type" translate="label comment" sortOrder="9" type="select" showInDefault="1">
                    <label>Allow seller to add products with sku type</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\SkuType</source_model>
                </field>
                <field id="sku_prefix" translate="label comment" sortOrder="10" type="text" showInDefault="1">
                    <label>Product Sku Prefix</label>
                    <depends>
                        <field id="sku_type">dynamic</field>
                    </depends>
                    <comment>e.g if sku prefix = "xyz" then your product sku will be as xyz-productsku</comment>
                </field>
                <field id="allow_related_product" translate="label comment" sortOrder="11" type="select" showInDefault="1">
                    <label>Allow Seller to Add Related Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_upsell_product" translate="label comment" sortOrder="12" type="select" showInDefault="1">
                    <label>Allow Seller to Add Up-Sell Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_crosssell_product" translate="label comment" sortOrder="13" type="select" showInDefault="1">
                    <label>Allow Seller to Add Cross-Sell Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_product_limit" translate="label comment" sortOrder="14" type="select" showInDefault="1">
                    <label>Allow Seller to Add Limit on Product Purchase for Customer</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="global_product_limit" translate="label comment" sortOrder="15" type="text" showInDefault="1">
                    <label>Allowed Product Qty on Product Purchase for Customer</label>
                    <validate>validate-digits</validate>
                    <depends>
                        <field id="allow_product_limit">1</field>
                    </depends>
                    <comment>e.g if set 5 then customer will allowed to add maximum 5 qty of the products in cart.</comment>
                </field>
            </group>
            <group id="product_flag" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Product Flags</label>
                <field id="status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="product_flag_label" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Text</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                </field>
                <field id="guest_status" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Guests Can Flag</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                </field>
                <field id="reason" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reason</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\Reasonstatus</source_model>
                    <depends>
                        <field id="status">1</field>
                    </depends>
                    <comment>If this field is set to 'Yes, Required', make sure some reasons are created and enabled, First 5 reasons will show on flag creation page.</comment>
                </field>
                <field id="other_reason" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Accept Other Reasons</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="status">1</field>
                        <field id="reason">1</field>
                    </depends>
                </field>
                <field id="other_reason_label" translate="label comment" sortOrder="6" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Other Field's Placeholder</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="status">1</field>
                        <field id="other_reason">1</field>
                        <field id="reason">1</field>
                    </depends>
                </field>
            </group>
            <group id="order_settings" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1">
                <label>Seller Order's Settings</label>
                <field id="order_approval" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1">
                    <label>Order Approval Required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="min_order_settings" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Minimum Order Amount Settings</label>
                <field id="min_order_status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="amount" translate="label comment" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Minimum Amount</label>
                    <validate>validate-number required-entry validate-greater-than-zero</validate>
                    <depends>
                        <field id="min_order_status">1</field>
                    </depends>
                </field>
                <field id="for_seller" translate="label comment" sortOrder="6" type="select" showInDefault="1" showInWebsite="1">
                    <label>Amount value for seller</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>if this value is yes then amount value will be used for those sellers who have not filled the minimum order amount.</comment>
                    <depends>
                        <field id="min_order_status">1</field>
                    </depends>
                </field>
            </group>
            <group id="inventory_settings" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Inventory Settings</label>
                <field id="low_stock_notification" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Low Notification</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Allow to send low stock notification mail to sellers</comment>
                </field>
                <field id="low_stock_amount" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Low Stock Quantity</label>
                    <comment>Allow to send low stock notification mail to sellers when product quantity will be equal or less then this quantity</comment>
                    <validate>validate-number required-entry integer validate-greater-than-zero</validate>
                    <depends>
                        <field id="low_stock_notification">1</field>
                    </depends>
                </field>
            </group>
            <group id="profile_settings" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Profile Page Settings</label>
                <field id="seller_profile_display" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Seller Profile</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="banner" translate="label" type="image" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Image</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png.</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">avatar</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">avatar</upload_dir>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                </field>
                <field id="card_type" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Information Display Card Type</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\CardType</source_model>
                    <comment>It will be visible on Product Page</comment>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                </field>
                <field id="activecolorpicker" translate="label comment" sortOrder="20" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Color Picker</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                    <comment>Allow sellers to change their profile page background color</comment>
                </field>
                <field id="seller_policy_approval" translate="label comment" sortOrder="30" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Policies Enable at frontend</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                </field>
                <field id="url_rewrite" translate="label comment" sortOrder="40" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Rewrite Seller's Shop URL</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                    <comment>Allow sellers to manage their shop URL</comment>
                </field>
                <field id="auto_url_rewrite" translate="label comment" sortOrder="50" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow to automatic create seller public url on seller registration</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Automatic create seller public url as profile url - yourwebsite.com/seller-shop-url, collection url - yourwebsite.com/seller-shop-url/collection, location url - yourwebsite.com/seller-shop-url/location, feedback url - yourwebsite.com/seller-shop-url/feedback on seller registration</comment>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                </field>
                <field id="vendor_featured" translate="label comment" sortOrder="51" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow to show featured sellers</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                    <comment>Allow to show the featured sellers on frontend</comment>
                </field>
            </group>
            <group id="review_settings" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Marketplace Seller Review Settings</label>
                <field id="review_status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Make a Review on only Order Purchase</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="seller_profile_display">1</field>
                    </depends>
                    <comment>Allow only authorize buyers to review for seller</comment>
                </field>
            </group>
            <group id="landingpage_settings" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Landing Page Settings</label>
                <field id="marketplacelabel" translate="label comment" sortOrder="1" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Head Title</label>
                    <comment>e.g Turn Your Passion Into a Business</comment>
                </field>
                <field id="pageLayout" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Layout</label>
                    <source_model>Webkul\Marketplace\Model\Config\Source\LandingPageLayout</source_model>
                    <comment>e.g Select Layout Which will be seen at frontend</comment>
                </field>
                <!-- Layout 1 fields -->
                <field id="displaybanner" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Banner</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="banner" translate="label" type="image" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Image</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 1200x500 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/banner</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/banner</upload_dir>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="bannercontent" translate="label comment" sortOrder="4" type="editor" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Content</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field</frontend_model>
                    <comment>This content will be display over the banner image in the marketplace landing page url-www.yourwebsite.com/marketplace</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplacelabel1" translate="label comment" sortOrder="5" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label1</label>
                    <comment>e.g Turn Your Passion Into a Business (will be display above all feature's icons)</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplacelabel2" translate="label comment" sortOrder="6" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label2</label>
                    <comment>e.g Sellers with Taste (will be display above top 4 seller's block)</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplacelabel3" translate="label comment" sortOrder="7" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label3</label>
                    <comment>e.g More than 500 shop owners have joined us last month (will be display above "View All Sellers" button)</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplacelabel4" translate="label comment" sortOrder="8" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label4</label>
                    <comment>e.g Why to sell with us (will be display above marketplace content)</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplacebutton" translate="label comment" sortOrder="9" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Button Label</label>
                    <comment>e.g Open a Marketplace Shop - this will also display over banner</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="displayicons" translate="label comment" sortOrder="10" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Icons</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon1" translate="label" type="image" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:1 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 180x180 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon1_label" translate="label comment" sortOrder="12" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:1 Icon Label</label>
                    <comment>e.g Register Yourself</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon2" translate="label" type="image" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:2 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 180x180 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon2_label" translate="label comment" sortOrder="14" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:2 Icon Label</label>
                    <comment>e.g Add Products</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon3" translate="label" type="image" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:3 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 180x180 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon3_label" translate="label comment" sortOrder="16" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:3 Icon Label</label>
                    <comment>e.g Start Selling</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon4" translate="label" type="image" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:4 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 180x180 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="feature_icon4_label" translate="label comment" sortOrder="18" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:4 Icon Label</label>
                    <comment>e.g Generate Revenues</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="marketplaceprofile" translate="label comment" sortOrder="19" type="editor" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>About Marketplace</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field</frontend_model>
                    <comment>e.g webkul</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="sellerlisttop" translate="label comment" sortOrder="20" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Seller List Page Top Label</label>
                    <comment>e.g webkul</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <field id="sellerlistbottom" translate="label comment" sortOrder="20" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Seller List Page Bottom Label</label>
                    <comment>e.g webkul</comment>
                    <depends>
                        <field id="pageLayout">1</field>
                    </depends>
                </field>
                <!-- Layout 2 fields -->
                <field id="displaybannerLayout2" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Banner</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="pageLayout">2</field>
                    </depends>
                </field>
                <field id="bannerLayout2" translate="label" type="image" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Image</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 1500x600 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/banner</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/banner</upload_dir>
                    <depends>
                        <field id="pageLayout">2</field>
                    </depends>
                </field>
                <field id="bannercontentLayout2" translate="label comment" sortOrder="4" type="editor" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Content</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field</frontend_model>
                    <comment>This content will be display over the banner image in the marketplace landing page url-www.yourwebsite.com/marketplace</comment>
                    <depends>
                        <field id="pageLayout">2</field>
                    </depends>
                </field>
                <field id="marketplacebuttonLayout2" translate="label comment" sortOrder="5" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Layout2 Button Label</label>
                    <comment>e.g Open your shop - this will also display over banner button</comment>
                    <depends>
                        <field id="pageLayout">2</field>
                    </depends>
                </field>
                <field id="termConditionLinkLayout2" translate="label comment" sortOrder="6" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Terms and Conditions URL</label>
                    <comment>e.g Terms and Conditions link(cms page link) where customer will see all the terms of becoming seller, url will be look like - http://www.yourwebsite.com/terms-conditions</comment>
                    <depends>
                        <field id="pageLayout">2</field>
                    </depends>
                </field>
                <!-- Layout 3 fields -->
                <field id="displaybannerLayout3" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Banner</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="bannerLayout3" translate="label" type="image" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Image</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 1500x600 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/banner</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/banner</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="bannercontentLayout3" translate="label comment" sortOrder="4" type="editor" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Banner Content</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field</frontend_model>
                    <comment>This content will be display over the banner image in the marketplace landing page url-www.yourwebsite.com/marketplace</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="marketplacebuttonLayout3" translate="label comment" sortOrder="5" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Layout3 Button Label</label>
                    <comment>e.g Open your shop - this will also display over banner button</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="marketplacelabel1Layout3" translate="label comment" sortOrder="6" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label1</label>
                    <comment>e.g Really easy to setup and customize (will be display above all feature's icons)</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="marketplacelabel2Layout3" translate="label comment" sortOrder="7" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label2</label>
                    <comment>e.g Why to sell with us</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="marketplacelabel3Layout3" translate="label comment" sortOrder="8" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Marketplace Landing Page Label3</label>
                    <comment>e.g Open your online shop and Explore a new world of market with more than millions of shoppers(will be display above "View All Sellers" button)</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="displayiconsLayout3" translate="label comment" sortOrder="9" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Icons</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon1_layout3" translate="label" type="image" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:1 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon1_label_layout3" translate="label comment" sortOrder="11" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:1 Icon Label</label>
                    <comment>e.g Create an Account</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon2_layout3" translate="label" type="image" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:2 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon2_label_layout3" translate="label comment" sortOrder="13" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:2 Icon Label</label>
                    <comment>e.g Customize your Profile</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon3_layout3" translate="label" type="image" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:3 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon3_label_layout3" translate="label comment" sortOrder="15" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:3 Icon Label</label>
                    <comment>e.g Add your Details</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon4_layout3" translate="label" type="image" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:4 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon4_label_layout3" translate="label comment" sortOrder="17" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:4 Icon Label</label>
                    <comment>e.g Add your Product Listing</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon5_layout3" translate="label" type="image" sortOrder="18" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:5 Icon</label>
                    <comment>Allowed file types: jpg, jpeg, gif, png. Recommended image size: 150x150 px</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Image</backend_model>
                    <base_url type="media" scope_info="1">marketplace/icon</base_url>
                    <upload_dir config="system/filesystem/media" scope_info="1">marketplace/icon</upload_dir>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="feature_icon5_label_layout3" translate="label comment" sortOrder="19" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Feature:5 Icon Label</label>
                    <comment>e.g Sell your Product and Earn Profits</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="termConditionLinkLayout3" translate="label comment" sortOrder="20" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Terms and Conditions URL</label>
                    <comment>e.g Terms and Conditions link(cms page link) where customer will see all the terms of becoming seller, url will be look like -www.yourwebsite.com/terms-conditions</comment>
                    <depends>
                        <field id="pageLayout">3</field>
                    </depends>
                </field>
                <field id="allow_seller_registration_block" translate="label comment" sortOrder="0" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Seller registration block on customer registration page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>

            <group id="layered_navigation" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller Layered Navigation</label>
                <field id="enable" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Seller Filter in Layered Navigation</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="admin_name" translate="label comment" sortOrder="15" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Name for Admin in Layered Navigation</label>
                    <comment>This will be used for Admin's product in layered navigation</comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>

            <group id="sitemap" translate="label" type="text" sortOrder="76" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Seller's Sitemap Setting</label>
                <field id="enable" translate="label comment" sortOrder="5" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Seller's Url in Sitemap</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_profile_url" translate="label comment" sortOrder="10" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Seller's Profile Url in Sitemap</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="profile_url_changefreq" translate="label" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller's Profile Url Frequency in Sitemap</label>
                    <source_model>Magento\Sitemap\Model\Config\Source\Frequency</source_model>
                    <depends>
                        <field id="enable">1</field>
                        <field id="allow_profile_url">1</field>
                    </depends>
                </field>
                <field id="profile_url_priority" translate="label comment" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller's Profile Url Priority in Sitemap</label>
                    <backend_model>Magento\Sitemap\Model\Config\Backend\Priority</backend_model>
                    <comment>Valid values range from 0.0 to 1.0.</comment>
                    <depends>
                        <field id="enable">1</field>
                        <field id="allow_profile_url">1</field>
                    </depends>
                </field>
                <field id="allow_collection_url" translate="label comment" sortOrder="50" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Seller's Collection Url in Sitemap</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="collection_url_changefreq" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller's Collection Url Frequency in Sitemap</label>
                    <source_model>Magento\Sitemap\Model\Config\Source\Frequency</source_model>
                    <depends>
                        <field id="enable">1</field>
                        <field id="allow_collection_url">1</field>
                    </depends>
                </field>
                <field id="collection_url_priority" translate="label comment" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller's Collection Url Priority in Sitemap</label>
                    <backend_model>Magento\Sitemap\Model\Config\Backend\Priority</backend_model>
                    <comment>Valid values range from 0.0 to 1.0.</comment>
                    <depends>
                        <field id="enable">1</field>
                        <field id="allow_collection_url">1</field>
                    </depends>
                </field>
            </group>

            <group id="producthint_settings" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Marketplace Product Page Fields Hints</label>
                <field id="product_hint_status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Product Hints</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="product_category" translate="label comment" sortOrder="2" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Category</label>
                </field>
                <field id="product_name" translate="label comment" sortOrder="3" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Name</label>
                </field>
                <field id="product_des" translate="label comment" sortOrder="4" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Description</label>
                </field>
                <field id="product_sdes" translate="label comment" sortOrder="5" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Short Description</label>
                </field>
                <field id="product_sku" translate="label comment" sortOrder="6" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product SKU</label>
                </field>
                <field id="product_price" translate="label comment" sortOrder="7" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Price</label>
                </field>
                <field id="product_sprice" translate="label comment" sortOrder="8" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Special Price</label>
                </field>
                <field id="product_sdate" translate="label comment" sortOrder="9" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Special Price Start Date</label>
                </field>
                <field id="product_edate" translate="label comment" sortOrder="10" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Special Price End Date</label>
                </field>
                <field id="product_qty" translate="label comment" sortOrder="11" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Quantity</label>
                </field>
                <field id="product_stock" translate="label comment" sortOrder="12" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Stock Availability</label>
                </field>
                <field id="product_tax" translate="label comment" sortOrder="13" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Tax Types</label>
                </field>
                <field id="product_weight" translate="label comment" sortOrder="14" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Weight</label>
                </field>
                <field id="product_image" translate="label comment" sortOrder="15" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Image</label>
                </field>
                <field id="product_enable" translate="label comment" sortOrder="16" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Enable</label>
                </field>
            </group>
            <group id="profilehint_settings" translate="label" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Marketplace Profile Page Fields Hints</label>
                <field id="profile_hint_status" translate="label comment" sortOrder="1" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Profile Hints</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="become_seller" translate="label comment" sortOrder="2" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Do You Want To Become Seller/Vendor?</label>
                </field>
                <field id="shopurl_seller" translate="label comment" sortOrder="3" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Shop URL</label>
                </field>
                <field id="profile_tw" translate="label comment" sortOrder="4" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Twitter id</label>
                </field>
                <field id="profile_fb" translate="label comment" sortOrder="5" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Facebook id</label>
                </field>
                <field id="profile_inst" translate="label comment" sortOrder="6" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Instagram id</label>
                </field>
                <field id="profile_google" translate="label comment" sortOrder="7" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Google id</label>
                </field>
                <field id="profile_youtube" translate="label comment" sortOrder="8" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Youtube id</label>
                </field>
                <field id="profile_vimeo" translate="label comment" sortOrder="9" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Vimeo id</label>
                </field>
                <field id="profile_pin" translate="label comment" sortOrder="10" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Pinterest id</label>
                </field>
                <field id="profile_cn" translate="label comment" sortOrder="11" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Contact Number</label>
                </field>
                <field id="profile_bc" translate="label comment" sortOrder="12" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Background Color</label>
                </field>
                <field id="profile_shop" translate="label comment" sortOrder="13" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Shop Name</label>
                </field>
                <field id="profile_banner" translate="label comment" sortOrder="14" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Shop Banner Image</label>
                </field>
                <field id="profile_logo" translate="label comment" sortOrder="15" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Company Logo</label>
                </field>
                <field id="profile_loc" translate="label comment" sortOrder="16" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Company Locality</label>
                </field>
                <field id="profile_desciption" translate="label comment" sortOrder="17" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Company Description</label>
                </field>
                <field id="returnpolicy" translate="label comment" sortOrder="18" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Return Policy</label>
                </field>
                <field id="shippingpolicy" translate="label comment" sortOrder="19" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Shipping Policy</label>
                </field>
                <field id="profile_country" translate="label comment" sortOrder="20" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Country</label>
                </field>
                <field id="profile_meta" translate="label comment" sortOrder="21" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Meta Keywords</label>
                </field>
                <field id="profile_mdesc" translate="label comment" sortOrder="22" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Meta Description</label>
                </field>
                <field id="profile_bank" translate="label comment" sortOrder="23" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Profile Payment Information</label>
                </field>
                <field id="profile_tax" translate="label comment" sortOrder="24" type="textarea" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tax/VAT Number</label>
                </field>
            </group>
            <group id="email" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Marketplace Transactional Emails</label>
                <field id="seller_approve_notification_template" translate="label comment" type="select" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Approved Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="becomeseller_request_notification_template" translate="label comment" type="select" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Request Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="seller_disapprove_notification_template" translate="label comment" type="select" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Unsubscribe Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="seller_process_notification_template" translate="label comment" type="select" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller State Processing Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="seller_deny_notification_template" translate="label comment" type="select" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Deny by Admin Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                 <field id="product_deny_notification_template" translate="label comment" type="select" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Deny by Admin Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="new_product_notification_template" translate="label comment" type="select" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Approval Request to Admin Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="edit_product_notification_template" translate="label comment" type="select" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product Edit Approval Request to Admin Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="askproductquery_seller_template" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Ask Question to Seller Regarding Product Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="askquery_seller_template" translate="label comment" type="select" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Ask Question to Seller Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="askquery_admin_template" translate="label comment" type="select" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Ask Question to Admin By Seller Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="product_approve_notification_template" translate="label comment" type="select" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product approval Mail to Seller Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="product_disapprove_notification_template" translate="label comment" type="select" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Product disapproval Mail to Seller Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="order_placed_notification_template" translate="label comment" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Order Placed Notification Mail Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="order_invoiced_notification_template" translate="label comment" type="select" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Order Invoiced Notification Mail Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="seller_transaction_notification_template" translate="label comment" type="select" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Transaction Mail Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="low_stock_template" translate="label comment" type="select" sortOrder="18" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Seller Low Stock Notification Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="withdrawal_request_template" translate="label comment" type="select" sortOrder="19" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Withdrawal Request Mail Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="product_flag_template" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reported Flag Against Product Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="seller_flag_template" translate="label comment" type="select" sortOrder="21" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reported Flag Against Seller Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
