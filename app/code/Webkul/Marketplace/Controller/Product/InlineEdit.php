<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Webkul\Marketplace\Controller\Product;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Exception\LocalizedException;

class InlineEdit extends \Magento\Framework\App\Action\Action implements HttpPostActionInterface
{
    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonFactory
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param ResourceConnection $resourceConnection
     * @param \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        private \Magento\Framework\Controller\Result\JsonFactory $jsonFactory,
        private \Magento\Catalog\Model\ProductRepository $productRepository,
        private readonly ResourceConnection $resourceConnection,
        private \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->jsonFactory->create();
        $error = false;
        $messages = [];
        $postItems = $this->getRequest()->getParam('items', []);
        if (count($postItems)) {
            if (!count($postItems)) {
                $messages[] = __('Please correct the data sent.');
                $error = true;
            } else {
                foreach ($postItems as $item) {
                    try {
                        if ($item['entity_id']) {
                            $product = $this->productRepository->getById($item['entity_id']);
                            $stockItem = $this->stockRegistry->getStockItem($item['entity_id']);
                            $stockItem->setData('qty', $item['qty']);
                            $product->setStatus($item['status']);
                            $product->setPrice($item['price']);
                            $product->setVisibility($item['visibility']);
                            $product->save();
                            $this->handleApproval($item);
                        }
                    } catch (\Exception $e) {
                        $messages[] = "[Error:]  {$e->getMessage()}";
                        $error = true;
                    }
                }
            }
        }

        return $resultJson->setData([
            'messages' => $messages,
            'error' => $error
        ]);
    }

    /**
     * @param array $item
     * @return void
     */
    private function handleApproval(array $item): void
    {
        if (!isset($item['is_approved']) || !isset($item['entity_id'])) {
            return;
        }

        $connection = $this->resourceConnection->getConnection('write');
        $connection->update(
            $connection->getTableName('marketplace_product'),
            [
                'is_approved' => $item['is_approved'],
            ],
            [
                'mageproduct_id = ?' => $item['entity_id'],
            ]
        );
    }
}
