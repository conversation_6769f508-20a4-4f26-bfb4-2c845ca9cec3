<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Service;

use Magento\Customer\Model\Customer;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;

class UserInfoService
{
    /**
     * @var string[]|array
     */
    private array $loadedEntities = [];

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(private readonly CollectionFactory $collectionFactory)
    {}

    /**
     * @param int $productId
     * @param int|null $orderId
     * @return array{id: string, uid?: string, name: string}|array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get(int $productId, ?int $orderId = null): array
    {
        $loadedKey = sprintf('%s-%s', $productId, $orderId);

        if (isset($this->loadedEntities[$loadedKey])) {
            return $this->loadedEntities[$loadedKey];
        }

        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->reset(\Magento\Framework\DB\Select::COLUMNS)
            ->columns(['entity_id', 'firstname', 'lastname']);

        $collection->addAttributeToSelect('commave_uuid', 'left');

        if ($orderId) {
            $collection->getSelect()
                ->join(
                    ['so' => $collection->getTable('marketplace_saleslist')],
                    '`so`.seller_id = `e`.entity_id',
                    ''
                )->where(
                    'order_id = ?',
                    $orderId
                )->group('seller_id');
        } else {
            $collection->getSelect()
                ->join(
                    ['mp' => $collection->getTable('marketplace_product')],
                    'mp.seller_id = e.entity_id',
                    []
                );
        }

        $collection->getSelect()->where('mageproduct_id = ?', $productId);

        if (!$collection->getSize()) {
            return [];
        }

        /** @var Customer $customer */
        $customer = $collection->getFirstItem();

        $this->loadedEntities[$loadedKey] = [
            'id' => $customer->getId(),
            'name' => $customer->getName(),
            'uid' => $customer->getData('commave_uuid')
        ];

        return $this->loadedEntities[$loadedKey];
    }
}
