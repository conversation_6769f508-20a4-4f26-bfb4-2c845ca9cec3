<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Webkul\Marketplace\Block\Adminhtml\Items\Column\Name;

use Webkul\Marketplace\Service\UserInfoService;

class Seller extends \Magento\Sales\Block\Adminhtml\Items\Column\Name
{

    /**
     * @param UserInfoService $userInfoService
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry
     * @param \Magento\CatalogInventory\Api\StockConfigurationInterface $stockConfiguration
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Catalog\Model\Product\OptionFactory $optionFactory
     * @param array $data
     */
    public function __construct(
        private readonly UserInfoService $userInfoService,
        \Magento\Backend\Block\Template\Context $context,
        \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
        \Magento\CatalogInventory\Api\StockConfigurationInterface $stockConfiguration,
        \Magento\Framework\Registry $registry,
        \Magento\Catalog\Model\Product\OptionFactory $optionFactory,
        array $data = []
    ) {
        parent::__construct($context, $stockRegistry, $stockConfiguration, $registry, $optionFactory, $data);
    }

    /**
     * @param $id
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getUserInfo($id): array
    {
        $order = $this->getOrder();

        return $this->userInfoService->get(
            (int) $id,
            (int) $order->getId()
        );
    }

    /**
     * Get Customer Url By Customer Id.
     *
     * @param string $customerId
     *
     * @return string
     */
    public function getCustomerUrl($customerId)
    {
        return $this->_urlBuilder->getUrl(
            'customer/index/edit',
            ['id' => $customerId]
        );
    }
}
