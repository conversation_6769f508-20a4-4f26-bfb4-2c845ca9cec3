<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\MpMultiShopifyStoreMageConnect\Helper;

use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifycategorymapRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifyaccountsRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ImportedtmpproductRepositoryInterface;
use Magento\Framework\Data\Form\FormKey;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Group\CollectionFactory as AttrGroupCollection;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory as AttrOptionCollectionFactory;
use Magento\Eav\Api\AttributeManagementInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable\Attribute as ConfigurableAttributeModel;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProTypeModel;
use Magento\Framework\Filter\FilterManager;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ListingTemplateFactory;
use Magento\Framework\Exception\LocalizedException;
use Comave\MapOrderStatuses\Service\OrderStatuses;

/**
 * Custom MpMultiShopifyStoreMageConnect Data helper.
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    /**
     * @var array $operation
     */
    private static $operation = [
        '' => '--Select--',
        'Increase' => 'Increase',
        'Decrease' => 'Decrease',
    ];

    /**
     * @var array $operationType
     */
    private static $operationType = [
            '' => '--Select--',
            'Fixed' => 'Fixed',
            'Percent' => 'Percent',
    ];

    /**
     * @var array $status
     */
    private static $status = [
        '1' => 'Enable',
        '0' => 'Disable',
    ];

    /**
     * @var const CATALOGSEARCH_FULLTEXT
     */
    public const CATALOGSEARCH_FULLTEXT = 'catalogsearch_fulltext';

    public const IMG_STORE_DEFAULT = 0;

    public const IMG_STORE_PROFILER = 1;

    public const IMG_STORE_MQ = 2;

    public const IMG_IMPORT_CONSUMER_NAME = "shopify.image.import";

    public const PRODUCT_MAP_CONSUMER_NAME = "shopify.product.map";

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $_storeManager;

    /**
     * @var object $_scopeConfig
     */
    protected $_scopeConfig;

    /**
     * @var object $importedTmpProductRepository
     */
    private $importedTmpProductRepository;

    /**
     * @var RootCat's object
     */
    private $rootCat;

    /**
     * @var \Magento\Framework\Data\Form\FormKey $formkey
     */
    private $formkey;

    /**
     * @var \Magento\Framework\HTTP\Client\Curl
     */
    private $curl;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    public $jsonHelper;

    /**
     * @var FilterManager
     */
    public $filterManager;

    /**
     * @var ShopifycategorymapRepositoryInterface
     */
    public $shopifyCategoryMapRepository;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory
     */
    public $_shopifyaccountsFactory;

    /**
     * @var ShopifyaccountsRepositoryInterface
     */
    public $shopifyAccountsRepository;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\Storage\DbStorage
     */
    public $dbStorage;

    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    public $product;

    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    public $productRepository;

    /**
     * @var SaveProduct
     */
    public $saveProduct;

    /**
     * @var AttributeFactory
     */
    public $attributeFactory;

    /**
     * @var AttrGroupCollection
     */
    public $attrGroupCollection;

    /**
     * @var AttrOptionCollectionFactory
     */
    public $attrOptionCollectionFactory;

    /**
     * @var AttributeManagementInterface
     */
    public $attributeManagement;

    /**
     * @var ProductAttributeRepositoryInterface
     */
    public $productAttribute;

    /**
     * @var \Magento\Framework\Registry
     */
    public $registry;

    /**
     * @var \Magento\Framework\Filesystem
     */
    public $filesystem;

    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    public $file;

    /**
     * @var ConfigurableAttributeModel
     */
    public $configurableAttributeModel;

    /**
     * @var ConfigurableProTypeModel
     */
    public $configurableProTypeModel;

    /**
     * @var \Magento\Widget\Model\Template\Filter
     */
    public $templateProcessor;

    /**
     * @var \Magento\Framework\Indexer\IndexerInterfaceFactory
     */
    public $indexerFactory;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    public $encryptor;

    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    public $cartRepositoryInterface;

    /**
     * @var \Magento\Quote\Api\CartManagementInterface
     */
    public $cartManagementInterface;

    /**
     * @var \Magento\Quote\Model\Quote\Address\Rate
     */
    public $shippingRate;

    /**
     * @var \Magento\Sales\Model\Order
     */
    public $order;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    public $customerRepository;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    public $customerFactory;

    /**
     * @var \Magento\Backend\Model\Session
     */
    public $backendSession;

    /**
     * @var \Magento\Directory\Model\Config\Source\Country
     */
    public $countryHelper;

    /**
     * @var \Magento\Directory\Model\CountryFactory
     */
    public $countryFactory;

    /**
     * @var ListingTemplateFactory
     */
    public $listingTemplate;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger
     */
    public $logger;

    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    public $mphelper;

    /**
     * @var \Webkul\Marketplace\Model\ProductFactory
     */
    public $mpProductFactory;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    public $_date;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\PriceRuleFactory
     */
    public $priceRule;

    /**
     * @var string
     */
    public $ruleId;

    /**
     * @var \Laminas\Uri\Uri
     */
    public $laminasUri;

    /**
     * @var AttributeSetInterface
     */
    protected $attributeSetRepository;
    /**
     * @var OrderStatuses
     */
    private $orderStatuses;

    public function __construct(
        FilterManager $filterManager,
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifycategorymap\Source\RootCatFactory $rootCat,
        ShopifycategorymapRepositoryInterface $shopifyCategoryMapRepository,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyaccountsFactory,
        ShopifyaccountsRepositoryInterface $shopifyAccountsRepository,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\Storage\DbStorage $dbStorage,
        ImportedtmpproductRepositoryInterface $importedTmpProductRepository,
        \Magento\Catalog\Model\ProductFactory $product,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        FormKey $formkey,
        SaveProduct $saveProduct,
        AttributeFactory $attributeFactory,
        AttrGroupCollection $attrGroupCollection,
        AttrOptionCollectionFactory $attrOptionCollectionFactory,
        AttributeManagementInterface $attributeManagement,
        ProductAttributeRepositoryInterface $productAttribute,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Filesystem $filesystem,
        ConfigurableAttributeModel $configurableAttributeModel,
        ConfigurableProTypeModel $configurableProTypeModel,
        \Magento\Widget\Model\Template\Filter $templateProcessor,
        \Magento\Framework\Indexer\IndexerInterfaceFactory $indexerFactory,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Quote\Api\CartRepositoryInterface $cartRepositoryInterface,
        \Magento\Quote\Api\CartManagementInterface $cartManagementInterface,
        \Magento\Quote\Model\Quote\Address\Rate $shippingRate,
        \Magento\Sales\Model\Order $order,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Backend\Model\Session $backendSession,
        \Magento\Directory\Model\Config\Source\Country $countryHelper,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\HTTP\Client\Curl $curl,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\PriceRuleFactory $priceRule,
        ListingTemplateFactory $listingTemplate,
        \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger $logger,
        \Magento\Framework\Filesystem\Driver\File $file,
        \Webkul\Marketplace\Helper\Data $mphelper,
        \Webkul\Marketplace\Model\ProductFactory $mpProductFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Laminas\Uri\Uri $laminasUri,
        \Magento\Catalog\Api\AttributeSetRepositoryInterface $attributeSetRepository,
        OrderStatuses $orderStatuses,
    ) {
        $this->filterManager = $filterManager;
        $this->_storeManager = $storeManager;
        $this->_scopeConfig = $context->getScopeConfig();
        $this->rootCat = $rootCat;
        $this->shopifyCategoryMapRepository = $shopifyCategoryMapRepository;
        $this->_shopifyaccountsFactory = $shopifyaccountsFactory;
        $this->shopifyAccountsRepository = $shopifyAccountsRepository;
        $this->dbStorage = $dbStorage;
        $this->importedTmpProductRepository = $importedTmpProductRepository;
        $this->product = $product;
        $this->productRepository = $productRepository;
        $this->formkey = $formkey;
        $this->saveProduct = $saveProduct;
        $this->attributeFactory = $attributeFactory;
        $this->attrGroupCollection = $attrGroupCollection;
        $this->attrOptionCollectionFactory = $attrOptionCollectionFactory;
        $this->attributeManagement = $attributeManagement;
        $this->productAttribute = $productAttribute;
        $this->registry = $registry;
        $this->filesystem = $filesystem;
        $this->file = $file;
        $this->configurableAttributeModel = $configurableAttributeModel;
        $this->configurableProTypeModel = $configurableProTypeModel;
        $this->indexerFactory = $indexerFactory;
        $this->encryptor = $encryptor;
        $this->cartRepositoryInterface = $cartRepositoryInterface;
        $this->cartManagementInterface = $cartManagementInterface;
        $this->shippingRate = $shippingRate;
        $this->order = $order;
        $this->customerRepository = $customerRepository;
        $this->customerFactory = $customerFactory;
        $this->backendSession = $backendSession;
        $this->countryHelper = $countryHelper;
        $this->countryFactory = $countryFactory;
        $this->curl = $curl;
        $this->jsonHelper = $jsonHelper;
        $this->templateProcessor = $templateProcessor;
        $this->priceRule    = $priceRule;
        $this->listingTemplate = $listingTemplate;
        $this->logger = $logger;
        $this->mphelper = $mphelper;
        $this->mpProductFactory = $mpProductFactory;
        $this->_date = $date;
        $this->laminasUri = $laminasUri;
        $this->attributeSetRepository = $attributeSetRepository;
        parent::__construct($context);
    }

    /**
     * Get Logger for get log
     *
     * @return \Webkul\MultishopifyStoreMageConnect\Logger\Logger
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * LogInfoMessage function log the info message
     *
     * @param string $msg
     * @return void
     */
    public function logInfoMessage($msg = '')
    {
        $this->getLogger()->info($msg);
    }

    /**
     * LogCriticalMessage function log the critical message
     *
     * @param string $msg
     * @return void
     */
    public function logCriticalMessage($msg = '')
    {
        $this->getLogger()->critical($msg);
    }

    /**
     * GetShopifyConfiguration function get the shopify account configuration
     *
     * @param string $id
     * @return array
     */
    public function getShopifyConfiguration($id = ""): array
    {
        return $this->shopifyAccountsRepository->getConfigurationById($id)->toArray();
    }

    /**
     *  Get default attribute set
     *
     * @param int $shopId
     * @return int
     */
    public function getAttributeSetId($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['attribute_set_id'];
    }

    /**
     *  Get default website
     *
     * @param int $shopId
     * @return void
     */
    public function getDefaultWebsite($shopId = '')
    {
        $defaultStoreView = $this->getDefaultStoreView($shopId);
        $store = $this->_storeManager->getStore($defaultStoreView);
        $websiteId = $store->getWebsiteId();
        return $websiteId;
    }

    /**
     * Get default store VIEW
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultStoreForOrderSync($shopId = '')
    {
        return $this->getDefaultStoreView($shopId);
    }

    /**
     * Get default store view
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultStoreView($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['default_store_view'];
    }

    /**
     * Get default order status
     *
     * @param int $shopId
     * @return string
     */
    public function getDefaultOrderStatus($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['order_status'];
    }

    /**
     * Get default order status
     *
     * @param int $shopId
     * @return string
     */
    public function getCurrConRate($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['currency_conv_rate'];
    }

    /**
     * Get default category
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultCate($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['default_cate'];
    }

    /**
     * Get mp seller id
     *
     * @param int $shopId
     * @return int
     */
    public function getMpSellerId($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['seller_id'];
    }

    /**
     * Get default item with html status
     *
     * @param int $shopId
     * @return int
     */
    public function isDesWithHtml($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['item_with_html'];
    }

    /**
     * Get default template id
     *
     * @param int $shopId
     * @return int
     */
    public function useTemplate($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['template_id'];
    }

    /**
     * Get default allowed product type list
     *
     * @param int $shopId
     * @return string
     */
    public function getProductTypeAllowed($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['product_type_allowed'];
    }

    /**
     * Get default price rule status
     *
     * @param int $shopId
     * @return string
     */
    public function getPriceRuleOn($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['price_rule_on'];
    }

    /**
     * Get default product qty from particular shopify account configuration
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultProductQty($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['default_qty'];
    }

    /**
     * GecryptTheApiCredential function decrypt the shop credentials
     *
     * @param string $apiKey
     * @param string $pwd
     * @return array
     */
    public function decryptTheApiCredential($apiKey = '', $pwd = '')
    {
        $apiKey = $this->encryptor->decrypt($apiKey);
        $pwd = $this->encryptor->decrypt($pwd);
        return ['shopify_api_key'=> $apiKey, 'shopify_pwd'=> $pwd];
    }

     /**
      * DecryptTheApiCredential function decrypt the shop credentials
      *
      * @param string $accessToken
      * @return array
      */
    public function decryptTheAccessToken($accessToken = '')
    {
        return $this->encryptor->decrypt($accessToken);
    }

    /**
     * Get store base currency code
     *
     * @return string
     */
    public function getBaseCurrencyCode()
    {
        return $this->_storeManager->getStore()->getBaseCurrencyCode();
    }

    /**
     * ConvertPrice function
     *
     * @param [double] $price
     * @param [string] $process
     * @return double
     */
    public function convertPrice($price, $process)
    {
        $convRate = $this->getCurrConRate($this->ruleId);
        if ($convRate == 0 || $convRate == 0.0) {
            return $price;
        }
        if ($process == 'import') {
            $this->logCriticalMessage("check :".$price/$convRate);
            return $price/$convRate;
        }
        if ($process == 'export') {
            return $price*$convRate;
        }
    }

    /**
     * AuthorizeShopifyShop function authorize the shopify shop credentials and return the shop id
     *
     * @param array $data
     * @return array
     */
    public function authorizeShopifyShop($data = [])
    {
        try {
            $url = "https://".$data['shopify_domain_name']."/admin/shop.json";
            if ($data['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $data['access_token']
                );
            } else {
                $this->curl->setCredentials(
                    $credentials['shopify_api_key'],
                    $credentials['shopify_pwd']
                );
            }

            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                $data['shopify_user_id'] = $this->jsonDecoder($this->curl->getBody())['shop']['id'];
                $data['shopify_base_currency'] = $this->jsonDecoder($this->curl->getBody())['shop']['currency'];
                $data['api_request_http_code'] = $this->curl->getStatus();
                $data['status'] = true;
                return $data;
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("authorizeShopifyShop :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Shopify user didn\'t authorize successfully, Please try again and check the log.')
            ];
        }
    }

    /**
     * GetOrderFromShopify function get the list of order from shopify shop
     *
     * @param array $shopifyShopConfiguration
     * @return array
     */
    public function getOrderFromShopify($shopifyShopConfiguration = [])
    {
        try {
            if ($shopifyShopConfiguration['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $this->decryptTheAccessToken($shopifyShopConfiguration['access_token'])
                );
            } else {
                $credentials = $this->decryptTheApiCredential(
                    $shopifyShopConfiguration['shopify_api_key'],
                    $shopifyShopConfiguration['shopify_pwd']
                );
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $url = "https://".$shopifyShopConfiguration['shopify_domain_name']."/admin/orders.json?status=any";
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return [
                    'status'=>false,
                    'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']
                ];
            }
            if ($this->curl->getStatus() == 200) {
                return [
                    'status'=> true,
                    'data'=> $this->jsonDecoder($this->curl->getBody())
                ];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("getOrderFromShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while importing Shopify order, Please check the log.')
            ];
        }
    }

    /**
     * GetTheCustomCollectionFromShopify function get the categoreis (custom collections) from shopify
     *
     * @param string $id
     * @return array
     */
    public function getTheCustomCollectionFromShopify($id = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($id, true);
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $customCollections = ["custom_collections"=>[]];
            $errorMsg = '';
            $status = false;
            $categoryTypeList = ['smart_collections','custom_collections'];
            foreach ($categoryTypeList as $key => $categoryType) {
                $url = "https://".$credentials['shopify_domain_name']."/admin/api/2021-07/"
                                 .$categoryType.".json?limit=250";
                $tmpUrl = $url;
                do {
                    $arr = [
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "GET",
                    ];
                    $this->curl->setOptions($arr);
                    $this->curl->get($tmpUrl);
                    if ($this->curl->getStatus() == 200) {
                        $status = true;
                        $headers = $this->curl->getHeaders();
                        $categories = $this->jsonDecoder($this->curl->getBody());
                        $customCollections['custom_collections'] = array_merge_recursive(
                            $customCollections['custom_collections'],
                            $categories[$categoryType]
                        );
                        $pageInfo = $this->getResponsePageInfo();
                        $tmpUrl = $pageInfo != '' ? $url.'&page_info='.$pageInfo : '';
                    } else {
                        $errorMsg = $this->jsonDecoder($this->curl->getBody())['errors'];
                    }
                } while ($tmpUrl && $errorMsg == '' && count($categories[$categoryType]) == 250);
            }
            return ['status' => $status, 'error_msg' => $errorMsg, 'data' => $customCollections];
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getTheCustomCollectionFromShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping Shopify categories, Please check the log.')
            ];
        }
    }

    /**
     * GheckTheShopifyMappingCategoryExist function check that mapped category exixt or not at shopify end
     *
     * @param string $id
     * @param int $collectionId
     * @return array
     */
    public function checkTheShopifyMappingCategoryExist($id = '', $collectionId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($id, true);
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $categoryType = ['custom_collection', 'smart_collections'];
            $shopifyCategoryData = false;
            $status = false;
            $errorMsg = false;
            $count = 0;
            $categoryType = 'custom_collections';
            do {
                $url = "https://".$credentials['shopify_domain_name']."/admin/api/2021-07/"
                                 .$categoryType."/".$collectionId.".json";
                $arr = [
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                ];
                $this->curl->setOptions($arr);
                $this->curl->get($url);
                if ($this->curl->getStatus() != 200) {
                    $errorMsg = $this->jsonDecoder($this->curl->getBody())['errors'];
                } elseif ($this->curl->getStatus() == 200) {
                    $status = true;
                    $shopifyCategoryData = $this->jsonDecoder($this->curl->getBody());
                    $categoryType = substr_replace($categoryType, "", -1);
                    $shopifyCategoryData = $shopifyCategoryData[$categoryType];
                }
                $count++;
                $categoryType = 'smart_collections';
            } while ($shopifyCategoryData === false && $count < 2);
            return ['status'=> $status, 'data'=> $shopifyCategoryData, 'error_msg' => $errorMsg];
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getTheCustomCollectionFromShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping Shopify categories, Please check the log.')
            ];
        }
    }

    /**
     * GetAllShopifyCategoryAssociatedWithProduct function get the all categories at shopify end
     *
     * @param string $shopId
     * @param int $shopifyProductId
     * @return array
     */
    public function getAllShopifyCategoryAssociatedWithProduct($shopId = '', $shopifyProductId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $url = "https://".$credentials['shopify_domain_name']."/admin/collects.json?product_id=".$shopifyProductId;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getAllShopifyCategoryAssociatedWithProduct :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping Shopify categories, Please check the log.')
            ];
        }
    }

    /**
     * UpdateProductatShopify function update the product at shopify end
     *
     * @param int $shopId
     * @param int $productId
     * @param array $shopifyProductUpdateData
     * @return array
     */
    public function updateProductAtShopify($shopId = '', $productId = '', $shopifyProductUpdateData = [])
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $this->curl->addHeader('content-type', 'application/json');
            $url = "https://".$credentials['shopify_domain_name']."/admin/products/".$productId.".json";
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_POSTFIELDS => $this->jsonEncoder($shopifyProductUpdateData['item']),
                CURLOPT_CUSTOMREQUEST => "PUT",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getTheCustomCollectionFromShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping Shopify categories, Please check the log.')
            ];
        }
    }

    /**
     * UpdateProductQty function updtae the product qty at shopify shop after place order
     *
     * @param int $prdQty
     * @param string $shopId
     * @param array $inventoryItemIds
     * @param string $type
     * @return array
     */
    public function updateProductQty($prdQty, $shopId = '', $inventoryItemIds = '', $type = 'adjust')
    {
        try {
            $inventoryLevels = $this->getInventoryLevels($shopId, $inventoryItemIds);
            if ($type != 'set') {
                $this->checkShopifyLocationInventoryStock($prdQty, $inventoryLevels);
            }
                $locationId = $this->getTheLowestIdLocation($inventoryLevels, $prdQty);
                return $this->updateProductInventoryAtShopify($shopId, $locationId, $inventoryItemIds, $prdQty, $type);
        } catch (\Exception $e) {
            $this->logCriticalMessage("getTheCustomCollectionFromShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while update the product qty at Shopify, Please check the log.')
            ];
        }
    }

    /**
     * GetShopifyProductQtyFromLowestIdLocation function
     *
     * @param int $shopId
     * @param int $inventoryItemIds
     * @return int|array
     */
    public function getShopifyProductQtyFromLowestIdLocation($shopId, $inventoryItemIds)
    {
        try {
            $inventoryLevels =  $this->getInventoryLevels($shopId, $inventoryItemIds);
            $lowestIdLocationData = $this->getTheLowestIdLocation($inventoryLevels);
            return $lowestIdLocationData;
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getInventoryLevels :".$e->getMessage());
        }
    }

    /**
     * GetInventoryLevels function
     *
     * @param int $shopId
     * @param int $inventoryItemIds
     * @return array
     */
    public function getInventoryLevels($shopId, $inventoryItemIds)
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->setCredentials($credentials['shopify_api_key'], $credentials['shopify_pwd']);
            }
            $this->curl->addHeader('content-type', 'application/json');
            $url = "https://".$credentials['shopify_domain_name'].
                "/admin/inventory_levels.json?inventory_item_ids=".$inventoryItemIds;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return $inventoryLevels = $this->jsonDecoder($this->curl->getBody());
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getInventoryLevels :".$e->getMessage());
        }
    }

    /**
     * GetTheLowestIdLocation function
     *
     * @param array $InventoryLevels
     * @param int $prodOrderQty
     * @return int
     */
    public function getTheLowestIdLocation($InventoryLevels = [], $prodOrderQty = -1)
    {
        try {
            if (!isset($InventoryLevels['inventory_levels'][0]['location_id'])) {
                return [];
                //throw new LocalizedException(__('Not getting the location id of shopfiy to update inventory'));
            }
            if (array_key_exists('inventory_levels', $InventoryLevels)) {
                $minLocationId = $InventoryLevels['inventory_levels'][0]['location_id'];
                foreach ($InventoryLevels['inventory_levels'] as $InventoryLevel) {
                    if ($InventoryLevel['location_id']  <= $minLocationId) {
                            $minLocationId = $InventoryLevel['location_id'];
                            $shopifyProdQty = $InventoryLevel['available'];
                    }
                }
                $this->logInfoMessage("Helper Data getTheLowestIdLocation : ".$minLocationId);
                if ($prodOrderQty == -1) {
                    return ['minimumLocationId' => $minLocationId, 'qty' => $shopifyProdQty];
                }
                return $minLocationId;
            }

        } catch (\Exception $e) {
            $this->logCriticalMessage("getTheLowestIdLocation :".$e->getMessage());
        }
    }

    /**
     * CheckShopifyLocationInventoryStock function
     *
     * @param int $prdQty
     * @param array $InventoryLevels
     * @return void
     */
    public function checkShopifyLocationInventoryStock($prdQty, $InventoryLevels = [])
    {
        $qtyTotal = 0;
        foreach ($InventoryLevels['inventory_levels'] as $inventoryLocation) {
            $qtyTotal += $inventoryLocation['available'];
        }
        $this->logInfoMessage("Helper Data checkShopifyLocationInventoryStock qtyTotal: ".$qtyTotal);
        $this->logInfoMessage("Helper Data checkShopifyLocationInventoryStock prdQty: ".$prdQty);
        if ($prdQty >= $qtyTotal) {
            throw new LocalizedException(__('In sufficient stock at shopify'));
        }
    }

    /**
     * UpdateProductInventoryAtShopify function
     *
     * @param int $shopId
     * @param int $locationId
     * @param int $inventoryItemId
     * @param int $avilableAdjustment
     * @param string $type
     * @return void
     */
    public function updateProductInventoryAtShopify(
        $shopId,
        $locationId,
        $inventoryItemId,
        $avilableAdjustment,
        $type = "adjust"
    ) {
        $appendUrl = 'inventory_levels/adjust.json';
        if ($type == "set") {
            $appendUrl = 'inventory_levels/set.json';
        }
        try {
            $postData = [
                'location_id'=> $locationId,
                'inventory_item_id'=> $inventoryItemId,
                'available_adjustment' => -$avilableAdjustment
            ];
            if ($type == 'set') {
                unset($postData['avilable_adjustment']);
                $postData['available'] = $avilableAdjustment;
            }
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            $this->curl->addHeader('content-type', 'application/json');
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->addHeader('x-shopify-access-token', $credentials['shopify_pwd']);
            }
            $url = "https://".$credentials['shopify_domain_name']."/admin/".$appendUrl;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_POSTFIELDS => $this->jsonEncoder($postData),
                CURLOPT_CUSTOMREQUEST => "POST",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("updateProductInventoryAtShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__(
                    'Something wrong while updating product qty after place order at shopfiy Shopify.'
                )
            ];
        }
    }

    /**
     * GetShopifyVariationFromVariationId function get the variant data from shopify
     *
     * @param array $shopifyShopConfiguration
     * @param int $shopifyVariationId
     * @param string $field
     * @return array
     */
    public function getShopifyVariationFromVariationId(
        $shopifyShopConfiguration = [],
        $shopifyVariationId = '',
        $field = ''
    ) {
        try {
            if ($shopifyShopConfiguration['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $shopifyShopConfiguration['access_token']
                );
            } else {
                $this->curl->setCredentials(
                    $credentials['shopify_api_key'],
                    $credentials['shopify_pwd']
                );
            }
            $url = "https://".$shopifyShopConfiguration['shopify_domain_name'].
                    "/admin/variants/".$shopifyVariationId.".json?fields=".$field;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getShopifyVariationFromVariationId :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while getting variant data from shopify, Please check the log.')
            ];
        }
    }

    /**
     * GetShopifyProductListFromShopifyProductId function get the shopify product list from shopify
     *
     * @param array $shopifyShopConfiguration
     * @param int $shopifyProductId
     * @param string $field
     * @return array
     */
    public function getShopifyProductListFromShopifyProductId(
        $shopifyShopConfiguration = [],
        $shopifyProductId = '',
        $field = ''
    ) {
        try {
            if ($shopifyShopConfiguration['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $shopifyShopConfiguration['access_token']
                );
            } else {
                $credentials = $this->decryptTheApiCredential(
                    $shopifyShopConfiguration['shopify_api_key'],
                    $shopifyShopConfiguration['shopify_pwd']
                );
                $this->curl->setCredentials(
                    $credentials['shopify_api_key'],
                    $credentials['shopify_pwd']
                );
            }
            $url = "https://".$shopifyShopConfiguration['shopify_domain_name'].
                    "/admin/products/".$shopifyProductId.".json?fields=".$field;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getShopifyProductListFromShopifyProductId :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while getting shopify product list, Please check the log.')
            ];
        }
    }

    /**
     * GetShopifyProductList function
     *
     * @param array $shopifyShopConfiguration
     * @return array
     */
    public function getShopifyProductList($shopifyShopConfiguration = [])
    {
        try {
            if ($shopifyShopConfiguration['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $this->decryptTheAccessToken($shopifyShopConfiguration['access_token'])
                );
            } else {
                $credentials = $this->decryptTheApiCredential(
                    $shopifyShopConfiguration['shopify_api_key'],
                    $shopifyShopConfiguration['shopify_pwd']
                );
                $this->curl->setCredentials(
                    $credentials['shopify_api_key'],
                    $credentials['shopify_pwd']
                );
            }
            $pageInfo = '';
            if (isset($shopifyShopConfiguration['page_info']) && $shopifyShopConfiguration['page_info'] !='') {
                $pageInfo = $shopifyShopConfiguration['page_info'];
                $pageInfo = $pageInfo != '' ? '&page_info='.$pageInfo : '';
            }
            if (isset($shopifyShopConfiguration['CategoryID']) && $shopifyShopConfiguration['CategoryID'] != '') {
                $url = "https://".$shopifyShopConfiguration['shopify_domain_name'].
                        "/admin/collections/".$shopifyShopConfiguration['CategoryID']."/products.json?limit=250";
            } else {
                $url = "https://".$shopifyShopConfiguration['shopify_domain_name']."/admin/products.json?limit=250";
            }
            $url = $url.$pageInfo;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                $products = $this->jsonDecoder($this->curl->getBody());
                //return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
                return [
                    'status'=> true,
                    'next_page_info' => $this->getResponsePageInfo(),
                    'total_imported' => count($products['products']) == 0 ? 1 : 0,
                    'data'=> $products,
                ];

            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getShopifyProductListFromShopifyProductId :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while getting shopify product list, Please check the log.')
            ];
        }
    }

    /**
     * CreateMageProductInShopify function create the magento product in shopify shop
     *
     * @param array $item
     * @param int $shopId
     * @return array
     */
    public function createMageProductInShopify($item = [], $shopId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            $url = "https://".$credentials['shopify_domain_name']."/admin/products.json";
            $this->curl->addHeader('content-type', 'application/json');
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->addHeader('x-shopify-access-token', $credentials['shopify_pwd']);
            }
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_POSTFIELDS => $this->jsonEncoder($item),
                CURLOPT_CUSTOMREQUEST => "POST",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200 && $this->curl->getStatus() != 201 && $this->curl->getStatus() != 100) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200 || $this->curl->getStatus() == 201 || $this->curl->getStatus() == 100) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("createMageProductInShopify :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__(
                    'Something wrong while creating Magento product at shopify end, Please check the log.'
                )
            ];
        }
    }

    /**
     * AssignShopifyProductToShopifyCategory function assign the shopify product to shopify collection
     *
     * @param array $data
     * @param int $shopId
     * @return array
     */
    public function assignShopifyProductToShopifyCategory($data = [], $shopId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            $url = "https://".$credentials['shopify_domain_name']."/admin/collects.json";
            $this->curl->addHeader('content-type', 'application/json');
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->addHeader('x-shopify-access-token', $credentials['shopify_pwd']);
            }
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_POSTFIELDS => $this->jsonEncoder($data),
                CURLOPT_CUSTOMREQUEST => "POST",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200 && $this->curl->getStatus() != 201) {
                $this->deleteShopifyProduct($data['collect']['product_id'], $shopId);
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200 || $this->curl->getStatus() == 201) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            if (isset($data['collect']['product_id'])) {
                $this->deleteShopifyProduct($data['collect']['product_id'], $shopId);
            }
            $this->logCriticalMessage("Helper Data assignShopifyProductToShopifyCategory :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while assigining Shopify product to collection, Please check the log.')
            ];
        }
    }

     /**
      * Delete Shopify Product
      *
      * @param int $shopifyProductId
      * @param int $shopId
      * @return array
      */
    public function deleteShopifyProduct($shopifyProductId, $shopId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($shopId, true);
            $url = "https://".$credentials['shopify_domain_name']."/admin/products/".$shopifyProductId.".json";
            $this->curl->addHeader('content-type', 'application/json');
            if ($credentials['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $credentials['access_token']
                );
            } else {
                $this->curl->addHeader('x-shopify-access-token', $credentials['shopify_pwd']);
            }
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_POSTFIELDS => $this->jsonEncoder($data),
                CURLOPT_CUSTOMREQUEST => "DELETE",
            ];
            $this->curl->setOptions($arr);
            $this->curl->get($url);
            if ($this->curl->getStatus() != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            if ($this->curl->getStatus() == 200) {
                return ['status'=> true, 'data'=> $this->jsonDecoder($this->curl->getBody())];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data deleteShopifyProduct :".$e->getMessage());
            return [
              'status'=>false,
              'error_msg'=>__('Something wrong while deleting Shopify product, Please check the log.')
            ];
        }
    }

    /**
     * Decodes the given $string string which is
     *
     * Encoded in the JSON format
     *
     * @param string $string
     * @return array
     */
    public function jsonDecoder($string = '')
    {
        $decodedData = $this->jsonHelper->jsonDecode($string);
        return $decodedData;
    }

    /**
     * Encodes the given $arr array which is
     *
     * Encoded in the array format
     *
     * @param string $arr
     * @return array
     */
    public function jsonEncoder($arr = [])
    {
        $encodedData = $this->jsonHelper->jsonEncode($arr);
        return $encodedData;
    }

    /**
     * Get array of perform operation
     *
     * @return array
     */
    public function getOperations()
    {
        return self::$operation;
    }

    /**
     * Get opeation type
     *
     * @return array
     */
    public function getOperationsTypes()
    {
        return self::$operationType;
    }

    /**
     * Get rule status
     *
     * @return array
     */
    public function getStatus()
    {
        return self::$status;
    }

    /**
     * Get the status of product qty update at shopify shop after place order at magento from configuration
     *
     * @return void
     */
    public function getStatusOfProQtyUpdate()
    {
        return $this->_scopeConfig->getValue(
            'mpmultishopifystoremageconnect/general_settings/shopify_product_qty_update',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get the status of product update at shopify shop after place product update at magento from configuration
     *
     * @return void
     */
    public function getStatusOfProUpdate()
    {
        return $this->_scopeConfig->getValue(
            'mpmultishopifystoremageconnect/general_settings/shopify_product_update',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get price after applied price rule
     *
     * @param int $price
     * @param string $process
     * @return void
     */
    public function getPriceAfterAppliedRule($price, $process)
    {
        try {
            $ruleData = $this->getPriceRuleByPrice($price);
            if ($price && $ruleData) {
                if ($ruleData->getSku() === 'Fixed') {
                    $price = $this->getFixedPriceCalculation($ruleData, $price, $process);
                } else {
                    $price = $this->getPercentPriceCalculation($ruleData, $price, $process);
                }
            }
            return $price;
        } catch (\Exception $e) {
            $this->logger->info('Helper Data getPriceAfterAppliedRule : '.$e->getMessage());
        }
    }

    /**
     * Done fixed price rule calcuation
     *
     * @param object $ruleData
     * @param int $price
     * @param string $process
     * @return int
     */
    public function getFixedPriceCalculation($ruleData, $price, $process)
    {
        try {
            if ($ruleData->getOperation() === 'Increase') {
                if ($process === $this->getPriceRuleOn($this->ruleId)) {
                    $price = $price + $ruleData->getRate();
                } else {
                    return $price;
                }
            } else {
                if ($process === $this->getPriceRuleOn($this->ruleId)) {
                    $price = $price - $ruleData->getRate();
                } else {
                    return $price;
                }
            }
            return $price;
        } catch (\Exception $e) {
            $this->logger->info('Helper Data getFixedPriceCalculation : '.$e->getMessage());
        }
    }

    /**
     * Done percent price rule calcuation
     *
     * @param object $ruleData
     * @param int $price
     * @param string $process
     * @return int
     */
    public function getPercentPriceCalculation($ruleData, $price, $process)
    {
        try {
            $percentPrice = ($price * $ruleData->getRate())/100;
            if ($ruleData->getOperation() === 'Increase') {
                if ($process === $this->getPriceRuleOn($this->ruleId)) {
                    $price = $price + $percentPrice;
                } else {
                    return $price;
                }
            } else {
                if ($process === $this->getPriceRuleOn($this->ruleId)) {
                    $price = $price - $percentPrice;
                } else {
                    return $price;
                }
            }
            return $price;
        } catch (\Exception $e) {
            $this->logger->info('Helper Data getPercentPriceCalculation : '.$e->getMessage());
        }
    }

    /**
     * Get price rule by price
     *
     * @param integer $price
     * @return object
     */
    public function getPriceRuleByPrice($price)
    {
        $shopifyPriceRule = $this->priceRule
                ->create()
                ->getCollection()
                ->addFieldToFilter('shopify_account_id', ['eq' => $this->ruleId])
                ->addFieldToFilter('price_from', ['lteq' => round($price)])
                ->addFieldToFilter('price_to', ['gteq' => round($price)])
                ->addFieldToFilter('status', ['eq' => 1])->setPageSize(1);
        if ($shopifyPriceRule->getSize()) {
            return $shopifyPriceRule->getFirstItem();
        }
        return false;
    }

    /**
     * Get all stores of shopify
     *
     * @return array
     */
    public function getAllShopifyStores()
    {
        $shopifystores = [];
        $accountCol = $this->_shopifyaccountsFactory->create()->getCollection();
        foreach ($accountCol as $account) {
            $shopifystores[$account->getId()] = $account->getStoreName();
        }
        return $shopifystores;
    }

    /**
     * GetTopLevelMageCategory function
     *
     * @return array
     */
    public function getTopLevelMageCategory()
    {
        return $this->rootCat->create()->tomageCatArray();
    }

    /**
     * GetTopLevelShopifyCategory function
     *
     * @return array
     */
    public function getTopLevelShopifyCategory()
    {
        return $this->rootCat->create()->toshopifyCatArray();
    }

    /**
     * Is Mage Category Mapped
     *
     * @param int $leafMageCategory
     * @param int $ruleId
     * @return bool
     */
    public function isMageCategoryMapped($leafMageCategory, $ruleId)
    {
        $shopifyExistCatColl = $this->shopifyCategoryMapRepository
            ->getCollectionByMageCateIdnRuleId($leafMageCategory, $ruleId);

        if ($shopifyExistCatColl->getSize()) {
            foreach ($shopifyExistCatColl as $shopifyExistCat) {
                return $shopifyExistCat->getEntityId();
            }
        }
        return false;
    }

    /**
     * GetTheShopApiCredentials function get the shopify shop private app credentials
     *
     * @param int $id
     * @param boolean $isDecrypt
     * @return array
     */
    public function getTheShopApiCredentials($id = "", $isDecrypt = false)
    {
        $shopifyAccountsModel = $this->_shopifyaccountsFactory->create();
        $shopifyAccountsModel->load($id);
        $apiKey = $shopifyAccountsModel->getShopifyApiKey();
        $pwd = $shopifyAccountsModel->getShopifyPwd();
        $domainName = $shopifyAccountsModel->getShopifyDomainName();
        $shopifyApp = $shopifyAccountsModel->getShopifyAppSelect();
        $accessToken = $shopifyAccountsModel->getAccessToken();
        if ($shopifyApp == 1) {
            return [
                'access_token'=> $isDecrypt ? $this->decryptTheAccessToken($accessToken) : $accessToken,
                'shopify_domain_name'=> $domainName,
                'shopify_app_select' => $shopifyApp
            ];
        }
        if (!$isDecrypt) {
            return [
                'shopify_api_key'=> $apiKey,
                'shopify_pwd'=> $pwd,
                'shopify_domain_name'=> $domainName,
                'shopify_app_select' => $shopifyApp
            ];
        }
        $cred = $this->decryptTheApiCredential($apiKey, $pwd);
        return [
            'shopify_api_key'=> $cred['shopify_api_key'],
            'shopify_pwd'=> $cred['shopify_pwd'],
            'shopify_domain_name'=> $domainName,
            'shopify_app_select' => $shopifyApp
        ];
    }

    /**
     * GetSpecificKeyValueFromArray function get the specific value from arry with match key
     *
     * @param array $array
     * @param string $matchKey
     * @return void
     */
    public function getSpecificKeyValueFromArray($array = [], $matchKey = '')
    {
        $arr = [];
        foreach ($array as $key => $data) {
            foreach ($data as $key => $value) {
                if ($key == $matchKey) {
                    $arr[] = $value;
                }
            }
        }
        return $arr;
    }

    /**
     * Get Store Shopify Cat Map Data
     *
     * @param int $shopifyCategory
     * @param int $ruleId
     * @return fixed
     */
    public function getStorShopifyCatMapData($shopifyCategory, $ruleId)
    {

        $shopifyExistCatColl = $this->shopifyCategoryMapRepository
            ->getCollectionByShopifyCateIdnRuleId($shopifyCategory, $ruleId);
        if ($shopifyExistCatColl->getSize()) {
            foreach ($shopifyExistCatColl as $shopifyExistCat) {
                return $shopifyExistCat;
            }
        }
        return false;
    }

    /**
     * Get mapped category data
     *
     * @param int $ruleId
     * @return void
     */
    public function getMappedCategoryData($ruleId)
    {
        $shopifyExistCatColl = $this->shopifyCategoryMapRepository->getCollectionByRuleId($ruleId);

        if ($shopifyExistCatColl->getSize()) {
            return $shopifyExistCatColl->toArray();
        }
        return false;
    }

    /**
     * Get Template data
     *
     * @param int $ruleId
     * @return array
     */
    public function getTemplateByRuleId($ruleId)
    {
        $shopifyAccountsModel = $this->_shopifyaccountsFactory->create();
        $shopifyAccountsModel->load($ruleId);
        return $shopifyAccountsModel->getTemplateId();
    }

    /**
     * Get count of imported items.
     *
     * @param string $itemType
     * @param int $ruleId
     * @return int
     */
    public function getTotalImportedCount($itemType, $ruleId)
    {
        $tempProCollection = $this->importedTmpProductRepository
            ->getCollectionByProductTypeAndRuleId($itemType, $ruleId);

        return $tempProCollection->getSize();
    }

    /**
     * Save data in table
     *
     * @param  array $tableName
     * @param  array $completeWellFormedData
     * @return null
     */
    public function insertDataInBulk($tableName, $completeWellFormedData = [])
    {
        try {
            if (!empty($completeWellFormedData)) {
                    return $this->dbStorage->insertMultiple($completeWellFormedData, $tableName);
            }
        } catch (\Exception $e) {
            $this->logger->info('ManageProductRawData insertDataInBulk : '.$e->getMessage());
        }
    }

    /**
     * Return Congigurable associated product id.
     *
     * @param object $productId
     * @param array $nameValueList
     * @param int $ruleId
     * @return bool|int
     */
    public function getConfAssoProductId($productId, $nameValueList, $ruleId)
    {
        $assocateProId = false;
        //$product = $this->product->create()->load($productId);
        $productObj = $this->productRepository->get($nameValueList["sku"]);

        return $productObj->getId();
    }

    /**
     * Check for Valid Sku to Upload Product.
     *
     * @param int|string $sku
     * @return bool
     */
    public function isValidSku($sku)
    {
        if ($sku == '') {
            return false;
        } else {
            return $this->product->create()->getIdBySku($sku) ? false : true;
        }
    }

    /**
     * Save Simple Product.
     *
     * @param array $proDataReq
     * @param int $ruleId
     * @param int $isAssociateProduct
     * @param array $attributeValues
     * @param array $assocatedPro
     * @param int $viaListener
     * @return array
     */
    public function saveSimpleProduct(
        $proDataReq,
        $ruleId = 0,
        $isAssociateProduct = 0,
        $attributeValues = [],
        $assocatedPro = [],
        $viaListener = 0
    ) {
        $defaultWebsiteId = $this->getDefaultWebsite($ruleId);
        $inventorySource = $this->scopeConfig->getValue(
            'mpmultishopifystoremageconnect/general_settings/default_source',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        $proData = $proDataReq->getParams();
        $result = ['error' => 0];
        $hasWeight = 1;
        $categoryIds = [];
        $sku = $isAssociateProduct ? $assocatedPro['sku'] : $proData['sku'];
        if ($this->isValidSku($proData['sku'])) {
            $wholeData = [
                            'form_key' => $this->formkey->getFormKey(),
                            'type' => $proData['type_id'],
                            'new-variations-attribute-set-id' => $proData['attribute_set_id'],
                            'set' => $proData['attribute_set_id']
                        ];
            $wholeData['product']['website_ids'] = [$defaultWebsiteId];
            $wholeData['product']['name'] = $proData['name'];
            $wholeData['product']['sku'] = $proData['sku'];
            $wholeData['product']['price'] = $proData['price'];
            $wholeData['product']['tax_class_id'] = $proData['tax_class_id'];
            $wholeData['product']['quantity_and_stock_status']['qty'] = isset($proData['stock'])?$proData['stock']: 1;
            $wholeData['product']['quantity_and_stock_status']['is_in_stock'] = $proData['is_in_stock'];
            $wholeData['product']['product_has_weight'] = $proData['weight'] ? true : false;
            $wholeData['product']['weight'] = $proData['weight'];
            $wholeData['product']['category_ids'] = $proData['category'];
            $wholeData['product']['description'] = $proData['description'];
            $wholeData['product']['status'] = $proData['status'];
            $wholeData['product']['visibility'] = 4;
            $wholeData['product']['stock_data']['manage_stock'] = 1;
            $wholeData['product']['stock_data']['use_config_manage_stock'] = 1;
            $wholeData['product']['stock_data']['qty'] = isset($proData['stock']) ? $proData['stock'] : 1;
            $wholeData['product']['stock_data']['inventory_source'] = $inventorySource;
            if (isset($proData['supperattr']) && !empty($proData['supperattr'])) {
                $wholeData['product']['supperattr'] = $proData['supperattr'];
                foreach ($proData['supperattr'] as $mageAttrCode) {
                    $attrInfo = $this->_getAttributeInfo($mageAttrCode);
                    if ($attrInfo) {
                        $wholeData['product']['attributes'][] = $attrInfo->getAttributeId();
                    }
                }
            }
            if (isset($proData['specification'])) {
                $brandCategory = ['433'=>'4','661'=>'6','688'=>'7','852'=>'8','483'=>'5'];
                $shopify_brand = isset($proData['specification']['shopify_brand']) ?
                                    $proData['specification']['shopify_brand'] :
                                    '' ;
                if (isset($brandCategory[$shopify_brand]) &&
                    $brandCategory[$proData['specification']['shopify_brand']] !=""
                ) {
                    $category = $brandCategory[$shopify_brand];
                } else {
                    $category = 9;
                }
                $wholeData['product']['category_ids'] = array_push($proData['category'], $category);
                foreach ($proData['specification'] as $attrCode => $value) {
                    $wholeData['product'][$attrCode] = $value;
                }
            }
            if ($isAssociateProduct == 1) {
                foreach ($attributeValues as $code => $value) {
                    $wholeData['product'][$code] = $value;
                }
                $wholeData['product']['visibility'] = 1;
                if (!empty($assocatedPro)) {
                    $wholeData['type'] = 'simple';
                    $wholeData['product']['weight'] = $assocatedPro['weight'];
                    $wholeData['product']['sku'] = $assocatedPro['sku'];
                    $wholeData['product']['price'] = (float) $assocatedPro['price'];
                    $wholeData['product']['tax_class_id'] = trim($assocatedPro['tax_class_id']);
                    $wholeData['product']['quantity_and_stock_status']['qty'] = $assocatedPro['qty'];
                    $wholeData['product']['quantity_and_stock_status']['is_in_stock'] = 1;
                }
            }
            try {
                foreach ($wholeData as $key => $value) {
                    $proDataReq->setPostValue($key, $value);
                }
                $productId = (int) $this->saveProduct->saveProductData($proDataReq);
                if ($productId && isset($proData['image_data']) && !empty($proData['image_data'])) {
                    $imageData = [];
                    /*$autoImportImg = $this->scopeConfig->getValue(
                        'mpebaymagentoconnect/ebay_event/auto_image_import',
                        \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                    );*/
                    $autoImportImg = 0;
                    foreach ($proData['image_data']['images'] as $image) {
                        $isDefault = $image == $proData['image_data']['default'] ? 1 : 0;
                        if ($viaListener && $autoImportImg) {
                            $this->addImages($productId, $image, $isDefault);
                        } else {
                            $imageData[] = [
                                'magento_pro_id' => $productId,
                                'image_url' => $image,
                                'is_default'=> $isDefault,
                                'rule_id' => $ruleId
                            ];
                        }
                    }

                    $d = $this->dbStorage->insertMultiple($imageData, 'wk_mpshopify_product_image');
                }
            } catch (\Execption $e) {
                $productId = 0;
            }
            $result = $productId ? ['error' => 0, 'product_id' => $productId]:
                                    [
                                        'error' => 1,
                                        'msg' => 'Skipped '.$proData['name'].'. error in importing product.'
                                    ];
            if (isset($result['product_id']) && $proData['type_id'] == 'simple') {
                $result['shopify_variant_map'][] = [
                        'product_id' => $productId,
                        'variant_id' => $proData['shopify_variant_id']
                ];
                $this->assignProductToSeller(["0"=>$productId], $this->getMpSellerId($ruleId));
            }
        } elseif (isset($proData['revise']) && $proData['revise']) {
            $this->logger->info(' data revise saveSimple ');
            $this->logger->info($this->jsonEncoder($proData));
            $productId = $this->updateMageProduct($proDataReq, $isAssociateProduct, $attributeValues, $assocatedPro);
            $result = $productId ? ['error' => 0, 'product_id' => $productId]:
            [
                'error' => 1,
                'msg' => 'Skipped '.$proData['name'].'. error in importing product.'
            ];
        } else {
            $result['error'] = 1;
            $result['msg'] = 'Skipped '.$proData['name'].". sku '".$proData['sku']."' already exist.";
        }
        return $result;
    }

    /**
     * Revise product
     *
     * @param array $proDataReq
     * @param bool $isAssociateProduct
     * @param array $attributeValues
     * @param array $assocatedPro
     * @return void
     */
    public function updateMageProduct($proDataReq, $isAssociateProduct, $attributeValues, $assocatedPro)
    {
        try {
            $proData = $proDataReq->getParams();
            $proDataReq->clearParams();
            $result = ['error' => 0, 'product_id' => 0, 'error_list' => []];
            $hasWeight = 1;
            $categoryIds = [];
            $product = $this->productRepository->get($proData['sku'], true, 0);
            $productId = $product->getEntityId();
            $wholeData['id'] = $product->getEntityId();
            $wholeData['name'] = $proData['name'];
            $wholeData['sku'] = $proData['sku'];
            $wholeData['price'] = $proData['price'];
            if (isset($proData['special_price']) && $proData['special_price'] > 0) {
                $wholeData['special_price'] = $proData['special_price'];
            } else {
                $wholeData['special_price'] = null;
            }
            $proData['stock'] = $isAssociateProduct ? $assocatedPro['qty'] : $proData['stock'];
            $wholeData['quantity_and_stock_status']['qty'] = isset($proData['stock']) ? $proData['stock'] : 1;
            $wholeData['quantity_and_stock_status']['is_in_stock'] = $proData['is_in_stock'];
            $wholeData['product_has_weight'] = $proData['weight'] ? true : false;
            $wholeData['weight'] = $proData['weight'];
            $wholeData['category_ids'] = $proData['category'];
            $wholeData['description'] = $proData['description'];
            $wholeData['status'] = $proData['status'];
            $wholeData['store_id'] = 0;
            $wholeData['stock_data']['manage_stock'] = 1;
            $wholeData['stock_data']['use_config_manage_stock'] = 1;
            $wholeData['stock_data']['qty'] = isset($proData['stock']) ? $proData['stock'] : 1;

            if ($isAssociateProduct == 1) {
                $wholeData['visibility'] = 1;
                if (!empty($assocatedPro)) {
                    $product = $this->productRepository->get($assocatedPro['sku'], true, 0);
                    $productId = $product->getId();
                    $wholeData['id'] = $productId;
                    $wholeData['type'] = 'simple';
                    $wholeData['weight'] = $assocatedPro['weight'];
                    $wholeData['sku'] = $assocatedPro['sku'];
                    $wholeData['price'] = (float) $assocatedPro['price'];
                    if (isset($assocatedPro['special_price']) && $assocatedPro['special_price'] > 0) {
                        $wholeData['special_price'] = $assocatedPro['special_price'];
                    } else {
                        $wholeData['special_price'] = null;
                    }
                    $wholeData['quantity_and_stock_status']['qty'] = $assocatedPro['qty'];
                    $wholeData['quantity_and_stock_status']['is_in_stock'] = 1;
                    $wholeData['store_id'] = 0;
                }
            }
            try {
                $product->addData($wholeData);
                // $this->catalogSession->setShopifySession(1);
                $product->save();
                // $this->catalogSession->setShopifySession(0);
                $result['product_id'] = $productId;
            } catch (\Execption $e) {
                $this->logger->error('Product updateMageProduct error : '.$e->getMessage());
                $result = [
                    'error' => 1,
                    'product_id' => 0,
                    'error_list' => [
                        __('Skipped %1. error in importing product. %2', $proData['name'], $e->getMessage())
                    ]
                ];
            }
        } catch (\Exception $e) {
            $this->logger->info('Product updateMageProduct : '.$e->getMessage());
            $result = [
                'error' => 1,
                'product_id' => 0,
                'error_list' => [__('Skipped %1. error in importing product. %2', $proData['name'], $e->getMessage())]
            ];
        }
        return $result;
    }


    /**
     * Get assignProductToSeller
     *
     * @param array $proIds
     * @param int $sellerId
     * @return string
     */
    public function assignProductToSeller($proIds, $sellerId = 0)
    {
        if (!$sellerId) {
            $sellerId = $this->getSellerId();
        }
        if ($sellerId > 0) {
            foreach ($proIds as $productId) {
                $rowId = $this->productRepository->getById($productId)->getRowId();
                $model = $this->mpProductFactory->create();
                $model->setMageproductId($productId)
                        ->setMageProRowId($rowId)
                        ->setStatus(1)
                        ->setSellerId($sellerId)
                        ->setIsApproved(1)
                        ->setAdminPendingNotification(0)
                        ->setSellerPendingNotification(0)
                        ->setCreatedAt($this->_date->gmtDate())
                        ->setUpdatedAt($this->_date->gmtDate())
                        ->setStoreId(0)
                        ->save();
            }
        }
    }

    /**
     * Get NewImagePath
     *
     * @param string $imageUrl
     * @param string $productSku
     * @return string
     */
    private function _getNewImagePath($imageUrl, $productSku)
    {
        $path = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)
                                ->getAbsolutePath().'import/mpmultishopifystoremageconnect/';
        $imgSrcExplode = explode('?', $imageUrl);
        $imgSrcExplode = explode('/', $imgSrcExplode[0]);
        $imageType = substr(strrchr($imgSrcExplode[count($imgSrcExplode) - 1], '.'), 1);
        $imgPath = $path.sha1($imgSrcExplode[count($imgSrcExplode) - 1].$productSku)
                            .'.'.strtolower($imageType);
        return $imgPath;
    }

    /**
     * Add Images To Product.
     *
     * @param int          $productId
     * @param string|array $image
     * @param int          $isDefault
     */
    public function addImages($productId, $image, $isDefault)
    {
        try {
            $product = $this->productRepository->getById($productId, true, 0);
            $image = trim($image);
            if ($image != '') {
                $imgPath = $this->_getNewImagePath($image, $product->getSku());
                $this->saveImage($image, $imgPath);
                if ($this->file->isExists($imgPath)) {
                    $imgValues = $isDefault ? ['image', 'small_image', 'thumbnail']: [];
                    if (in_array('small_image', $imgValues)) {
                        $currentStoreId = $this->_storeManager->getStore()->getStoreId();
                        $storeId = $this->_storeManager->setCurrentStore(0);
                    }
                    if (function_exists('exif_imagetype')) {
                        $isPicture = exif_imagetype($imgPath) ? true : false;
                        if ($isPicture) {
                            $product->addImageToMediaGallery(
                                $imgPath,
                                $imgValues,
                                false,
                                false
                            );
                            $this->saveObject($product);
                        }
                    } else {
                        $product->addImageToMediaGallery(
                            $imgPath,
                            $imgValues,
                            false,
                            false
                        );
                        $this->saveObject($product);
                    }
                    if (in_array('small_image', $imgValues)) {
                        $this->_storeManager->setCurrentStore($currentStoreId);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('AddImage :'.$e->getMessage());
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    private function saveObject($object)
    {
        $object->save();
    }

    /**
     * Save image in store.
     *
     * @param string $inPath
     * @param string $outPath
     */
    public function saveImage($inPath, $outPath)
    {
        try {
            $browserStr = 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/525.13 '
                                    .'(KHTML, like Gecko) Chrome/0.A.B.C Safari/525.13';
            $option = [
                CURLOPT_USERAGENT => $browserStr,
                CURLOPT_RETURNTRANSFER => 1,
            ];
            $this->curl->setOptions($option);
            $this->curl->get($inPath);
            $response = $this->curl->getBody();
            $file = $this->file->fileOpen($outPath, 'w');
            if ($file === false) {
                $this->logCriticalMessage('Helper Data saveImage :- unable to open file');
                $this->file->deleteFile($outPath);
                return false;
            }
            $this->file->fileWrite($file, $response);
            $this->file->fileClose($file);
            return true;
        } catch (\Exception $e) {
            $this->logCriticalMessage('Helper Data saveImage :- '.$e->getMessage());
            return false;
        }
    }

    /**
     * Get MageSupperAttribute for get Attribute label with its value
     *
     * @param array $attrVals
     */
    private function _getSupAttrWithValue($attrVals)
    {
        $mageSupAttrs =[];
        foreach ($attrVals as $variation) {
            $mageSupAttrs[$variation['name']] = $variation['values'];
        }
        return $mageSupAttrs;
    }

    /**
     * GetAttributeCode function
     *
     * @param int $attrId
     * @return int
     */
    public function getAttributeCode($attrId)
    {
        $attribute = $this->attributeFactory->create()->load($attrId);
        $attributeId = $attribute->getAttributeCode();
        return $attribute->getAttributeCode();
    }

    /**
     * GetAttributeOptionText function
     *
     * @param int $optionId
     * @param int $attrId
     * @return string
     */
    public function getAttributeOptionText($optionId, $attrId)
    {
        $attribute = $this->attributeFactory->create()->load($attrId);
        $optionText = $attribute->getSource()->getOptionText($optionId);
        return $optionText;
    }

    /**
     * GetAttributeInfo
     *
     * @param string $mageAttrCode
     * @return false | Magento\Catalog\Model\ResourceModel\Eav\Attribute
     */
    private function _getAttributeInfo($mageAttrCode)
    {
        $attributeInfoColl = $this->attributeFactory->create()
                                    ->getCollection()
                                    ->addFieldToFilter(
                                        'attribute_code',
                                        ['eq' => $mageAttrCode]
                                    );
        $attributeInfo = false;
        foreach ($attributeInfoColl as $attrInfoData) {
            $attributeInfo = $attrInfoData;
        }
        return $attributeInfo;
    }

    /**
     * GetAttributeGroupId
     *
     * @param string $groupName
     * @param int $attributeSetId
     * @return array
     */
    private function getAttributeGroupId($groupName, $attributeSetId)
    {
        $group = $this->attrGroupCollection->create()
                                        ->addFieldToFilter('attribute_group_name', $groupName)
                                        ->addFieldToFilter('attribute_set_id', $attributeSetId)
                                        ->setPageSize(1)->getFirstItem();
        if (!$group->getAttributeGroupId()) {
            $data = [
                'attribute_group_name' => $groupName,
                'attribute_set_id' => $attributeSetId,
                'attribute_group_code' => sha1($groupName)
            ];
            $group = $group->setData($data)->save();
        }
        return $group->getId();
    }

    /**
     * CreateSuperAttrMagento return supper attributes code with values
     *
     * @param object $variations
     * @param object $attributeSetId
     * @return array
     */
    public function createSuperAttrMagento($variations, $attributeSetId)
    {
        $mapAttr = [];
        $option = [];
        try {
            $mageSupAttrs = $this->_getSupAttrWithValue($variations);
            $allStores = $this->_storeManager->getStores();
            foreach ($mageSupAttrs as $attrCode => $values) {
                $i = 0;
                if ($attrCode == '') {
                    continue;
                }
                $attributeCode = str_replace(' ', '_', $attrCode);
                $attributeCode = preg_replace('/[^A-Za-z0-9\_]/', '', $attributeCode);
                $mageAttrCode = substr('conf_'.strtolower($attributeCode), 0, 30);
                if ($mageAttrCode == 'conf_') {
                    continue;
                }
                $attributeInfo = $this->_getAttributeInfo($mageAttrCode);
                $mapAttr[$attrCode] = $mageAttrCode;
                $attributeSetId = $attributeSetId;
                $attributeGroupId = $this->getAttributeGroupId('Shopify Product Variation', $attributeSetId);
                if ($attributeInfo === false) {
                    $attributeScope = \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL;
                    $attrData = [
                        'entity_type_id' => 4,
                        'attribute_code' => $mageAttrCode,
                        'frontend_label' => [0 => $attrCode],
                        'attribute_group_id' => $attributeGroupId,
                        'attribute_set_id' => $attributeSetId,
                        'backend_type' => 'int',
                        'frontend_input' => 'select',
                        'global' => $attributeScope,
                        'visible' => true,
                        'required' => false,
                        'is_user_defined' => true,
                        'searchable' => false,
                        'filterable' => false,
                        'comparable' => false,
                        'visible_on_front' => true,
                        'visible_in_advanced_search' => false,
                        'unique' => false,
                    ];
                    $labels = [];
                    $labels[0] = $attrCode;
                    foreach ($allStores as $store) {
                        $labels[$store->getId()] = $attrCode;
                    }
                    $option = $this->_getAttributeOptions($mageAttrCode, $labels, $values);
                    try {
                        $attrData['option'] = $option;
                        $attribute = $this->attributeFactory->create();
                        $attributeIdNew = $attribute->setData($attrData)->save()->getId();

                        $attributeInfo = $this->_getAttributeInfo($mageAttrCode);
                        $option = $this->_getAttributeOptionsForEdit($attributeIdNew, $values);

                        $this->attributeManagement->assign(
                            'catalog_product',
                            $attributeSetId,
                            $attributeGroupId,
                            $mageAttrCode,
                            $attributeIdNew
                        );
                        if (isset($option['value'])) {
                            $attr = $this->productAttribute->get($attributeInfo->getAttributeCode());
                            $attr->setOption($option);
                            $this->productAttribute->save($attr);
                        }
                    } catch (\Exception $e) {
                        $this->logger->info($e->getMessage());
                    }
                } else {
                    try {
                        /****For get Attribute Options ****/
                        $option = $this->_getAttributeOptionsForEdit($attributeInfo->getAttributeId(), $values);

                        $this->attributeManagement->assign(
                            'catalog_product',
                            $attributeSetId,
                            $attributeGroupId,
                            $mageAttrCode,
                            $attributeInfo->getAttributeId()
                        );
                        if (isset($option['value'])) {
                            $attr = $this->productAttribute->get($attributeInfo->getAttributeCode());

                            $attr->setOption($option);
                            $this->productAttribute->save($attr);
                        }
                    } catch (\Exception $e) {
                        $this->logger->info('Create createSuperAttrMagento : '.$e->getMessage());
                    }
                    $option = [];
                }
            }
        } catch (\Exception $e) {
            $this->logger->info('Create createSuperAttrMagento : '.$e->getMessage());
            $mageSupAttrs = [];
        }
        return $mapAttr;
    }

    /**
     * Get AttributeOption
     *
     * @param string $mageAttrCode
     * @param array $labels
     * @param array $values
     * @return array
     */
    private function _getAttributeOptions($mageAttrCode, $labels, $values)
    {
        $allStores = $this->_storeManager->getStores();
        $attributeInfo = $this->_getAttributeInfo($mageAttrCode);
        $option = [];
        if ($attributeInfo) {
            $attribute = $this->attributeFactory->create()->load($attributeInfo->getAttributeId());
            $attribute->setStoreLabels($labels)->save();
            $option['attribute_id'] = $attribute->getAttributeId();
            foreach ($values as $key => $value) {
                $option['value']['wk'.$value][0] = $value;
                foreach ($allStores as $store) {
                    $option['value']['wk'.$value][$store->getId()] = $value;
                }
            }
        }
        return $option;
    }

    /**
     * Get AttributeOptionsForEdit
     *
     * @param string $mageAttrId
     * @param array $values
     * @return array
     */
    private function _getAttributeOptionsForEdit($mageAttrId, $values)
    {
        $attributeOptions = $this->attrOptionCollectionFactory->create()
                                            ->setPositionOrder('asc')
                                            ->setAttributeFilter($mageAttrId)
                                            ->setStoreFilter(0)->load();
        $optionsValues = [];
        foreach ($attributeOptions as $kay => $attributeOption) {
            array_push($optionsValues, strtolower($attributeOption->getDefaultValue()));
        }
        $allStores = $this->_storeManager->getStores();
        $option = [];
        $option['attribute_id'] = $mageAttrId;
        foreach ($values as $key => $value) {
            if (in_array(strtolower($value), $optionsValues) === false && $value != '' && $value != ' ') {
                $option['value']['wk'.$value][0] = $value;
                foreach ($allStores as $store) {
                    $option['value']['wk'.$value][$store->getId()] = $value;
                }
            }
        }
        return $option;
    }

    /**
     * Save Configurable Product.
     *
     * @param array $proDataReq
     * @return array
     */
    public function saveConfigProduct($proDataReq)
    {
        $proData = $proDataReq->getParams();
        $proDataReq->clearParams();
        $finalResult = ['error' => 0];
        $attributes = [];
        $associatedProductIds = [];
        $flag = true;
        $error = 0;
        $attributeCodetemp = '';
        try {
            if (!empty($proData['supperattr'])) {
                foreach ($proData['supperattr'] as $attributeCode) {
                    $attributeCode = trim($attributeCode);
                    $attributeId = $this->isValidAttribute($attributeCode);
                    if ($attributeId) {
                        $attributes[] = $attributeId;
                    } else {
                        $flag = false;
                        $attributeCodetemp = $attributeCodetemp.$attributeCode.',';
                        break;
                    }
                }
            } else {
                $flag = false;
            }
            if ($flag) {
                $errors = [];
                foreach ($proData as $key => $value) {
                    $proDataReq->setPostValue($key, $value);
                }
                $configResult = $this->addAssociatedProduct($proDataReq);
                $errorCount = 0;
                foreach ($configResult as $res) {
                    if (isset($res['error']) && $res['error'] == 1) {
                        ++$error;
                        $errors[] = $res['msg'];
                    } else {
                        $associatedProductIds[] = $res['product_id'];
                    }
                }
                if (!empty($associatedProductIds) && $error == 0) {
                    $proData['is_in_stock'] = 1;
                    foreach ($proData as $key => $value) {
                        $proDataReq->setPostValue($key, $value);
                    }
                    $result = $this->saveSimpleProduct($proDataReq, $this->ruleId);

                    if ($result['error'] == 0) {
                        $productId = $result['product_id'];
                        $this->completeConfigProduct($productId, $associatedProductIds, $attributes);
                        $finalResult['product_id'] = $productId;
                        $finalResult['shopify_sku'] = $proData['sku'];
                        $finalResult['shopify_variant_map'] = $configResult;
                    } else {
                        $finalResult['error'] = 1;
                        $finalResult['msg'] = $result['msg'];
                    }
                } else {
                    $finalResult['error'] = 1;
                    $msg = 'Unable to create associated products.';
                    $finalResult['msg'] = implode('<br>', $errors);
                    $finalResult['msg'] = $msg.$finalResult['msg'];
                }
                if ($error > 0) {
                    if (empty($associatedProductIds)) {
                        $msg = 'Unable to create associated products.<br>';
                        $finalResult['msg'] = implode('<br>', $errors);
                        $finalResult['msg'] = $msg.$finalResult['msg'];
                    } else {
                        $finalResult['msg'] = implode('<br>', $errors);
                    }
                }
            } else {
                $finalResult['msg'] = 'Some of super attribute is not valid for product '.$proData['name'];
                $finalResult['error'] = true;
            }
        } catch (\Exception $e) {
            $finalResult = [
                'error' => true,
                 'msg' => 'Some of super attribute is not valid for product'
            ];
            $this->logger->info('Helper Data saveConfigProduct : '.$e->getMessage());
        }
        return $finalResult;
    }

    /**
     * Add Associated Product to Configurabel Product After Creating Products.
     *
     * @param int   $productId
     * @param array $associatedProductIds
     * @param array $attributes
     */
    public function completeConfigProduct($productId, $associatedProductIds, $attributes)
    {
        try {
            $product = $this->product->create()->load($productId);
            if ($product->getTypeId() != 'configurable') {
                $count = 0;
                foreach ($attributes as $attributeId) {
                    $data = [
                        'attribute_id' => $attributeId,
                        'product_id' => $productId,
                        'position' => $count
                    ];
                    ++$count;
                    $this->configurableAttributeModel->setData($data)->save();
                }
                $attributeSetId = $this->getAttributeSetId($this->ruleId);
                $product->setTypeId('configurable');
                $product->setAffectConfigurableProductAttributes($attributeSetId);
                $this->configurableProTypeModel->setUsedProductAttributeIds($attributes, $product);
                $product->setNewVariationsAttributeSetId($attributeSetId);
                $product->setAssociatedProductIds($associatedProductIds);
                $product->setId($productId);
                $product->save();
                $product->setCanSaveConfigurableAttributes(1);
                $this->assignProductToSeller($associatedProductIds);
                $this->assignProductToSeller(["0"=>$productId]);
            }
            $product->save();
            $indexer = $this->indexerFactory->create()->load(self::CATALOGSEARCH_FULLTEXT);
            $indexer->reindexAll();
        } catch (\Exception $e) {
            $this->logger->info('Helper Data completeConfigProduct : '.$e->getMessage());
        }
    }

    /**
     * Check Attribute Code is Valid or Not for Configurable Product.
     *
     * @param string $attributeCode
     * @return bool
     */
    public function isValidAttribute($attributeCode)
    {
        $attribute = $this->attributeFactory->create()->getCollection()
                                           ->addFieldToFilter('attribute_code', ['eq' => $attributeCode])
                                           ->addFieldToFilter('frontend_input', 'select')
                                           ->getFirstItem();

        return $attribute->getId() ? $attribute->getId() : false;
    }

    /**
     * Create Associated Product of Configurable Product.
     *
     * @param array $proDataReq
     * @return array
     */
    public function addAssociatedProduct($proDataReq)
    {
        $proData = $proDataReq->getParams();
        $result = [];
        $parentData = [];
        $attributeValues = [];
        foreach ($proData['assocate_pro'] as $assocatedPro) {
            $flag = true;
            foreach ($proData['supperattr'] as $supAttrCode) {
                if (!isset($assocatedPro[$supAttrCode])) {
                    $flag = false;
                    break;
                } else {
                    $attributeValues[$supAttrCode] = $assocatedPro[$supAttrCode];
                }
            }
            if ($flag) {
                foreach ($proData as $key => $value) {
                    $proDataReq->setPostValue($key, $value);
                }
                $proInfo = $this->saveSimpleProduct($proDataReq, $this->ruleId, 1, $attributeValues, $assocatedPro);
                if (isset($proInfo['product_id'])) {
                    $result[] = [
                        'product_id'=> $proInfo['product_id'],
                        'variant_id'=> $assocatedPro['shopify_variant_id']
                    ];
                } else {
                    $this->logger->info('addAssociatedProduct : '.$proInfo['msg']);
                    $result[] = ['msg' => $proInfo['msg'], 'error' => 1];
                    continue;
                }
                $this->registry->unregister('product');
                $this->registry->unregister('current_product');
                $this->registry->unregister('current_store');
            } else {
                $result[] = [
                    'msg' => __('Some of super attribute is Not Valid for product ').$proData['name'],
                    'error' => 1
                ];
                return $result;
            }
        }

        return $result;
    }

    /**
     * Create Imported Shopify Order On Your Store.
     *
     * @param array $orderData
     * @return array
     */
    public function createMageOrder($orderData)
    {
        if (!$this->orderStatuses->isStatusActive('processing')) {
            $this->logger->warning('Status is inactive or not permitted.');
            return;
        }
        try {
            $productNameError = '';
            if (!$orderData['shipping_address']['street'] || !$orderData['shipping_address']['country_id']) {
                return [
                    'error' => 1,
                    'msg' => __('Shopify order id ').$orderData['shopify_order_id'].__(' not contain address')
                ];
            }
            $storeId = $this->getDefaultStoreForOrderSync($this->ruleId);
            $store = $this->_storeManager->getStore($storeId);
            // create shopify customer
            $customer = $this->createShopifyCustomer($orderData, $this->ruleId);

            // prepare cart for shopify order
            $cartId = $this->cartManagementInterface->createEmptyCart();
            $quote = $this->cartRepositoryInterface->get($cartId);

            $quote->setStore($store);
            $this->backendSession->setShopifyOrder(1);
            $customer = $this->customerRepository->getById($customer->getEntityId());
            $quote->setCurrency();
            $quote->assignCustomer($customer);
            try {
                foreach ($orderData['items'] as $item) {
                    $product = $this->product->create()->load($item['product_id']);
                    $productNameError = $productNameError .' '. $product->getName().'( SKU : '.$product->getSku().')';
                    $product->setPrice($item['price']);
                    $product->setTaxClassId(0);
                    $quote->addProduct($product, (int)$item['qty']);
                }
            } catch (\Exception $e) {
                throw new LocalizedException(__("out of stock error"));
            }
            //Set Address to quote
            $shipRegionId = 0;
            $billRegionId = 0;
            $isRegionIdRequired = $this->checkRegionIdIsRequired($orderData['shipping_address']['country_id']);
            if ($isRegionIdRequired || 1) {
                $shipRegionId = $this->getRegionIdFrmRegionLable(
                    $orderData['shipping_address']['country_id'],
                    $orderData['shipping_address']['region']
                );
                $orderData['shipping_address']['region_id'] = $shipRegionId;
                $orderData['shipping_address']['telephone'] = 0000000000;

                $billRegionId = $this->getRegionIdFrmRegionLable(
                    $orderData['billing_address']['country_id'],
                    $orderData['billing_address']['region']
                );
                $orderData['billing_address']['region_id'] = $billRegionId;
                $orderData['billing_address']['telephone'] = 0000000000;
            }
            /*if (($shipRegionId == 0 || $billRegionId == 0) && $idRegionIdRequired) {
                throw new LocalizedException(__('region id is required field while creating order'));
            }*/
            $orderData['billing_address']['vat_id'] = $orderData['billing_address']['vat_id'] != '' ?
             $orderData['billing_address']['vat_id'] : 'XXXXXXXXXXX';
            $orderData['shipping_address']['vat_id'] = $orderData['shipping_address']['vat_id'] != '' ?
                $orderData['shipping_address']['vat_id'] : 'XXXXXXXXXXX';
            $quote->getBillingAddress()->addData($orderData['billing_address']);
            $quote->getShippingAddress()->addData($orderData['shipping_address']);
            // Collect Rates and Set Shipping & Payment Method
            $shipmethod = 'wk_multishopifyship_wk_multishopifyship';
            // Collect Rates and Set Shipping & Payment Method
            $this->shippingRate->setCode('wk_multishopifyship_wk_multishopifyship')->getPrice(1);

            //store shipping data in session
            $this->backendSession->setMultiShopifyShipDetail($orderData['shipping_service']);
            $this->backendSession->setShopifyTotalTaxAmount($orderData['total_tax']);
            $shippingAddress = $quote->getShippingAddress();
            $shippingAddress->setCollectShippingRates(true)
                            ->collectShippingRates()
                            ->setShippingMethod('wk_multishopifyship_wk_multishopifyship');
            $quote->getShippingAddress()->addShippingRate($this->shippingRate);
            $quote->setPaymentMethod('shopifypayment');
            $quote->setInventoryProcessed(false);

            // Set Sales Order Payment
            $quote->getPayment()->importData(['method' => 'shopifypayment']);
            // Collect Totals & Save Quote
            $quote->save();
            $quote->collectTotals();
            // Create Order From Quote
            $quote = $this->cartRepositoryInterface->get($quote->getId());
            $orderId = $this->cartManagementInterface->placeOrder($quote->getId());
            $this->backendSession->setShopifyOrder(0);
            $order = $this->order->load($orderId);
            $defaultOrderStatus = $this->getDefaultOrderStatus($this->ruleId);
            $order->setStatus($defaultOrderStatus)->save();
            $order->setState($defaultOrderStatus)->save();
            $order->setEmailSent(0);
            $order->setShopifyOrderId($orderData['shopify_order_id']);
            $order->save();
            $incrementId = $order->getRealOrderId();
            // Resource Clean-Up
            $quote = $customer = $service = null;
            if ($order->getEntityId()) {
                $result['order_id'] = $order->getRealOrderId();
            } else {
                $result = [
                    'error' => 1,
                    'msg' => __('Shopify order id ').$orderData['shopify_order_id'].__(' not created on your store')
                ];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage('Helper Data createMageOrder : '.$e->getMessage());
            $errorMsg = $e->getMessage();
            if ($e->getMessage() == 'out of stock error') {
                $errorMsg =  $productNameError. ' is out of stock. please increase the stock to create order.';
            }
            $result = [
                'error' => 1,
                'msg' => __('Shopify order id ').$orderData['shopify_order_id'].'--'.$errorMsg,
                'product_ids' => $this->jsonEncoder($orderData['items'])
            ];
        }
        return $result;
    }

    /**
     * CreateEmailFromPhonNumber function
     *
     * @param int $ruleId
     * @param array $orderData
     * @return null|string
     */
    public function createEmailFromPhonNumber($ruleId, $orderData = [])
    {
        if ($orderData['alternate_email_phone'] == null || $orderData['alternate_email_phone'] == '') {
            return null;
        }

        $shopifyConfiguration = $this->getShopifyConfiguration($ruleId);

        if ($shopifyConfiguration['shopify_domain_name'] == null ||
            $shopifyConfiguration['shopify_domain_name'] == ''
        ) {
            return null;
        }

        $email = $orderData['alternate_email_phone'].'@'.$shopifyConfiguration['shopify_domain_name'];
        $this->logInfoMessage('Helper Data createEmailFromPhonNumber :-'.$email);
        return $email;
    }

    /**
     * Create shopify customer if not exist
     *
     * @param int $orderData
     * @param array $ruleId
     * @return object
     */
    public function createShopifyCustomer($orderData, $ruleId)
    {
        try {
            $email = $orderData['email'];
            if ($email=="N/A" || strlen($email)<6) {
                $email = $orderData["shopify_order_id"]."@xyz.com";
            }
            if ($orderData['email'] == null || $orderData['email'] == '') {
                $email = $this->createEmailFromPhonNumber($ruleId, $orderDat);
            }
            $storeId = $this->getDefaultStoreForOrderSync($this->ruleId);
            $store = $this->_storeManager->getStore($storeId);
            $websiteId = $this->_storeManager->getStore($storeId)->getWebsiteId();
            $customer = $this->customerFactory->create();
            $customer->setWebsiteId($websiteId);
            $customer->loadByEmail($email);
            if (!$customer->getEntityId()) {
                $customer->setWebsiteId($websiteId)
                        ->setStore($store)
                        ->setFirstname($orderData['shipping_address']['firstname'])
                        ->setLastname($orderData['shipping_address']['lastname'])
                        ->setEmail($email)
                        ->setPassword($email);
                $customer->save();
            }
            return $customer;
        } catch (\Exception $e) {
            $this->logCriticalMessage('Helper Data createShopifyCustomer :-'.$e->getMessage());
            throw new LocalizedException(__($e->getMessage()));
        }
    }

    /**
     * CheckCountryCode function check weather country code is correct or not ?
     *
     * @param string $code
     * @return bool
     */
    public function checkCountryCode($code = "")
    {
        if ($code != "") {
            $countries = $this->countryHelper->toOptionArray();
            $countryCode = [];
            foreach ($countries as $countryKey => $country) {
                if ($country['value'] != '') {
                    $countryCode[] =  $country['value'];
                }
            }
            if (!in_array($code, $countryCode)) {
                return 1;
            }
                return 0;
        }
    }

    /**
     * CheckRegionIdIsRequired function check weather region id is required or not
     *
     * @param string $countryCode
     * @return boolean
     */
    public function checkRegionIdIsRequired($countryCode = '')
    {
        $status = false;
        if ($countryCode != '') {
            $countries = $this->countryHelper->toOptionArray();
            $countryCodeWithState = [];
            foreach ($countries as $countryKey => $country) {
                if ($country['value'] != '' && $country['value'] == $countryCode) {
                    if (isset($country['is_region_required']) && $country['is_region_required'] == 1) {
                        $status =  true;
                    }
                }
            }
                return $status;
        }
    }

    /**
     * GetRegionIdFrmRegionLable function get the region id code from the region label
     *
     * @param string $countryCode
     * @param string $regionLable
     * @return int
     */
    public function getRegionIdFrmRegionLable($countryCode = '', $regionLable = '')
    {
        $regionId = 0;
        if ($countryCode != "" && $regionLable!="") {
            $countries = $this->countryHelper->toOptionArray();
            $countryCodeWithState = [];
            foreach ($countries as $countryKey => $country) {
                if ($country['value'] != '' && $country['value'] == $countryCode) {
                    $ctryCode =  $country['value'];
                    $stateArray = $this->countryFactory->create()->setId(
                        $country['value']
                    )->getLoadedRegionCollection()->toOptionArray();
                    if (!empty($stateArray) && ($ctryCode == $countryCode)) {
                        foreach ($stateArray as $state) {
                            (($state['value'] != '') && ($state['label'] == $regionLable)) ?
                             $regionId = $state['value']:
                             null;
                        }
                    }
                }
            }
        }
        return $regionId;
    }

    /**
     * CheckRegionId function check that region id is correct?
     *
     * @param string $code
     * @param string $countryCode
     * @return boolean
     */
    public function checkRegionId($code = "", $countryCode = "")
    {
        if ($code != "" && $countryCode!="") {
            $countries = $this->countryHelper->toOptionArray();
            $countryCodeWithState = [];
            foreach ($countries as $countryKey => $country) {
                if ($country['value'] != '') {
                    $ctryCode =  $country['value'];
                    $stateArray = $this->countryFactory->create()->setId(
                        $country['value']
                    )->getLoadedRegionCollection()->toOptionArray();
                    if (!empty($stateArray) && ($ctryCode == $countryCode)) {
                        foreach ($stateArray as $state) {
                            $state['value'] != ''? ($countryCodeWithState[] = $state['value']): null;
                        }
                    }
                }
            }
            if (!in_array($code, $countryCodeWithState)) {
                return 1;
            }
            return 0;
        }
    }

    /**
     * GetPictureUrl use for get magento product image url.
     *
     * @param object $product
     * @return array
     */
    public function getPictureUrl($product)
    {
        $_productImagesList = $product->getMediaGalleryImages();
        $pictureUrl = [];
        foreach ($_productImagesList as $_image) {
            $pictureUrl[] = $_image->getUrl();
        }
        return $pictureUrl;
    }

    /**
     * GetProductDescription function use for get magento product description
     *
     * @param object $product
     * @param array $shopifyDefaultSetting
     * @param int $templateId
     * @param int $ruleId
     * @return string
     */
    public function getProductDescription($product, $shopifyDefaultSetting, $templateId, $ruleId = '')
    {
        try {
            if ($this->useTemplate($ruleId)) {
                $templateDetail = $this->listingTemplate->create()->load($templateId);

                $templateContent = $templateDetail->getTemplateContent();
                $mapAttributes = $this->jsonHelper->jsonDecode($templateDetail->getMappedAttribute());
                foreach ($mapAttributes as $key => $value) {
                    $attrValue = $product->getResource()->getAttribute($value['mage_attr']);
                    if ($attrValue) {
                        $attrValue = $attrValue->getFrontend()->getValue($product);
                        $templateContent = str_replace('#'.$value['temp_var'], (string)$attrValue, $templateContent);
                    }
                }
                $description = $templateContent;
            } else {
                $description = $product->getDescription();
            }
            $description = $this->templateProcessor->filter($description);
            $productDescription =  $this->isDesWithHtml($ruleId) ?
                                   $description :
                                   $this->filterManager->stripTags($description);
            if ($productDescription == '') {
                $productDescription = $product->getName();
            }
            return $productDescription;
        } catch (\Exception $e) {
            $this->logCriticalMessage('Helper Data getProductDescription '.$e->getMessage());
        }
    }

     /**
      * GetAccountDetailsBySellerId function
      *
      * @return string
      */
    public function getAccountDetailsBySellerId()
    {
        $sellerId = $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
        $sellerData = $this->_shopifyaccountsFactory->create()
                        ->getCollection()
                        ->addFieldToFilter("seller_id", ["eq"=>$sellerId]);
        return $sellerData;
    }

     /**
      * GetAccountDetailsByRuleId function
      *
      * @return string
      */
      public function getAccBySeller($sellerId)
      {
          $sellerData = $this->_shopifyaccountsFactory->create()
                          ->getCollection()
                          ->addFieldToFilter("seller_id", ["eq"=>$sellerId])
                          ->getFirstItem()->getData();
          return $sellerData;
      }

    /**
     * Return options array.
     *
     * @param int $id
     * @return array
     */
    public function getCategData($id)
    {
        $categoriesArr = [];//[] = ['value' => '','label' => 'Select Shopify Category'];
        $shopifyCategories = $this->getTheCustomCollectionFromShopify($id);
        if (isset($shopifyCategories['data']['custom_collections'])) {
            foreach ($shopifyCategories['data']['custom_collections'] as $category) {
                $categoriesArr[] = ['value' => $category['id'],'label' => $category['title']];
            }
        }
        return $categoriesArr;
    }

     /**
      * Return ccountDetails
      *
      * @return array
      */
    public function getAccountDetails()
    {
        $data['store_name'] = '';
        $data['attribute_set_id'] = '';
        $data['shopify_app_select'] = '';
        $data['access_token'] = '';
        $data['shopify_api_key'] = '';
        $data['shopify_pwd'] = '';
        $data['shopify_domain_name'] = '';
        $data['currency_conv_rate'] = '';
        $data['default_qty'] = '';
        $data['other_info'] = '';
        $data["default_cate"] = '';
        $data["default_store_view"] = '';
        $data["import_product"] = '';
        $data["item_with_html"] = "0";
        $data["price_rule_on"] = '';
        $data["order_status"] = '';
        $data["currency_conv_rate"] = '';
        $data["default_qty"] = '';
        $data["template_id"] = '';
        $data["product_type_allowed"] = "";
        $data["product_update_webhook"] = "0";
        $data["order_update_webhook"] = "0";
        $data["product_images_import_with"] = "0";
        $data['webhook_verify_key'] = '';

        $sellerData = $this->getAccountDetailsBySellerId();
        if ($sellerData->getSize()>0) {
            $data = $sellerData->getFirstItem()->getData();
            if ($data['shopify_app_select']==1) {
                $this->curl->addHeader(
                    "X-Shopify-Access-Token",
                    $this->decryptTheAccessToken($data['access_token'])
                );
            } else {
                $decryptedVal = $this->decryptTheApiCredential($data["shopify_api_key"], $data["shopify_pwd"]);
                $data["shopify_api_key"] = $decryptedVal["shopify_api_key"];
                $data["shopify_pwd"] = $decryptedVal["shopify_pwd"];
            }
        }
        return $data;
    }

      /**
       * Return SellerId
       *
       * @return array
       */
    public function getSellerId()
    {
        return $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
    }

    /**
     * Get ResponsePageInfo
     *
     * @return string
     */
    private function getResponsePageInfo()
    {
        $headers = $this->curl->getHeaders();
        if (isset($headers['link'])) {
            $this->laminasUri->setQuery($headers['link']);
            $output = $this->laminasUri->getQueryAsArray();
            return $pageInfo = explode('>', $output['page_info'])[0];
        } else {
            return "";
        }
    }

    /**
     *  Get default website code
     *
     * @param int $shopId
     * @return string
     */
    public function getDefaultWebsiteCode($shopId = '')
    {
        $websiteId = $this->getDefaultWebsite($shopId);
        return $this->_storeManager->getWebsite($websiteId)->getCode();
    }

    /**
     * Get Attribute Set
     *
     * @param int $attributeSetId
     * @return AttributeSetInterface
     */
    public function getAttributeSet($attributeSetId)
    {
        try {
            $attributeSet = $this->attributeSetRepository->get($attributeSetId);
        } catch (\Exception $e) {
            $this->logger->critical('getAttributeSet : '. $e->getMessage());
        }

        return $attributeSet;
    }

}
