<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$_helper = $objectManager->create(\Webkul\Marketplace\Helper\Data::class);
$isPartner= $_helper->isSeller();
$urlInterface = $objectManager->get(Magento\Framework\UrlInterface::class);
$magentoCurrentUrl = $urlInterface->getCurrentUrl();
$isSellerGroup = $_helper->isSellerGroupModuleInstalled();
$shopifyHelper = $objectManager->create(\Webkul\MpMultiShopifyStoreMageConnect\Helper\Data::class);
$sellerDetails = $shopifyHelper->getAccountDetailsBySellerId();
$showAddit = false;
if ($sellerDetails->getSize()>0) {
    $showAddit = true;
}
$pickupEnable = false;
$accConnectUrl = $block->getUrl(
    'mpmultishopifystoremageconnect/account/connect',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapCategory = $block->getUrl(
    'mpmultishopifystoremageconnect/map/category',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapProduct = $block->getUrl(
    'mpmultishopifystoremageconnect/map/product',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapOrder = $block->getUrl(
    'mpmultishopifystoremageconnect/map/orders',
    ['_secure' => $block->getRequest()->isSecure()]
);
$exportProduct = $block->getUrl(
    'mpmultishopifystoremageconnect/export/products',
    ['_secure' => $block->getRequest()->isSecure()]
);
$templateView = $block->getUrl(
    'mpmultishopifystoremageconnect/templates/view',
    ['_secure' => $block->getRequest()->isSecure()]
);
$priceRule = $block->getUrl(
    'mpmultishopifystoremageconnect/pricerule/view',
    ['_secure' => $block->getRequest()->isSecure()]
);
?>
<style type="text/css">
    @media only screen and (max-width: 767px){

        .block-collapsible-nav.wk-mp-main{
            top: 20px;
        }
    }
</style>
<?php if ($pickupEnable && $isPartner) { ?>
    <?php if (($isSellerGroup && $_helper->isAllowedAction('mpmultishopifystoremageconnect/account/connect'))
                || !$isSellerGroup) { ?>
    <li 
class="level-0 <?=/* @noEscape */ strpos($magentoCurrentUrl, 'mpmultishopifystoremageconnect')? "current active":"";?>">
        <a href="#" onclick="return false;" class="">
            <span><?=/* @noEscape */ __('Mp Shopify Connect')?></span>
        </a>
        <div class="wk-mp-submenu">
            <strong class="wk-mp-submenu-title"><?=/* @noEscape */ __('Mp Shopify Connect')?></strong>
            <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
            <ul>
                <li data-ui-id="menu-webkul-marketplace-menu" class="item-menu  parent  level-1">
                    <strong class="wk-mp-submenu-group-title">
                        <span><?=/* @noEscape */ __('Menu')?></span>
                    </strong>
                    <div class="wk-mp-submenu">
                        <ul>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/account/connect'))
                              || !$isSellerGroup) { ?>
                                  <?php if ($pickupEnable) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $accConnectUrl ?>">
                                        <span><?=/* @noEscape */ __('Connect Account') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/map/category'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $mapCategory ?>">
                                        <span><?=/* @noEscape */ __('Map Categories') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/map/product'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) { ?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $mapProduct ?>">
                                        <span><?=/* @noEscape */ __('Map Products') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/map/orders'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) { ?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $mapOrder ?>">
                                        <span><?=/* @noEscape */ __('Map Orders') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/export/products'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) { ?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $exportProduct ?>">
                                        <span><?=/* @noEscape */ __('Export To Shopify') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/templates/view'))
                            || !$isSellerGroup) { ?>
                            <?php if ($pickupEnable) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $templateView ?>">
                                        <span><?=/* @noEscape */ __('Listing Template') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultishopifystoremageconnect/pricerule/view'))
                            || !$isSellerGroup) { ?>
                            <?php if ($pickupEnable) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $priceRule ?>">
                                        <span><?=/* @noEscape */ __('Price Rule') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </li>
<?php } ?>
<?php } ?>