<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCoupons
 * <AUTHOR> Software Private Limited
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\MpSellerCoupons\Controller\Coupon;

use Magento\Framework\View\Result\PageFactory;
use Webkul\MpSellerCoupons\Api\MpSellerCouponsRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use \Magento\Framework\App\Config\ScopeConfigInterface;
use \Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Controller\ResultFactory;

/**
 * Webkul MpSellerCoupons SendMail class.
 */
class SendMail extends \Magento\Framework\App\Action\Action
{
    /**
     * DB Storage table name
     */
    public const TABLE_NAME = 'mpseller_coupon_sendmail';

    public const XML_PATH_EMAIL_TEMPLATE = 'webkul_seller_coupon/email/sellercoupon_send_customer_template';

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var \Magento\Directory\Model\Currency
     */
    protected $_currencyModel;

    /**
     * @var Webkul\MpSellerCoupons\Helper\Data
     */
    protected $_helperData;

    /**
     * @var Webkul\MpSellerCoupons\Api\MpSellerCouponsRepositoryInterface
     */
    protected $_mpSellerCouponRepository;

    /**
     * @var \Webkul\MpSellerCoupons\Model\MpSellerCoupons
     */
    protected $_mpSellerCouponsModel;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $_timezoneInterface;

    /**
     * @var ResultFactory
     */
    protected $_resultPageFactory;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

    /**
     * @var \Webkul\Marketplace\Helper\Data $mpData
     */
    protected $mpData;

    /**
     * Init function
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Directory\Model\Currency $currencyModel
     * @param \Webkul\MpSellerCoupons\Helper\Data $helperData
     * @param MpSellerCouponsRepositoryInterface $mpSellerCouponRepository
     * @param \Webkul\MpSellerCoupons\Model\MpSellerCoupons $mpSellerCouponsModel
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezoneInterface
     * @param ResourceConnection $resource
     * @param pageFactory $PageFactory
     * @param \Magento\Customer\Model\Customer $customer
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     * @param \Webkul\MpSellerCoupons\Model\NotifySellersFactory $notifySellerFactory
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Webkul\MpSellerCoupons\Model\MpSellerEmailFactory $mpSellerEmail
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation
     * @param \Webkul\Marketplace\Helper\Data $mpData
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Directory\Model\Currency $currencyModel,
        \Webkul\MpSellerCoupons\Helper\Data $helperData,
        MpSellerCouponsRepositoryInterface $mpSellerCouponRepository,
        \Webkul\MpSellerCoupons\Model\MpSellerCoupons $mpSellerCouponsModel,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezoneInterface,
        ResourceConnection $resource,
        pageFactory $PageFactory,
        \Magento\Customer\Model\Customer $customer,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly TransportBuilder $transportBuilder,
        private readonly \Magento\Framework\MessageQueue\PublisherInterface $publisher,
        private readonly \Webkul\MpSellerCoupons\Model\NotifySellersFactory $notifySellerFactory,
        private readonly \Magento\Framework\Json\Helper\Data $jsonHelper,
        private readonly \Webkul\MpSellerCoupons\Model\MpSellerEmailFactory $mpSellerEmail,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        private readonly \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Webkul\Marketplace\Helper\Data $mpData
    ) {
        $this->_customerSession = $customerSession;
        $this->_storeManager = $storeManager;
        $this->_currencyModel = $currencyModel;
        $this->_helperData = $helperData;
        $this->_mpSellerCouponRepository = $mpSellerCouponRepository;
        $this->_mpSellerCouponsModel = $mpSellerCouponsModel;
        $this->_timezoneInterface = $timezoneInterface;
        $this->customer = $customer;
        $this->_url = $context->getUrl();
        $this->messageManager = $messageManager;
        $this->mpData = $mpData;
        parent::__construct($context);
    }

    /**
     * Send mail
     *
     * @return void
     */
    public function execute()
    {
        $tableName = self::TABLE_NAME;
        $data = [];
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
            $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->getRequest()->isPost()) {
            $formData = $this->getRequest()->getParams();
            $formData['seller_coupon'] = implode(',', $formData['seller_coupon']);

            foreach ($formData['email_id'] as $emailId) {
                $id[] = $this->updateData($formData, $emailId);
            }

            $this->publisher->publish(
                'notifysellers.massmail',
                $this->jsonHelper->jsonEncode($id)
            );
            if ($id) {
                $this->messageManager->addSuccess(
                    __('Your Mail was successfully Sent!!')
                );
            } else {
                $this->messageManager->addSuccess(
                    __('Something Went Wrong!!')
                );
            }
            return $this->resultRedirectFactory->create()->setPath(
                '*/index/customer',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }

    /**
     * Get converted time according to locale
     *
     * @param  timestmap $date
     * @return timestamp
     */
    public function getDateTimeAccordingTimeZone($date = false)
    {
        if (!$date) {
            $date = new \DateTime(date("Y-m-d H:i:s"));
        }
        $dateTimeAsTimeZone = $this->_timezoneInterface->date($date)
                                                       ->format('Y-m-d H:i:s');
        return $dateTimeAsTimeZone;
    }

    /**
     * MailToCustomer function
     *
     * @param array $data
     * @return void
     */
    public function sendMailToCustomer($data)
    {
        $emailTemplateVariables = [];
        $sellerCouponId = $data["coupon_code"];
        $templateId = $data["template_id"];
        $multiCoupon = explode(",", $sellerCouponId);
        foreach ($multiCoupon as $couponId) {
            $couponCollection = $this->_mpSellerCouponRepository->getCouponById($couponId);
            $couponCodes[] = $couponCollection->getCouponCode();
        }
        $couponCode = implode(',', $couponCodes);
        $emailIds = $data["customer_id"];

        $customer_data = $this->customer->getCollection()->addFieldToFilter('entity_id', ['eq'=>$emailIds]);
        if ($customer_data->getSize()) {
            foreach ($customer_data as $bdata) {
                $name = $bdata->getData("firstname").' '.$bdata->getData("lastname");
                $email = $bdata->getData("email");
            }
        }
        if ($templateId != 0) {
            $tempData = $this->mpSellerEmail->create()->getCollection()
                ->addFieldToFilter('entity_id', $templateId);
            if ($tempData->getSize()) {
                foreach ($tempData as $tempdata) {
                    $tempname = $tempdata->getData("template_name");
                    $tempsub = $tempdata->getData("template_subject");
                    $tempcontent = $tempdata->getData("template_content");
                }
            }
        }
        $couponUrl = $this->_url->getUrl(
            'checkout/cart',
            ['code' => $couponCode]
        );
        try {
            $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
            $recipient = $email;
            $sellerId = $data["seller_id"];
            $sellerData = $this->customer->getCollection()->addFieldToFilter('entity_id', ['eq'=>$sellerId]);
            if ($sellerData->getSize()) {
                foreach ($sellerData as $sdata) {
                    $sellerName = $sdata->getData("firstname").' '.$sdata->getData("lastname");
                    $sender = $sdata->getData("email");
                }
            }
            $this->scopeConfig->getValue(self::XML_PATH_EMAIL_TEMPLATE, $storeScope);
            $isValid = $this->_helperData->getSendCouponLink();
            $emailTemplateVariables['code'] = $couponCode;
            $emailTemplateVariables['customer_name'] =$name;
            $emailTemplateVariables['approvalLink'] =($isValid==1 ? 1 : "");
            $emailTemplateVariables['cartLink'] =$couponUrl;

            $adminEmail = $this->mpData->getAdminEmailId();

            $adminName = $this->scopeConfig->getValue(
                'marketplace/general_settings/admin_name',
                $storeScope
            );
            $senderInfo['email'] = $adminEmail;
            $senderInfo['name'] = $adminName;
            $receiverInfo = [
                'name' => $this->jsonHelper->jsonDecode($this->jsonHelper->jsonEncode($name)),
                'email' => $recipient,
            ];
            if ($recipient != '') {
                $this->inlineTranslation->suspend();
                $templateHtml = $this->scopeConfig->getValue(self::XML_PATH_EMAIL_TEMPLATE, $storeScope);
                if ($templateId != 0) {
                    $templateHtml = "webkul_seller_coupon_email_sellercoupon_send_custom_template";
                    $emailTemplateVariables['temp_name'] =$tempname;
                    $emailTemplateVariables['temp_sub'] =$tempsub;

                    $tempcontent = str_replace("{couponcode}", $couponCode, $tempcontent);
                    $emailTemplateVariables['temp_content'] =$tempcontent;
                }
                $transport = $this->transportBuilder
                    ->setTemplateIdentifier($templateHtml)
                    ->setTemplateOptions(
                        [
                            'area' => 'frontend',
                            'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
                        ]
                    )
                    ->setTemplateVars($emailTemplateVariables)
                    ->setFrom($senderInfo)
                    ->addTo($receiverInfo['email'], $receiverInfo['name'])
                    ->getTransport();

                $transport->sendMessage();

                return true;
            }
        } catch (\Exception $e) {
            $this->mpData->logDataInLogger("mail error coupon -".$e->getMessage());
        }
        $this->inlineTranslation->resume();
    }

    /**
     * UpdateData function
     *
     * @param array $formData
     * @param string $emailId
     * @return void
     */
    public function updateData($formData, $emailId)
    {
        $tableName = self::TABLE_NAME;
        $data = [];
        $notifySellerCollection = $this->notifySellerFactory->create();
        if ($formData) {
            $sellerId = $this->_customerSession
                        ->getCustomer()->getId();
            $email = $emailId;
            $coupon_code = $formData['seller_coupon'];
            $template_id = $formData['template_id'];
            $createdAt = $this->getDateTimeAccordingTimeZone();
            $status = 1;

            $notifySellerCollection->addData([
                'coupon_code'   =>$coupon_code,
                'seller_id'     =>$sellerId,
                'template_id'   =>$template_id,
                'customer_id'   =>$email,
                'status'        =>$status,
                'created_at'    =>$createdAt
            ]);
            $saveData = $notifySellerCollection->save();
            return $notifySellerCollection->getId();
        }
    }
}
