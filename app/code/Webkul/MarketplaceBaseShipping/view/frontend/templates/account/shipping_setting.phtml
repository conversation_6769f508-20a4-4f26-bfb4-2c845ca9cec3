
<?php
/**
 * Webkul Software
 *
 * @category Webkul
 * @package Webkul_MarketplaceBaseShipping
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license https://store.webkul.com/license.html
 */

?>
<?php
/** @var Webkul\MarketplaceBaseShipping\Block\ShippingSetting $block */
$_company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class);
$_telephone = $block->getLayout()->createBlock(Magento\Customer\Block\Widget\Telephone::class);
$countryRestriction = $block->getCountryRestrictionsHelper();
?>
<form
      action="<?= $block->escapeUrl($block->getSaveUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">

    <div class="wk-mp-page-title page-title">
        <h2 class="legend"><?= /* @noEscape */ __('Shipping Origin Address') ?></h2>
        <button type="submit" class="button wk-mp-btn action save primary">
            <span><span><?= /* @noEscape */ __('Save') ?></span></span>
        </button>
    </div>
    <fieldset class="fieldset info">

        <?php if ($_company->isEnabled()): ?>
            <?= $_company->setCompany($block->getAddress()->getCompany())->toHtml() ?>
        <?php endif ?>

        <?php if ($_telephone->isEnabled()): ?>
            <?= $_telephone->setTelephone($block->getAddress()->getTelephone())->toHtml() ?>
        <?php endif ?>
        <?php  $_streetValidationClass = $block->getCustomerHelper()->getAttributeValidationClass('street'); ?>
        <div class="field street required">
            <label for="street_1" class="label">
                <span><?= $block->escapeHtml(__('Street Address')) ?></span>
            </label>
            <div class="tooltip">
            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the street address')) ?></span>
            </div>
            <div class="control">
                <input type="text"
                       name="street[]"
                       value="<?= $block->escapeHtmlAttr($block->getStreetLine(1)) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>"
                       id="street_1"
                       class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>"/>
                <div class="nested">
                    <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                    <?php for ($_i = 1, $_n = $block->getCustomerHelper()->getStreetLines(); $_i < $_n; $_i++): ?>
                        <div class="field additional">
                            <label class="label" for="street_<?= /* @noEscape */ $_i + 1 ?>">
                                <span><?= $block->escapeHtml(__('Street Address %1', $_i + 1)) ?></span>
                            </label>
                            <div class="control">
                                <input type="text" name="street[]"
                                       value="<?= $block->escapeHtmlAttr($block->getStreetLine($_i + 1)) ?>"
                                       title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i + 1)) ?>"
                                       id="street_<?= /* @noEscape */ $_i + 1 ?>"
                                       class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>">
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <div class="field city required">
            <label class="label" for="city"><span><?= $block->escapeHtml(__('City')) ?></span></label>
            <div class="tooltip">
            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Specify the city for store address')) ?></span>
            </div>
            <div class="control">
                <input type="text"
                       name="city"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getCity()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('City')) ?>"
                       class="input-text <?=
                        $block->escapeHtmlAttr($block->getCustomerHelper()->getAttributeValidationClass('city')) ?>"
                       id="city">
            </div>
        </div>
        <div class="field region required">
            <label class="label" for="region_id">
                <span><?= $block->escapeHtml(__('State/Province')) ?></span>
            </label>
            <div class="tooltip">
            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the state or province for store address')) ?></span>
            </div>
            <div class="control">
                <select id="region_id" name="region_id"
                        title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>"
                        class="validate-select" <?=
                        /* @noEscape */ !$block->getConfig('general/region/display_all') ? ' disabled="disabled"' : ''
                        ?>>
                    <option value="">
                    <?= $block->escapeHtml(__('Please select a region, state or province.')) ?>
                    </option>
                </select>
                <input type="text"
                       id="region"
                       name="region"
                       value="<?= $block->escapeHtmlAttr($block->getRegion()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('State/Province')) ?>"
                       class="input-text <?=
                        $block->escapeHtmlAttr($block->getCustomerHelper()->getAttributeValidationClass('region')) ?>"/>
            </div>
        </div>
        <div class="field zip required">
            <label class="label" for="zip">
                <span><?= $block->escapeHtml(__('Zip/Postal Code')) ?></span>
            </label>
            <div class="tooltip">
            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter your zip/postal code ')) ?></span>
           </div>
            <div class="control">
                <input type="text"
                       name="postal_code"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getPostalCode()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Zip/Postal Code')) ?>"
                       id="zip"
                       class="input-text validate-zip-international <?=
                        $block->escapeHtmlAttr($block->getCustomerHelper()->getAttributeValidationClass('postcode'))
                        ?>">
            </div>
        </div>
        <div class="field country required">
            <label class="label" for="country"><span><?= $block->escapeHtml(__('Country')) ?></span></label>
            <div class="tooltip">
            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the country where your business is located from the dropdown menu.')) ?></span>
            </div>
            <div class="control">
                <?= $block->getCountryHtmlSelect() ?>
            </div>
        </div>
    </fieldset>

    <div class="wk-mp-aboutus-title page-title">
        <h2 class="legend"><?= /* @noEscape */ __('Shipping Destinations') ?></h2>
    </div>
    <fieldset class="fieldset info">
        <div class="field sallowspecific">
            <label class="label" for="sallowspecific"><span><?= $block->escapeHtml(__('Ship to Applicable Countries')) ?></span></label>
            <div class="tooltip">
                <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Ship only to selected countries')) ?></span>
            </div>
            <div class="control">
                <select id="sallowspecific" class="required-entry select" name="sallowspecific">
                    <?php $allowSpecific = $countryRestriction->getAllowSpecific(); ?>
                    <option value="0" <?= $allowSpecific == 0 ? 'selected' : '' ?>>Disabled</option>
                    <option value="1" <?= $allowSpecific == 1 ? 'selected' : '' ?>>Enabled</option>
                </select>
            </div>
        </div>

        <div class="field specificcountry">
            <label class="label" for="country"><span><?= $block->escapeHtml(__('Ship to Specific Countries')) ?></span></label>
            <div class="tooltip">
                <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Ship only to selected countries')) ?></span>
            </div>
            <div class="control">
                <select id="specificcountry" class="required-entry select" name="specificcountry[]" multiple style="height: 240px">
                    <?php $countries = $block->getCountryCollection()->toOptionArray(); array_shift($countries); ?>
                    <?php $selected = $countryRestriction->getAllowedCountriesAsArray(); ?>
                    <?php foreach ($countries as $country): ?>
                        <option value="<?= /* @noEscape */ $country['value'] ?>"
                            <?= in_array($country['value'], $selected)? 'selected' : ''; ?>  >
                            <?=  /* @noEscape */ $country['label']?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </fieldset>
</form>
<script type="text/x-magento-init">
    {
        "#country": {
            "regionUpdater": {
                "optionalRegionAllowed": true,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $block->getDHelper()->getRegionJson() ?>,
                "defaultRegion": "<?= (int) $block->getRegionId() ?>",
                "countriesWithOptionalZip": <?=
                /* @noEscape */ $block->getDHelper()->getCountriesWithOptionalZip(true)
                ?>
            }
        }
    }
</script>
<script>
    require([
        'jquery'
    ], function ($) {
        $(function () {
            if ($("#region_id").prop('disabled')) {
                var val = $("#region").val();
                if (val) {
                    var intVal = setInterval(function () {
                        if (!$("#region").val()) {
                            $("#region").val(val);
                            clearInterval(intVal);
                        }
                    }, 200);
                }
            }

            function setRequiredAttributes() {
                $(".required-entry").attr("required", "required");
                $("input, select, textarea").not(".required-entry").removeAttr("required");
            }

            setRequiredAttributes();

            $("#form-validate").on("change", function () {
                setRequiredAttributes();
            });
        });
    });
</script>
