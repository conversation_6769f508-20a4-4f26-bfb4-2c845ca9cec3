<?php
/**
 * PL Development.
 *
 * @category    PL
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2016 PL Development. (http://www.polacin.com)
 */
namespace PL\Mpgs\Model;

use PL\Mpgs\Model\Api\Connection;

class HostedCheckout extends \Magento\Payment\Model\Method\AbstractMethod
{
    CONST API_VERSION = 69;

    const METHOD_CODE = 'mpgs_hostedcheckout';

    protected $_code = self::METHOD_CODE;

    protected $_infoBlockType = 'PL\Mpgs\Block\Info\HostedCheckout';

    protected $_canAuthorize = true;

    protected $_canCapture = true;

    /**
     * @var bool
     */
    protected $_canRefund = true;

    /**
     * @var bool
     */
    protected $_canRefundInvoicePartial = true;

    /**
     * @var bool
     */
    protected $_canUseInternal = false;

    /**
     * @var bool
     */
    protected $_isInitializeNeeded = true;

    /**
     * @var
     */
    protected $encryptor;

    /**
     * @var
     */
    protected $request;

    /**
     * @var
     */
    protected $urlBuilder;

    /**
     * @var
     */
    protected $plLogger;

    /**
     * @var
     */
    protected $jsonHelper;

    /**
     * @var
     */
    protected $orderSender;

    /**
     * @var
     */
    protected $invoiceSender;

    /**
     * @var \PL\Mpgs\Helper\Data
     */
    protected $mpgsHelper;

    protected $checkoutSession;

    protected $api;

    protected $countryFactory;

    protected $resolver;

    public function __construct(
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Framework\App\RequestInterface $request,
        \Magento\Framework\UrlInterface $urlBuilder,
        \PL\Mpgs\Helper\Data $mpgsHelper,
        \PL\Mpgs\Logger\Logger $plLogger,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender,
        \Magento\Sales\Model\Order\Email\Sender\InvoiceSender $invoiceSender,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\Locale\Resolver $resolver,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory,
        \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory,
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Payment\Model\Method\Logger $logger,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $paymentData,
            $scopeConfig,
            $logger,
            $resource,
            $resourceCollection,
            $data
        );
        $this->urlBuilder = $urlBuilder;
        $this->mpgsHelper = $mpgsHelper;
        $this->plLogger = $plLogger;
        $this->request = $request;
        $this->encryptor = $encryptor;
        $this->jsonHelper = $jsonHelper;
        $this->orderSender = $orderSender;
        $this->invoiceSender = $invoiceSender;
        $this->checkoutSession = $checkoutSession;
        $this->countryFactory = $countryFactory;
        $this->resolver = $resolver;
        if ($this->getConfigData('active')) {
            $config['apiVersion'] = self::API_VERSION;
            $config['gatewayUrl'] = $this->getGatewayUrl();
            $config['sslVerifyPeer'] = $this->getConfigData('ssl_enabled');
            $this->api = new Connection($config);
        }
    }

    public function validate()
    {
        /** @noinspection PhpDeprecationInspection */
        parent::validate();
        $paymentInfo = $this->getInfoInstance();
        /** @noinspection PhpUnnecessaryFullyQualifiedNameInspection */
        if ($paymentInfo instanceof \Magento\Sales\Model\Order\Payment) {
            $paymentInfo->getOrder()->getBaseCurrencyCode();
        } else {
            $paymentInfo->getQuote()->getBaseCurrencyCode();
        }
        return $this;
    }

    public function getMerchantid()
    {
        $merchantId = trim($this->getConfigData('merchant_id'));
        return $merchantId;
    }

    public function getApiPassword()
    {
        $apiPassword = trim($this->getConfigData('api_password'));
        return $apiPassword;
    }

    public function getMerchantName()
    {
        $merchantName = trim($this->getConfigData('merchant_name'));
        if (empty($merchantName)) {
            return 'Polacin MPGS module - Mageneto 2';
        }
        return $merchantName;
    }


    public function getCheckoutRedirectUrl()
    {
        return $this->urlBuilder->getUrl(
            'mpgs/hostedcheckout/redirect',
            ['_secure' => $this->request->isSecure()]
        );
    }

    public function getCheckoutSuccessUrl()
    {
        return $this->urlBuilder->getUrl(
            'mpgs/hostedcheckout/success',
            ['_secure' => $this->request->isSecure()]
        );
    }

    public function getCheckoutCancelUrl()
    {
        return $this->urlBuilder->getUrl(
            'mpgs/hostedcheckout/cancel',
            ['_secure' => $this->request->isSecure()]
        );
    }

    public function getGatewayUrl()
    {
        return rtrim(
            $this->getConfigData('gateway_url') ?? '',
            '/'
        );
    }

    public function getOperation()
    {
        $apiOperation = $this->getConfigData('operation');
        return strtoupper($apiOperation);
    }

    public function getWebhookEnabled()
    {
        if($this->getConfigData('webhook_enabled')) {
            return 1;
        }
        return 0;
    }

    public function getNotificationUrl()
    {
        return $this->urlBuilder->getUrl(
            'mpgs/webhook',
            ['_secure' => $this->request->isSecure()]
        );
    }

    public function getWebhookSecret()
    {
        $notificationSecret = $this->getConfigData('notification_secret');
        if (!empty($notificationSecret)) {
            $secret = trim($this->getConfigData('notification_secret'));
            return $secret;
        }

    }


    public function getMultipleCurrencies()
    {
        $flag = $this->getConfigData('multiple_currency_enabled');
        return $flag;
    }

    public function getCurrencyCode(\Magento\Sales\Model\Order $order)
    {
        $currencyCode = $order->getBaseCurrencyCode();
        if ($this->getMultipleCurrencies()) {
            $currencyCode = $order->getOrderCurrencyCode();
        }
        return $currencyCode;
    }

    public function getAmount(\Magento\Sales\Model\Order $order)
    {
        $amount = sprintf("%.2F",$order->getBaseGrandTotal());
        if (strtoupper($this->getCurrencyCode($order))== 'KWD') {
            $amount = sprintf("%.3F",$order->getBaseGrandTotal());
        }
        if ($this->getMultipleCurrencies()) {
            $amount = sprintf("%.2F",$order->getGrandTotal());
            if (strtoupper($this->getCurrencyCode($order))== 'KWD') {
                $amount = sprintf("%.3F",$order->getGrandTotal());
            }
        }
        return $amount;
    }

    public function generateTransactionId($incrementId = null)
    {
        if (!empty($incrementId)) {
            $transactionId = $incrementId.gmdate("Ymdhis");
            return $transactionId;
        }
    }

    public function getPrefixOrder($incrementId = null)
    {
        $prefix = gmdate("Ymdh");
        if (!empty($incrementId)) {
            $orderId = $prefix.$incrementId;
            return $orderId;
        }
    }

    public function revertIncrementId($orderId = null)
    {
        $length = strlen($orderId);
        if (!empty($orderId)) {
            return substr($orderId, 10, $length);
        }
    }


    public function getCountryCodeIso3($iso2Code)
    {
        $countryCode = $this->countryFactory->create()
            ->load($iso2Code)->getData();
        return $countryCode['iso3_code'];
    }

    public function getJSCheckoutUrl()
    {
        $gatewayUrl = rtrim($this->getConfigData('gateway_url'),"/");
        $jsCheckoutUrl = "{$gatewayUrl}/static/checkout/checkout.min.js";
        return $jsCheckoutUrl;
    }

    public function getCheckoutConfig(\Magento\Sales\Model\Order $order)
    {

        $checkoutSession = $this->getHostedCheckoutSessionId($order);
        if (isset($checkoutSession['result']) && $checkoutSession['result'] == 'ERROR') {
            $errorMessage = sprintf(
                "%s - %s",
                $checkoutSession['error.cause'],
                $checkoutSession['error.explanation']
            );
            die($errorMessage);
        }
        $checkoutSessionId = $checkoutSession['session.id'];
        if (!empty($checkoutSessionId)) {
            $config['session']['id'] = $checkoutSessionId;
            if ($this->getConfigData('debug')) {
                $this->plLogger->debug("REQUEST DATA: ".print_r($config,1));
            }
            return json_encode($config);
        }
        return false;


    }


    /**
     * @param string $paymentAction
     * @param object $stateObject
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function initialize($paymentAction, $stateObject)
    {
        if ($paymentAction == 'order') {
            $order = $this->getInfoInstance()->getOrder();
            $order->setCustomerNoteNotify(false);
            $order->setCanSendNewEmailFlag(false);
            $stateObject->setIsNotified(false);
            $stateObject->setState(\Magento\Sales\Model\Order::STATE_PENDING_PAYMENT);
            $stateObject->setStatus(\Magento\Sales\Model\Order::STATE_PENDING_PAYMENT);
        }
    }


    public function getHostedCheckoutSessionId(\Magento\Sales\Model\Order $order)
    {
        $billing = $order->getBillingAddress();

        $request['apiOperation'] = 'INITIATE_CHECKOUT';
        $request['merchant'] = $this->getMerchantid();
        $request['apiUsername'] = 'merchant.'.$this->getMerchantid();
        $request['apiPassword'] = $this->getApiPassword();
        $request['order.id'] =  $this->getPrefixOrder($order->getIncrementId());
        $request['order.amount'] = $this->getAmount($order);
        $request['order.currency'] = $this->getCurrencyCode($order);
        $request['order.reference'] =  $this->generateTransactionId($order->getIncrementId());
        $request['order.description'] = 'Order #'.$order->getIncrementId();
        $request['interaction.operation'] = $this->getOperation();
        if ($this->getWebhookEnabled()) {
            $request['order.notificationUrl'] = $this->getNotificationUrl();
        }

        $request['interaction.merchant.name'] = $this->getMerchantName();
        $request['interaction.displayControl.billingAddress'] = 'HIDE';
        $request['interaction.displayControl.customerEmail'] = 'HIDE';
        $request['interaction.displayControl.paymentTerms'] = 'HIDE';
        $request['interaction.displayControl.shipping'] ='HIDE';
        $request['interaction.locale'] = $this->resolver->getLocale();;
        $request['checkoutMode'] ='WEBSITE';

        $request['billing.address.company'] = $billing->getCompany();
        $request['billing.address.street'] = $billing->getStreetLine(1);
        $request['billing.address.city'] = $billing->getCity();
        $request['billing.address.postcodeZip'] = $billing->getPostcode();
        $request['billing.address.stateProvince'] = $billing->getRegion();
        $request['billing.address.country'] = $this->getCountryCodeIso3($billing->getCountryId());

        if ($this->getConfigData('debug')) {
            $this->plLogger->debug("REQUEST DATA: ".print_r($request,1));
        }
        $response = $this->api->sendTransaction($request);
        $pairArray = explode("&", $response);
        $responseArray = [];
        foreach ($pairArray as $pair) {
            $param = explode("=", $pair);
            $responseArray[urldecode($param[0])] = urldecode($param[1]);
        }
        return $responseArray;

    }

    public function retrieveOrder(\Magento\Sales\Model\Order $order)
    {
        $request['apiOperation'] = 'RETRIEVE_ORDER';
        $request['merchant'] = $this->getMerchantid();
        $request['apiUsername'] = 'merchant.'.$this->getMerchantid();
        $request['apiPassword'] = $this->getApiPassword();
        $request['order.id'] =  $this->getPrefixOrder($order->getIncrementId());
        $response = $this->api->sendTransaction($request);
        if ($this->getConfigData('debug')) {
            $this->plLogger->debug("RESPONSE: ".$response);
        }
        $pairArray = explode("&", $response);
        $responseArray = [];
        foreach ($pairArray as $pair) {
            $param = explode("=", $pair);
            $responseArray[urldecode($param[0])] = urldecode($param[1]);
        }
        if ($this->getConfigData('debug')) {
            $this->plLogger->debug("RETRIEVE_ORDER RESPONSE".print_r($responseArray, 1));
        }
        return $responseArray;
    }

    public function acceptTransaction(\Magento\Sales\Model\Order $order, $response)
    {
        $this->checkOrderStatus($order);
        if ($order->getId()) {
            $additionalData = $this->jsonHelper->jsonEncode($response);
            $order->getPayment()->setTransactionId($response['id']);
            $order->getPayment()->setLastTransId($response['id']);
            $order->getPayment()->setAdditionalInformation('payment_additional_info', $additionalData);
            $note = __('PAYMENT %1. Order ID: "%2"', [$response['status'],$response['id']]);
            //TODO: Check if this action is triggered
            $order->setStatus(\Magento\Sales\Model\Order::STATE_PROCESSING);
            $order->setState(\Magento\Sales\Model\Order::STATE_PROCESSING);
            $order->addStatusHistoryComment($note);
            $order->setCustomerNoteNotify(true);
            $this->orderSender->send($order);
            if (strtoupper($this->getOperation()) == 'PURCHASE') {
                $order->setTotalpaid($this->getAmount($order));
                $this->createInvoice($order);
            }
            $order->save();
        }
    }

    public function createInvoice(\Magento\Sales\Model\Order $order)
    {
        if (!$order->hasInvoices() && $order->canInvoice()) {
            $invoice = $order->prepareInvoice();
            if ($invoice->getTotalQty() > 0) {
                $invoice->setRequestedCaptureCase(\Magento\Sales\Model\Order\Invoice::CAPTURE_ONLINE);
                $invoice->setTransactionId($order->getPayment()->getTransactionId());
                $invoice->register();
                $invoice->save();
				$this->invoiceSender->send($invoice);
            }
			$order->addCommentToStatusHistory(
                __('You notified customer about invoice #%1.', $invoice->getIncrementId())
            )->setIsCustomerNotified(true)->save();
        }
    }

    public function cancelTransaction(\Magento\Sales\Model\Order $order)
    {
        $this->checkOrderStatus($order);
        if ($order->getId()) {
            $order->getPayment()->setTransactionId($order->getIncrementId());
            $order->getPayment()->setLastTransId($order->getIncrementId());
            $note = __('Declined. Order ID: "%1"', $order->getIncrementId());
            $order->setState(\Magento\Sales\Model\Order::STATE_CANCELED);
            $order->setStatus(\Magento\Sales\Model\Order::STATE_CANCELED);
            $order->setCustomerNoteNotify(false);
            $order->addStatusHistoryComment($note);
            $order->cancel()->save();
            if ($order->getState()!= \Magento\Sales\Model\Order::STATE_CANCELED) {
                $order->registerCancellation($note)->save();
            }
        }
    }


    public function checkOrderStatus(\Magento\Sales\Model\Order $order)
    {
        if ($order->getId()) {
            $state = $order->getState();
            switch ($state) {
                case \Magento\Sales\Model\Order::STATE_PROCESSING:
                case \Magento\Sales\Model\Order::STATE_HOLDED:
                case \Magento\Sales\Model\Order::STATE_CANCELED:
                case \Magento\Sales\Model\Order::STATE_CLOSED:
                case \Magento\Sales\Model\Order::STATE_COMPLETE:
                    break;
            }
        }
    }


    public function capture(\Magento\Payment\Model\InfoInterface $payment, $amount)
    {

        $paymentInfo = $this->jsonHelper->jsonDecode(
            $payment->getAdditionalInformation('payment_additional_info')
        );
        if (strtoupper($paymentInfo['status'])== 'AUTHORIZED') {
            $order = $payment->getOrder();
            $request = [];
            $request['apiOperation'] = 'CAPTURE';
            $request['merchant'] = $this->getMerchantid();
            $request['apiUsername'] = 'merchant.'.$this->getMerchantid();
            $request['apiPassword'] = $this->getApiPassword();
            $request['order.id'] =  $paymentInfo['id']; //$order->getIncrementId();
            $request['transaction.id'] = $this->generateTransactionId($order->getIncrementId());
            $request['transaction.amount'] = $amount;
            $request['transaction.currency'] = strtoupper($this->getCurrencyCode($order));
            $response = $this->api->sendTransaction($request);
            if ($this->getConfigData('debug')) {
                $this->plLogger->debug($response);
            }
            $pairArray = explode("&", $response);
            if ($this->getConfigData('debug')) {
                $this->plLogger->debug(print_r($pairArray,1));
            }
            $responseArray = [];
            foreach ($pairArray as $pair) {
                $param = explode("=", $pair);
                $responseArray[urldecode($param[0])] = urldecode($param[1]);
            }
            if (array_key_exists("result", $responseArray)) {
                $result = $responseArray["result"];
                if ($result == 'SUCCESS') {
                    $payment
                        ->setTransactionId($responseArray['transaction.id'])
                        ->setLastTransId($responseArray['transaction.id'])
                        ->setParentTransactionId($responseArray['transaction.id'])
                        ->setIsTransactionClosed(0);
                    $additionalData = $this->jsonHelper->jsonEncode(
                        $this->mpgsHelper->getPaymentInfo($responseArray)
                    );
                    $payment->setAdditionalInformation('payment_additional_info', $additionalData);

                } else {
                    throw new \Magento\Framework\Exception\LocalizedException(__('Payment cannot be captured'));
                }
            } else {
                throw new \Magento\Framework\Exception\LocalizedException($pairArray['response.acquirerMessage']);
            }
        } else {
            parent::capture($payment, $amount);
        }

        return $this;
    }


    public function refund(\Magento\Payment\Model\InfoInterface $payment, $amount)
    {
        $paymentInfo = $this->jsonHelper->jsonDecode(
            $payment->getAdditionalInformation('payment_additional_info')
        );

        if ($payment->getParentTransactionId()) {
            $order = $payment->getOrder();
            $request = [];
            $request['apiOperation'] = 'REFUND';
            $request['merchant'] = $this->getMerchantid();
            $request['apiUsername'] = 'merchant.'.$this->getMerchantid();
            $request['apiPassword'] = $this->getApiPassword();
            $request['order.id'] =  $paymentInfo['id'];
            $request['transaction.id'] = $this->generateTransactionId($order->getIncrementId());
            $request['transaction.amount'] = $amount;
            $request['transaction.currency'] = strtoupper($this->getCurrencyCode($order));
            $response = $this->api->sendTransaction($request);
            if ($this->getConfigData('debug')) {
                $this->plLogger->debug($response);
            }
            $pairArray = explode("&", $response);
            if ($this->getConfigData('debug')) {
                $this->plLogger->debug(print_r($pairArray,1));
            }
            $responseArray = [];
            foreach ($pairArray as $pair) {
                $param = explode("=", $pair);
                $responseArray[urldecode($param[0])] = urldecode($param[1]);
            }
            if (array_key_exists("result", $responseArray)) {
                $result = $responseArray["result"];
                if ($result == 'SUCCESS') {
                    $payment
                        ->setTransactionId($responseArray['transaction.id'])
                        ->setLastTransId($responseArray['transaction.id'])
                        ->setParentTransactionId($responseArray['order.id']);
                    $payment->setIsTransactionClosed(true);
                    $additionalData = $this->jsonHelper->jsonEncode(
                        $this->mpgsHelper->getPaymentInfo($responseArray)
                    );
                    $payment->setAdditionalInformation('payment_additional_info', $additionalData);
                } else {
                    throw new \Magento\Framework\Exception\LocalizedException(__('Payment cannot be refunded'));
                }
            } else {
                throw new \Magento\Framework\Exception\LocalizedException($responseArray['response.acquirerMessage']);
            }
        }

    }

}
