<?php
/**
 * PL Development.
 *
 * @category    PL
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2016 PL Development. (http://www.polacin.com)
 */
namespace PL\Mpgs\Controller\Webhook;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\CsrfAwareActionInterface;
use Magento\Framework\App\Request\InvalidRequestException;
use Magento\Framework\App\RequestInterface;
use Magento\Sales\Model\OrderFactory;

use PL\Mpgs\Model\HostedCheckout;
use PL\Mpgs\Logger\Logger as PLLogger;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use PL\Mpgs\Helper\Data as MpgsHelper;
use Comave\MapOrderStatuses\Service\OrderStatuses;
use Comave\MapOrderStatuses\Model\ConfigProvider;

class Index extends \Magento\Framework\App\Action\Action implements CsrfAwareActionInterface
{
    const X_HEADER_SECRET = 'X-Notification-Secret';

    const X_HEADER_ATTEMPT = 'X-Notification-Attempt';

    const X_HEADER_ID = 'X-Notification-Id';

    protected $hosted;

    protected $plLogger;

    protected $orderFactory;

    protected $jsonHelper;

    protected $_mpgsHelper;

    public function __construct(
        Context $context,
        OrderFactory $orderFactory,
        HostedCheckout $hosted,
        JsonHelper $jsonHelper,
        MpgsHelper $mpgsHelper,
        PLLogger $plLogger,
        private readonly OrderStatuses $orderStatuses,
        private readonly ConfigProvider $orderStatusConfigProvider,
    ) {
        parent::__construct($context);
        $this->hosted = $hosted;
        $this->plLogger = $plLogger;
        $this->orderFactory = $orderFactory;
        $this->jsonHelper = $jsonHelper;
        $this->_mpgsHelper = $mpgsHelper;
    }


    public function execute()
    {
        try {
            $this->validateConnection();
            $params = $this->getRequest()->getParams();
            if (isset($params['order_id'])) {
                $incrementId = $this->hosted->revertIncrementId($params['order_id']);
                $order = $this->orderFactory->create()->loadByIncrementId($incrementId);


                if($order->getState() == \Magento\Sales\Model\Order::STATE_PENDING_PAYMENT) {
                    if (isset($params['order_status'])
                        && (
                            $this->orderStatuses->isStatusActive($params['order_status']) === false
                            || (
                                $this->orderStatusConfigProvider->isPredefinedStatusMapped($params['order_status']) === false
                                && $this->orderStatusConfigProvider->isCustomStatusMapped($params['order_status']) === false)
                            )
                    ) {
                        if($params['order_status'] == 'AUTHORIZED' || $params['order_status'] == 'CAPTURED') {
                            $additionalData = $this->jsonHelper->jsonEncode(
                                $this->_mpgsHelper->getDataNotification($params)
                            );
                            $order->getPayment()->setAdditionalInformation('payment_additional_info', $additionalData);
                            $order->getPayment()
                                ->setTransactionId($params['order_id'])
                                ->setLastTransId($params['order_id'])
                                ->setIsTransactionClosed(0);
                            $note = sprintf("Webhook Notification: %s. Transaction ID: %s",$params['order_status'],$params['order_id']);
                            $order->setStatus(\Magento\Sales\Model\Order::STATE_PROCESSING);
                            $order->setState(\Magento\Sales\Model\Order::STATE_PROCESSING);
                            $order->addStatusHistoryComment($note);
                            $order->setCustomerNoteNotify(false);
                            $order->setTotalpaid($this->hosted->getAmount($order));
                            if ($params['order_status'] == 'CAPTURED') {
                                $this->hosted->createInvoice($order);
                            }
                            $order->save();
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            return $this->getResponse()
                ->setHttpResponseCode(400)
                ->setBody(__("WebHook Exception: %1",$e->getMessage()));
        }
    }

    public function validateConnection()
    {
        if (!$this->getRequest()->isSecure())
        {
            throw new \Magento\Framework\App\Request\InvalidRequestException(__("https required."));
        }

        $headerId = $this->getRequest()->getHeader(static::X_HEADER_ID);

        if (empty($headerId)) {
            throw new \Magento\Framework\App\Request\InvalidRequestException(__("X_HEADER_ID not provided"));
        }
        $requestSecret = $this->getRequest()->getHeader(static::X_HEADER_SECRET);

        if (empty($requestSecret)) {
            throw new \Magento\Framework\App\Request\InvalidRequestException(__("Authorization not provided"));
        }
        $webhookSecret = $this->hosted->getWebhookSecret();
        if (empty($webhookSecret)) {
            throw new \Magento\Framework\App\Request\InvalidRequestException(__('Webhook Disabled'));
        }
        if ($webhookSecret !== $requestSecret) {
            throw new \Magento\Framework\App\Request\InvalidRequestException(__('Authorization failed'));
        }
    }



    public function createCsrfValidationException(
        RequestInterface $request
    ): ?InvalidRequestException {
        return null;
    }


    public function validateForCsrf(RequestInterface $request): ?bool
    {
        return true;
    }
}
