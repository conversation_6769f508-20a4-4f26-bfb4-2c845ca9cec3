<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Plugin;

use Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface;
use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Comave\ShopifyAccounts\Model\Command\GetSellerByDomain;
use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface;
use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterfaceFactory;
use Comave\ShopifyWebhookInstaller\Api\WebhookHeartbeatRepositoryInterface;
use Comave\ShopifyWebhookInstaller\Api\WebhookResultRepositoryInterface;
use Magento\Framework\App\HttpRequestInterface;
use Psr\Log\LoggerInterface;

class AddHeartbeat
{
    /**
     * @param GetSellerByDomain $getSellerByDomain
     * @param LoggerInterface $logger
     * @param WebhookResultRepositoryInterface $webhookInstallRepository
     * @param WebhookHeartbeatRepositoryInterface $webhookHeartbeatRepository
     * @param WebhookHeartbeatInterfaceFactory $heartbeatFactory
     */
    public function __construct(
        private readonly GetSellerByDomain $getSellerByDomain,
        private readonly LoggerInterface $logger,
        private readonly WebhookResultRepositoryInterface $webhookInstallRepository,
        private readonly WebhookHeartbeatRepositoryInterface $webhookHeartbeatRepository,
        private readonly WebhookHeartbeatInterfaceFactory $heartbeatFactory,
    ) {
    }

    /**
     * @param WebhookTopicProcessorInterface $webhookTopicProcessor
     * @param HttpRequestInterface $request
     * @return array|null
     * @throws InvalidWebhookRequestException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeProcess(
        WebhookTopicProcessorInterface $webhookTopicProcessor,
        HttpRequestInterface $request
    ): ?array {
        try {
            $accountData = $this->getSellerByDomain->get(
                $request->getHeader(WebhookValidatorInterface::SELLER_DOMAIN_HEADER)
            );
            $topicName = $request->getHeader(
                WebhookValidatorInterface::EVENT_TOPIC_HEADER
            );
            $webhook = $this->webhookInstallRepository->getByTopic(
                $accountData['entity_id'],
                strtoupper(str_replace('/', '_', $topicName))
            );

            if (!$webhook->getId()) {
                $this->logger->warning(
                    '[Comave.WebhookHeartbeat] Unable to add heartbeat, topic not installed',
                    [
                        'account' => $accountData['entity_id'],
                        'topic' => $topicName,
                    ]
                );

                return null;
            }
        } catch (\Exception $e) {
            $this->logger->warning(
                '[Comave.WebhookHeartbeat] Unable to add heartbeat',
                [
                    'e' => $e->getMessage(),
                ]
            );

            return null;
        }

        /** @var WebhookHeartbeatInterface $heartBeat */
        $heartBeat = $this->heartbeatFactory->create();

        $json = $request->getContent();
        $heartBeat->setPayload($json)
            ->setWebhookId($webhook->getId());

        $this->webhookHeartbeatRepository->save($heartBeat);
        $this->logger->info(
            '[Comave.WebhookHeartbeat] Added heartbeat',
            [
                'account' => $accountData['entity_id'],
                'topic' => $topicName,
            ]
        );

        return null;
    }
}
