<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Api;

use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface;

interface WebhookHeartbeatRepositoryInterface
{
    /**
     * @param string $topicName
     * @param string $shopifyAccountId
     * @return WebhookHeartbeatInterface|null
     */
    public function getWebhookHeartbeat(string $shopifyAccountId, string $topicName): ?WebhookHeartbeatInterface;

    /**
     * @param WebhookHeartbeatInterface $webhookHeartbeat
     * @return WebhookHeartbeatInterface
     */
    public function save(WebhookHeartbeatInterface $webhookHeartbeat): WebhookHeartbeatInterface;
}
