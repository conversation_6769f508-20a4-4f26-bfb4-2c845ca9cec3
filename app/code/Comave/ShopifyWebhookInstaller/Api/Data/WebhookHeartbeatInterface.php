<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Api\Data;


interface WebhookHeartbeatInterface
{
    public const string TABLE_NAME = 'comave_shopify_webhook_heartbeat';
    public const string CREATED_AT = 'created_at';
    public const string WEBHOOK_ID = 'webhook_id';
    public const string PAYLOAD = 'payload';

    /**
     * @param string $webhookId
     * @return self
     */
    public function setWebhookId(string $webhookId): self;

    /**
     * @return string|null
     */
    public function getWebhookId(): ?string;

    /**
     * @return string
     */
    public function getCreatedAt(): string;

    /**
     * @param string $createdAt
     * @return self
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): self;

    /**
     * @return string|null
     */
    public function getPayload(): ?string;
}
