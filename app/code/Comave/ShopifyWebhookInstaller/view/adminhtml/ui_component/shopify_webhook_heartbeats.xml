<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">shopify_webhook_heartbeats.shopify_webhook_heartbeats_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>shopify_webhook_heartbeats_columns</spinner>
        <deps>
            <dep>shopify_webhook_heartbeats.shopify_webhook_heartbeats_data_source</dep>
        </deps>
    </settings>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <dataSource name="shopify_webhook_heartbeats_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Webkul_MpMultiShopifyStoreMageConnect::shopify_account_connect</aclResource>
        <dataProvider class="Comave\ShopifyWebhookInstaller\Ui\DataProvider\Grid"
                      name="shopify_webhook_heartbeats_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <columns name="shopify_webhook_heartbeats_columns">
        <column name="topic_name" sortOrder="5">
            <settings>
                <filter>text</filter>
                <dataType>text</dataType>
                <label translate="true">Shopify Topic Name</label>
            </settings>
        </column>
        <column name="shopify_account_id" sortOrder="10">
            <settings>
                <filter>text</filter>
                <dataType>text</dataType>
                <label translate="true">Store Name</label>
            </settings>
        </column>
        <column name="payload" sortOrder="20">
            <settings>
                <filter>false</filter>
                <label translate="true">Payload</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" sortOrder="30">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Last Heartbeat</label>
            </settings>
        </column>
    </columns>
</listing>
