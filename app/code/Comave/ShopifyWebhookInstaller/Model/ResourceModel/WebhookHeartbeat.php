<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Model\ResourceModel;

use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

class WebhookHeartbeat extends AbstractDb
{
    protected function _construct(): void
    {
        $this->_init(
            WebhookHeartbeatInterface::TABLE_NAME,
            'id'
        );
    }
}
