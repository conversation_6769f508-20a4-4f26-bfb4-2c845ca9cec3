<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Model;

use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface;
use Magento\Framework\Model\AbstractModel;

class WebhookHeartbeat extends AbstractModel implements WebhookHeartbeatInterface
{
    protected function _construct(): void
    {
        $this->_init(
            \Comave\ShopifyWebhookInstaller\Model\ResourceModel\WebhookHeartbeat::class
        );
    }

    /**
     * @param string $webhookId
     * @return self
     */
    public function setWebhookId(string $webhookId): WebhookHeartbeatInterface
    {
        return $this->setData(WebhookHeartbeatInterface::WEBHOOK_ID, $webhookId);
    }

    /**
     * @return string|null
     */
    public function getWebhookId(): ?string
    {
        return $this->getData(WebhookHeartbeatInterface::WEBHOOK_ID);
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->getData(WebhookHeartbeatInterface::CREATED_AT);
    }

    /**
     * @param string $createdAt
     * @return self
     */
    public function setCreatedAt(string $createdAt): WebhookHeartbeatInterface
    {
        return $this->setData(WebhookHeartbeatInterface::CREATED_AT, $createdAt);
    }

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): WebhookHeartbeatInterface
    {
        return $this->setData(WebhookHeartbeatInterface::PAYLOAD, $payload);
    }

    /**
     * @return string|null
     */
    public function getPayload(): ?string
    {
        return $this->getData(WebhookHeartbeatInterface::PAYLOAD);
    }
}
