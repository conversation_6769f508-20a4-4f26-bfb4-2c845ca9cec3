<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Model;

use Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface;
use Comave\ShopifyWebhookInstaller\Api\Data\WebhookInstallResultInterface;
use Comave\ShopifyWebhookInstaller\Api\WebhookHeartbeatRepositoryInterface;
use Comave\ShopifyWebhookInstaller\Model\ResourceModel\WebhookHeartbeat;
use Comave\ShopifyWebhookInstaller\Model\ResourceModel\WebhookHeartbeat\CollectionFactory;

class WebhookHeartbeatRepository implements WebhookHeartbeatRepositoryInterface
{
    /**
     * @param WebhookHeartbeat $resourceModel
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly WebhookHeartbeat $resourceModel,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param string $topicName
     * @param string $shopifyAccountId
     * @return WebhookHeartbeatInterface|null
     */
    public function getWebhookHeartbeat(string $shopifyAccountId, string $topicName): ?WebhookHeartbeatInterface
    {
        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->join(
                ['wi' => WebhookInstallResultInterface::TABLE_NAME],
                'wi.id = main_table.webhook_id',
                ''
            )->where(
                'wi.topic_name = ?',
                $topicName
            )->where(
                'wi.shopify_account_id = ?',
                $shopifyAccountId
            );

        $collection->getSelect()->order('main_table.created_at DESC');

        return $collection->getSize() > 0 ? $collection->getFirstItem() : null;
    }

    /**
     * @param WebhookHeartbeatInterface $webhookHeartbeat
     * @return WebhookHeartbeatInterface
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function save(WebhookHeartbeatInterface $webhookHeartbeat): WebhookHeartbeatInterface
    {
        $this->resourceModel->save($webhookHeartbeat);

        return $webhookHeartbeat;
    }
}
