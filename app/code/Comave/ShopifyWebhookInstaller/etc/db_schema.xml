<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_shopify_webhook_install" resource="default" engine="innodb" comment="Webhook install storage">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="shopify_account_id" padding="10" unsigned="true" nullable="false" identity="false" comment="Shopify Account id"/>
        <column xsi:type="varchar" length="255" name="webhook_id" nullable="false" comment="Internal GraphQL Id"/>
        <column xsi:type="varchar" length="255" name="topic_name" nullable="false" comment="Topic Name"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>

        <constraint xsi:type="foreign"
                    referenceId="SHOPIFY_ACCOUNT_ID_WEBHOOK_ID"
                    table="comave_shopify_webhook_install"
                    column="shopify_account_id"
                    referenceTable="wk_mpmultishopify_seller_details"
                    referenceColumn="entity_id"
                    onDelete="CASCADE"/>

        <constraint xsi:type="unique" referenceId="COMAVE_WEBHOOK_INSTALLER_TOPIC_GQID">
            <column name="shopify_account_id"/>
            <column name="webhook_id"/>
            <column name="topic_name"/>
        </constraint>

        <index referenceId="SHOPIFY_WEBHOOK_INDEX" indexType="btree">
            <column name="shopify_account_id"/>
            <column name="webhook_id"/>
        </index>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
    </table>

    <table name="comave_shopify_webhook_heartbeat" resource="default" engine="innodb" comment="Webhook heartbeat storage">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="webhook_id" padding="10" unsigned="true" nullable="false" identity="false" comment="Webhook Id"/>
        <column xsi:type="json" name="payload" nullable="false" comment="Payload"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>

        <constraint xsi:type="foreign"
                    referenceId="SHOPIFY_WEBHOOK_HEARTBEAT_ID_WEBHOOK_ID"
                    table="comave_shopify_webhook_heartbeat"
                    column="webhook_id"
                    referenceTable="comave_shopify_webhook_install"
                    referenceColumn="id"
                    onDelete="CASCADE"/>

        <index referenceId="SHOPIFY_WEBHOOK_INDEX" indexType="btree">
            <column name="webhook_id"/>
        </index>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
    </table>
</schema>
