<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\ShopifyWebhookInstaller\Api\Data\WebhookHeartbeatInterface"
                type="Comave\ShopifyWebhookInstaller\Model\WebhookHeartbeat"/>

    <preference for="Comave\ShopifyWebhookInstaller\Api\WebhookHeartbeatRepositoryInterface"
                type="Comave\ShopifyWebhookInstaller\Model\WebhookHeartbeatRepository"/>

    <preference for="Comave\ShopifyWebhookInstaller\Api\WebhookInterface"
                type="Comave\ShopifyWebhookInstaller\Model\BaseShopifyWebhook"/>

    <preference for="Comave\ShopifyWebhookInstaller\Api\Data\WebhookInstallResultInterface"
                type="Comave\ShopifyWebhookInstaller\Model\WebhookResult"/>

    <preference for="Comave\ShopifyWebhookInstaller\Api\WebhookResultRepositoryInterface"
                type="Comave\ShopifyWebhookInstaller\Model\WebhookResultRepository"/>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="shopify_webhook_heartbeats_data_source" xsi:type="string">ErrorGridCollection</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave:shopify-webhooks:run" xsi:type="object">Comave\ShopifyWebhookInstaller\Console\Command\WebhookRunner</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="WebhookInstallerLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">WebhookInstallerLogger</argument>
            <argument name="loggerPath" xsi:type="string">webhook_installer</argument>
        </arguments>
    </virtualType>

    <type name="Comave\ShopifyWebhookInstaller\Model\BaseShopifyWebhook">
       <arguments>
           <argument xsi:type="object" name="logger">WebhookInstallerLogger</argument>
       </arguments>
    </type>

    <type name="Comave\ShopifyWebhookInstaller\Observer\TriggerInstallation">
       <arguments>
           <argument xsi:type="object" name="logger">WebhookInstallerLogger</argument>
       </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface">
        <plugin name="processHeartbeat" type="Comave\ShopifyWebhookInstaller\Plugin\AddHeartbeat"/>
    </type>

    <type name="Comave\ShopifyWebhookInstaller\Plugin\AddHeartbeat">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookInstallerLogger</argument>
        </arguments>
    </type>
</config>
