<?php

declare(strict_types=1);

namespace Comave\ShopifyWebhookInstaller\Ui\DataProvider;

use Comave\ShopifyWebhookInstaller\Api\Data\WebhookInstallResultInterface;
use Magento\Framework\App\Http\Context;
use Comave\ShopifyWebhookInstaller\Model\ResourceModel\WebhookHeartbeat\CollectionFactory;

class Grid extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @param Context $context
     * @param CollectionFactory $collectionFactory
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        Context $context,
        CollectionFactory $collectionFactory,
        $name,
        $primaryFieldName,
        $requestFieldName,
        array $meta = [],
        array $data = []
    ) {
        $collection = $collectionFactory->create();
        $collection->getSelect()
            ->join(
                ['sd' => $collection->getTable(WebhookInstallResultInterface::TABLE_NAME)],
                'main_table.webhook_id = sd.id',
                [
                    WebhookInstallResultInterface::TOPIC_NAME
                ]
            )->join(
                ['sa' => $collection->getTable('wk_mpmultishopify_seller_details')],
                'sa.entity_id = sd.shopify_account_id',
                [
                    'shopify_account_id' => 'store_name'
                ]
            );

        $this->collection = $collection;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * @param \Magento\Framework\Api\Filter $filter
     * @return void
     */
    public function addFilter(\Magento\Framework\Api\Filter $filter)
    {
        if ($filter->getField() === 'created_at') {
            $filter->setField('main_table.created_at');
        }

        if ($filter->getField() === 'shopify_account_id') {
            $filter->setField('store_name');
        }
        
        parent::addFilter($filter);
    }
}
