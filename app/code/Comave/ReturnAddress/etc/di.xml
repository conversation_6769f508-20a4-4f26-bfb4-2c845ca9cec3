<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Model\Address">
        <plugin name="comave_returnaddress_address_plugin" type="Comave\ReturnAddress\Plugin\AddressPlugin" sortOrder="10" />
    </type>
    <type name="Magento\Customer\Model\Address\DataProvider">
        <plugin name="comave_returnaddress_address_data_provider_plugin" type="Comave\ReturnAddress\Plugin\DataProviderPlugin" sortOrder="10" />
    </type>
    <type name="Magento\Customer\Api\AddressRepositoryInterface">
        <plugin name="comave_returnaddress_address_delete_before_plugin" type="Comave\ReturnAddress\Plugin\AddressDeleteBeforePlugin" sortOrder="1"/>
    </type>
    <type name="Webkul\MarketplaceBaseShipping\Controller\Shipping\FormPost">
        <plugin name="comave_returnaddress_marketplacebaseshipping_formpost_plugin" type="Comave\ReturnAddress\Plugin\MarketplaceBaseShippingFormPostPlugin" sortOrder="10" />
    </type>
</config>
