<?php

declare(strict_types=1);

namespace Comave\ReturnAddress\Block;

use Comave\SellerShippingCountry\Helper\Data as SellerShippingCountryHelper;
use Magento\Config\Model\Config\Source\Yesno;
use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Helper\Address as AddressHelper;
use Magento\Customer\Model\Address;
use Magento\Customer\Model\Session;
use Magento\Directory\Helper\Data as CustomerDataHelper;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory as CountryCollectionFactory;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory as RegionCollectionFactory;
use Magento\Framework\App\Cache\Type\Config;
use Magento\Framework\Json\EncoderInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;
use Webkul\Marketplace\Helper\Data as MarketplaceDataHelper;

class ReturnAddress extends \Magento\Directory\Block\Data
{
    protected AddressInterface|Address $_address;

    /**
     * Construct function
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Framework\Json\EncoderInterface $jsonEncoder
     * @param \Magento\Framework\App\Cache\Type\Config $configCacheType
     * @param \Magento\Directory\Model\ResourceModel\Region\CollectionFactory $regionCollectionFactory
     * @param \Magento\Directory\Model\ResourceModel\Country\CollectionFactory $countryCollectionFactory
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     * @param \Magento\Customer\Model\Session $_customerSession
     * @param \Magento\Config\Model\Config\Source\Yesno $yesNo
     * @param \Magento\Framework\Registry $_coreRegistry
     * @param \Magento\Directory\Helper\Data $dHelper
     * @param \Magento\Customer\Helper\Address $customerHelper
     * @param \Comave\SellerShippingCountry\Helper\Data $countryRestrictionsHelper
     * @param mixed[] $data
     */
    public function __construct(
        Context $context,
        EncoderInterface $jsonEncoder,
        Config $configCacheType,
        RegionCollectionFactory $regionCollectionFactory,
        CountryCollectionFactory $countryCollectionFactory,
        private readonly MarketplaceDataHelper $marketplaceHelper,
        private readonly Session $_customerSession,
        private readonly Yesno $yesNo,
        private readonly Registry $_coreRegistry,
        private readonly CustomerDataHelper $dHelper,
        private readonly AddressHelper $customerHelper,
        private readonly SellerShippingCountryHelper $countryRestrictionsHelper,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $dHelper,
            $jsonEncoder,
            $configCacheType,
            $regionCollectionFactory,
            $countryCollectionFactory,
            $data
        );
    }

    /**
     * Prepare global layout.
     *
     * @return $this
     */
    public function _prepareLayout()
    {
        parent::_prepareLayout();

        $this->_address = $this->_coreRegistry->registry('return_address');
        $this->setData('country_id', $this->_address->getCountryId());
    }

     /**
      * Return the associated address.
      *
      * @return \Magento\Customer\Api\Data\AddressInterface|\Magento\Customer\Model\Address
      */
    public function getAddress(): AddressInterface|Address
    {
        return $this->_address;
    }

    /**
     * Return the name of the region for the address being edited.
     *
     * @return ?string region name
     */
    public function getRegion(): ?string
    {
        $region = $this->getAddress()->getRegion();

        return $region === null ? '' : $region->getRegion();
    }

    /**
     * Return the name of the region for the address being edited.
     *
     * @return string|int region name
     */
    public function getRegionId(): string|int
    {
        $regionId = $this->getAddress()->getRegionId();

        return $regionId ?? '';
    }

    /**
     * Return the specified numbered street line.
     *
     * @param int $lineNumber
     * @return string
     */
    public function getStreetLine(int $lineNumber): string
    {
        $street = $this->_address->getStreet();

        return $street[$lineNumber - 1] ?? '';
    }

    /**
     * Check if address matches Customer Default Return function
     *
     * @return int
     */
    public function getDefaultReturn(): int
    {
        return (int) ($this->_customerSession->getCustomer()->getData('default_return') === $this->_address->getId());
    }

    /**
     * Get Default Return Options function
     *
     * @return string[]
     */
    public function getDefaultReturnOptions(): array
    {
        return $this->yesNo->toArray();
    }

    /**
     * Return the Url for saving.
     *
     * @return string
     */
    public function getSaveUrl(): string
    {
        return $this->_urlBuilder->getUrl(
            'returnaddress/address/save',
            ['_secure' => true]
        );
    }

    /**
     * Get Directory helper
     *
     * @return \Magento\Directory\Helper\Data
     */
    public function getDHelper(): CustomerDataHelper
    {
        return $this->dHelper;
    }

    /**
     * Get Customer helper
     *
     * @return \Magento\Customer\Helper\Address
     */
    public function getCustomerHelper(): AddressHelper
    {
        return $this->customerHelper;
    }

    /**
     * Get Country Restrictions Helper function
     *
     * @return \Comave\SellerShippingCountry\Helper\Data
     */
    public function getCountryRestrictionsHelper(): SellerShippingCountryHelper
    {
        return $this->countryRestrictionsHelper;
    }
}
