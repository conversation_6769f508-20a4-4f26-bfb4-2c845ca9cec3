<?php

declare(strict_types=1);

namespace Comave\ReturnAddress\Ui\DataProvider;

use Magento\Customer\Model\ResourceModel\Address\Grid\Collection;
use Magento\Customer\Model\ResourceModel\Address\Grid\CollectionFactory;
use Magento\Directory\Model\Country;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\App\RequestInterface;
use Webkul\Marketplace\Helper\Data;

class CustomerAddressListDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * Construct function
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param \Magento\Customer\Model\ResourceModel\Address\Grid\CollectionFactory $collectionFactory
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Webkul\Marketplace\Helper\Data $helper
     * @param \Magento\Framework\App\RequestInterface $request
     * @param mixed[] $meta
     * @param mixed[] $data
     */
    public function __construct(
        string $name,
        string $primaryFieldName,
        string $requestFieldName,
        private readonly CollectionFactory $collectionFactory,
        private readonly CountryFactory $countryFactory,
        private readonly Data $helper,
        private readonly RequestInterface $request,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);

        $this->collection = $this->collectionFactory->create();
    }

    /**
     * Add country key for default return blocks on seller addresses tab
     *
     * @return mixed[]
     */
    public function getData(): array
    {
        $countryDirectoryModel = $this->countryFactory->create();
        $collection = $this->getCollection();

        $this->addFiltersToCollection($collection);

        $data = $collection->toArray();
        $data['items'] = $this->addCountryNamesToItems($data['items'], $countryDirectoryModel);

        return $data;
    }

    /**
     * Add filters to the collection
     *
     * @param \Magento\Customer\Model\ResourceModel\Address\Grid\Collection $collection
     * @return void
     */
    private function addFiltersToCollection(Collection $collection): void
    {
        $collection->addFieldToFilter('parent_id', ['eq' => $this->helper->getCustomerId()]);

        $collection->getSelect()->join(
            ['address_varchar' => $collection->getTable('customer_address_entity_varchar')],
            'main_table.entity_id = address_varchar.entity_id AND address_varchar.attribute_id = (
                SELECT attribute_id
                FROM eav_attribute
                WHERE attribute_code = "save_as_address_type"
                AND entity_type_id = (
                    SELECT entity_type_id
                    FROM eav_entity_type
                    WHERE entity_type_code = "customer_address"
                )
            )',
            []
        );

        $collection->addFieldToFilter('address_varchar.value', [
            'eq' => \Comave\CustomerAddressType\Model\Config\AddressTypeOptions::OPTION_RETURN
        ]);
    }

    /**
     * Add country names to items
     *
     * @param mixed[] $items
     * @param \Magento\Directory\Model\Country $countryDirectoryModel
     * @return mixed[]
     */
    private function addCountryNamesToItems(array $items, Country $countryDirectoryModel): array
    {
        foreach ($items as $key => $item) {
            if (isset($item['country_id']) && !isset($item['country'])) {
                $items[$key]['country'] = $countryDirectoryModel->loadByCode($item['country_id'])->getName();
            }
        }

        return $items;
    }
}
