<?php

declare(strict_types=1);

namespace Comave\ReturnAddress\Controller\Address;

use Comave\ReturnAddress\Model\DefaultReturnAddressUpdater;
use Magento\Customer\Model\Address;
use Magento\Customer\Model\AddressFactory;
use Magento\Customer\Model\Metadata\FormFactory;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;

class Save extends \Magento\Framework\App\Action\Action
{
    /**
     * Construct function
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     * @param \Magento\Customer\Model\AddressFactory $addressFactory
     * @param \Magento\Customer\Model\Session $_customerSession
     * @param \Comave\ReturnAddress\Model\DefaultReturnAddressUpdater $defaultReturnAddressUpdater
     * @param \Magento\Customer\Model\Metadata\FormFactory $formFactory
     */
    public function __construct(
        Context $context,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly AddressFactory $addressFactory,
        private readonly Session $_customerSession,
        private readonly DefaultReturnAddressUpdater $defaultReturnAddressUpdater,
        private readonly FormFactory $formFactory
    ) {
        parent::__construct($context);
    }

    /**
     * Execute function
     *
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function execute(): ResponseInterface
    {
        $request = $this->getRequest();
        $data = $request->getPostValue();

        if ($this->hasValidationErrors($request)) {
            return $this->handleValidationErrors($data);
        }

        if (!$data) {
            return $this->handleMissingPostData();
        }

        return $this->handleAddressSave($data);
    }

    /**
     * Check if data has Validation Errors function
     *
     * @param \Magento\Framework\App\RequestInterface $request
     * @return bool
     */
    private function hasValidationErrors(RequestInterface $request): bool
    {
        $addressForm = $this->formFactory->create('customer_address', 'adminhtml_customer_address');
        $formData = $addressForm->extractData($request);
        $errors = $addressForm->validateData($formData);

        if ($errors !== true) {
            $this->messageManager->addError(implode(' ', $errors));

            return true;
        }

        return false;
    }

    /**
     * Handle Validation Errors function
     *
     * @param mixed[] $data
     * @return \Magento\Framework\App\ResponseInterface
     */
    private function handleValidationErrors(array $data): ResponseInterface
    {
        return !empty($data['entity_id'])
            ? $this->_redirect('*/*/edit', ['entity_id' => $data['entity_id']])
            : $this->_redirect('*/*/new');
    }

    /**
     * Handle Missing Post Data function
     *
     * @return \Magento\Framework\App\ResponseInterface
     */
    private function handleMissingPostData(): ResponseInterface
    {
        $this->messageManager->addErrorMessage(__('Address Save form action missing post data.'));

        return $this->_redirect('marketplace/account/dashboard');
    }

    /**
     * Handle Address Save function
     *
     * @param mixed[] $data
     * @return \Magento\Framework\App\ResponseInterface
     */
    private function handleAddressSave(array $data): ResponseInterface
    {
        try {
            $address = $this->saveAddress($data);
            $this->messageManager->addSuccessMessage(__('Address saved successfully.'));

            return $this->_redirect('*/*/edit', ['entity_id' => $address->getId()]);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        $this->dataPersistor->set('address_form_data', $data);

        return !empty($data['entity_id'])
            ? $this->_redirect('*/*/edit', ['entity_id' => $data['entity_id']])
            : $this->_redirect('*/*/new');
    }

    /**
     * Save Address function
     *
     * @param mixed[] $data
     * @return \Magento\Customer\Model\Address
     */
    private function saveAddress(array $data): Address
    {
        $customer = $this->_customerSession->getCustomer();
        $address = $this->addressFactory->create();

        if (!empty($data['entity_id'])) {
            $address->load($data['entity_id']);
        }

        $address->setData($data);
        $address->setCustomerId($customer->getId());
        $address->save();

        $this->defaultReturnAddressUpdater->updateDefaultReturn($customer, $data['default_return'], $address);

        return $address;
    }
}
