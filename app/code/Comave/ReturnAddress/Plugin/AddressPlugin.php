<?php

declare(strict_types=1);

namespace Comave\ReturnAddress\Plugin;

use Comave\CustomerAddressType\Model\Config\AddressTypeOptions;
use Comave\ReturnAddress\Model\DefaultReturnAddressUpdater;
use Magento\Customer\Model\Address;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class AddressPlugin
{
    /**
     * Construct function
     *
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Magento\Customer\Model\ResourceModel\Customer $resource
     * @param \Comave\ReturnAddress\Model\DefaultReturnAddressUpdater $defaultReturnAddressUpdater
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly RequestInterface $request,
        private readonly CustomerFactory $customerFactory,
        private readonly CustomerResource $resource,
        private readonly DefaultReturnAddressUpdater $defaultReturnAddressUpdater,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Before Save plugin function
     *
     * @param \Magento\Customer\Model\Address $subject
     * @return \Magento\Customer\Model\Address[]|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function beforeSave(Address $subject): ?array
    {
        $defaultReturnAddress = $this->request->getParam('default_return');
        $saveAsAddressType = $this->request->getParam('save_as_address_type');

        if ($saveAsAddressType === AddressTypeOptions::OPTION_RETURN || empty($saveAsAddressType)) {
            return null;
        }

        // Check if default_return is set and save_as_address_type is not OPTION_RETURN
        if (!empty($defaultReturnAddress) && $saveAsAddressType !== AddressTypeOptions::OPTION_RETURN) {
            throw new LocalizedException(
                __('Cannot assign "Default Return Address" for a non "Return" type for "Save Address Type As".')
            );
        }

        return null;
    }

    /**
     * After Save function
     *
     * @param \Magento\Customer\Model\Address $subject
     * @param \Magento\Customer\Model\Address $result
     * @return \Magento\Customer\Model\Address
     */
    public function afterSave(Address $subject, Address $result): Address
    {
        $defaultReturnAddress = $this->request->getParam('default_return');
        $customerId = $result->getCustomerId();

        if (!isset($defaultReturnAddress) || !$customerId) {
            return $result;
        }

        try {
            $customer = $this->loadCustomer((int) $customerId);
            $this->defaultReturnAddressUpdater->updateDefaultReturn($customer, $defaultReturnAddress, $result);
        } catch (\Exception $e) {
            $this->logger->error(
                'Error saving default return address: ' . $e->getMessage(),
                [
                    'address_id' => $result->getId(),
                    'customer_id' => $customerId,
                    'exception' => $e,
                ]
            );
        }

        return $result;
    }

    /**
     * Load Customer function
     *
     * @param int $customerId
     * @return \Magento\Customer\Model\Customer
     */
    private function loadCustomer(int $customerId): Customer
    {
        $customer = $this->customerFactory->create();
        $this->resource->load($customer, $customerId);

        return $customer;
    }
}
