<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Controller\Adminhtml\ShopifyAccount;

use Comave\SellerApi\Service\RequestHandler;
use Comave\ShopifyAccounts\Model\Command\GetProductsGraphqlString;
use Comave\ShopifyAccounts\Model\Command\GetShopCategoriesGraphqlString;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Comave\ShopifyAccounts\Service\Token;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Throwable;

class Check extends Action implements HttpGetActionInterface
{
    /**
     * @param Token $tokenService
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param RequestHandler $requestHandler
     * @param GetProductsGraphqlString $getProductsGraphqlString
     * @param GetShopCategoriesGraphqlString $getShopCategoriesGraphqlString
     * @param Context $context
     */
    public function __construct(
        private readonly Token $tokenService,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly RequestHandler $requestHandler,
        private readonly GetProductsGraphqlString $getProductsGraphqlString,
        private readonly GetShopCategoriesGraphqlString $getShopCategoriesGraphqlString,
        Context $context
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        $jsonData = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        //check the id param posted
        /** @var string|null $accountId */
        $accountId = $this->getRequest()->getParam('id');
        /** @var string|null $checkType */
        $checkType = $this->getRequest()->getParam('type');

        if (empty($accountId)) {
            return $jsonData->setData([
                'success' => false,
                'message' => __('No account identified'),
            ]);
        }

        $success = true;
        $tokenScope = true;

        try {
            $tokenScopeResult = $this->tokenService->readTokenScopes($accountId, $checkType);

            if (!$tokenScopeResult) {
                $success = false;
                $message = $tokenScopeResult;
            } else {
                $graphqlQuery = match($checkType) {
                    Token::ACCESS_TYPE_CATEGORY => json_encode([
                        'query' => $this->getShopCategoriesGraphqlString->get()
                    ]),
                    Token::ACCESS_TYPE_PRODUCT => json_encode([
                        'query' => $this->getProductsGraphqlString->get()
                    ]),
                    default => false
                };
                $configurableApi = $this->configurableApiFactory->create([
                    'shopifyAccountId' => $accountId,
                ])->setEndpoint(
                    in_array($checkType, [Token::ACCESS_TYPE_CATEGORY, Token::ACCESS_TYPE_PRODUCT]) ?
                        ConfigurableApi::GRAPHQL :
                        ConfigurableApi::SHOPIFY_ORDER_ENDPOINT_TYPE
                )->setParams($graphqlQuery ?: '')
                ->setMethod('POST');

                $response = $this->requestHandler->handleRequest($configurableApi);
                $responseContents = $response->getResult()->getBody()->getContents();
                $decoded = json_decode($responseContents, true);
                $message = substr($responseContents, 0, 100).'...';

                if ($response->hasError()) {
                    $success = $checkType === Token::ACCESS_TYPE_DRAFT_ORDERS && isset($decoded['errors']['draft_order']);
                    $message = $responseContents;
                }
            }
        } catch (Throwable $e) {
            $success = false;
            $message = $e->getMessage();
        }

        $apiAvailability = $success ?
            '<span style="color: green;">Available</span>' :
            '<span style="color: red;">Invalid</span>';
        $tokenScopeValid = $tokenScope ?
            '<span style="color: green;">Available</span>' :
            '<span style="color: red;">Invalid</span>';

        $result = [
            'content' => "<ul style='word-break: break-all'>
        <li>API Availability: $apiAvailability</li>
        <li>Response: $message</li>
        <li>Token Scope: $tokenScopeValid</li>
</ul>",
        ];

        return $jsonData->setData($result);
    }
}
