<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Controller\Adminhtml\Products;

use Comave\SellerApi\Model\IntegrationTypePool;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Comave\ShopifyAccounts\Model\Command\FormatImportData;
use Comave\ShopifyAccounts\Model\Command\ShopifyToMagentoMapper;
use Comave\ShopifyAccounts\Model\Command\TmpDeleteItems;
use Comave\ShopifyAccounts\Model\Queue\Consumer\ProcessCategoryMapping;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Registry;
use Magento\Store\Model\StoreManagerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ImportedtmpproductRepositoryInterface;

class Createproduct extends \Magento\Backend\App\Action implements HttpPostActionInterface
{
    /**
     * @param Context $context
     * @param ImportedtmpproductRepositoryInterface $repository
     * @param Registry $registry
     * @param IntegrationTypePool $integrationTypePool
     * @param PublisherInterface $publisher
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     * @param StoreManagerInterface $storeManager
     * @param TmpDeleteItems $tmpDeleteItems
     * @param FormatImportData $formatImportData
     * @param ShopifyToMagentoMapper $shopifyToMagentoMapper
     */
    public function __construct(
        Context $context,
        private readonly IntegrationTypePool $integrationTypePool,
        private readonly ImportedtmpproductRepositoryInterface $repository,
        private readonly Registry $registry,
        private readonly PublisherInterface $publisher,
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
        private readonly StoreManagerInterface $storeManager,
        private readonly TmpDeleteItems $tmpDeleteItems,
        private readonly FormatImportData $formatImportData,
        private readonly ShopifyToMagentoMapper $shopifyToMagentoMapper,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        $this->registry->register('shopify_prevent_cached_tags', true);
        $jsonResult = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $shopifyAccountId = $this->getRequest()->getParam('ruleId');
        $skippedErrors = [];
        $magentoProducts = [];
        $totalCount = min(
            $this->repository->getCollectionByProductTypeAndRuleId(
                'product',
                $shopifyAccountId
            )->getSize(),
            5
        );

        try {
            $this->storeManager->setCurrentStore(0);
            $magentoProducts = $this->formatImportData->execute($shopifyAccountId);

            if (empty($magentoProducts)) {
                return $jsonResult->setData([
                    'msg' => __(
                        'No more products found to import, possible errors encountered, finishing'
                    ),
                    'error' => 0,
                    'skipped_errors' => $skippedErrors,
                    'total_processed' => $totalCount
                ]);
            }
        } catch (\Exception $e) {
            $skippedErrors[] = $e->getMessage();
        }

        if (empty($magentoProducts) || current($magentoProducts) === '__n') {
            return $jsonResult->setData([
                'msg' => current($magentoProducts) === '__n' ?
                    __(
                        'Finished importing products, images/categories will be processed shortly'
                    ) : '',
                'skipped_errors' => array_merge(
                    $skippedErrors,
                    [
                        current($magentoProducts) === '__n' ?
                            '' :
                            __('Processed batched products with errors, review import data', $shopifyAccountId)
                    ]
                ),
                'total_processed' => $totalCount,
                'error' => 0
            ]);
        }

        try {
            foreach ($magentoProducts as $tmpId => $product) {
                try {
                    /**
                     * @see VariationProduct why we call the save directly
                     */
                    $product->save();
//                    $savedProduct = $this->productRepository->save($product);
                } catch (\Exception $e) {
                    $message = __('Skipped %1, error %2', $product->getSku(), $e->getMessage());
                    $skippedErrors[] = $message;
                    $this->tmpDeleteItems->addErrorContext(
                        $product->getSku(),
                        (string) $tmpId,
                        (string) $message
                    );

                    continue;
                }

                $this->shopifyToMagentoMapper->addMapping(
                    $shopifyAccountId,
                    $product
                );
            }

            $this->mediaGalleryRegistry->process();
            $this->shopifyToMagentoMapper->processMapping();
            $this->tmpDeleteItems->delete();
            $this->publisher->publish(
                ProcessCategoryMapping::TOPIC_NAME,
                $shopifyAccountId
            );
            $this->integrationTypePool->populate('shopify');

            return $jsonResult->setData([
                'msg' => __(
                    'Finished importing products, images/categories will be processed shortly'
                ),
                'error' => 0,
                'skipped_errors' => $skippedErrors,
                'total_processed' => $totalCount
            ]);
        } catch (\Exception $e) {
            return $jsonResult->setData([
                'msg' => $e->getMessage(),
                'error' => 1,
                'actual_error' => $e->getMessage(),
                'skipped_errors' => $skippedErrors,
                'total_processed' => $totalCount
            ]);
        }
    }
}
