<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Controller\Adminhtml\Products;

use Comave\ShopifyAccounts\Model\Command\GetProductsGraphqlString;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Comave\SellerApi\Service\RequestHandler;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Message\ResponseInterface;

class Import extends \Magento\Backend\App\Action implements HttpPostActionInterface
{
    /**
     * @param Context $context
     * @param GetProductsGraphqlString $getProductsGraphqlString
     * @param SerializerInterface $serializer
     * @param RequestHandler $requestHandler
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        Context $context,
        private readonly GetProductsGraphqlString $getProductsGraphqlString,
        private readonly SerializerInterface $serializer,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly ResourceConnection $resourceConnection,
    ) {
        parent::__construct($context);
    }

    public function execute(): Json
    {
        $json = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $accountId = $this->getRequest()->getParam('id');

        if (empty($accountId)) {
            return $json->setData([
                'data' => '',
                'error_msg' => __('Account ID not specified')
            ]);
        }

        $connection = $this->resourceConnection->getConnection();
        $after = null;

        if (empty($this->getRequest()->getParam('page_info'))) {
            $connection->delete(
                $connection->getTableName('wk_mpmultishopify_tempshopify'),
                [
                    "rule_id = ?" => $accountId,
                    "error_context IS NULL"
                ]
            );
        } else {
            $after = $this->getRequest()->getParam('page_info');
        }

        $queryJson = json_encode([
            'query' => trim($this->getProductsGraphqlString->get($after))
        ]);
        $configurableApi = $this->configurableApiFactory->create([
            'shopifyAccountId' => $accountId,
        ])->setEndpoint(ConfigurableApi::GRAPHQL)
        ->setParams($queryJson)
        ->setMethod('POST');

        try {
            $response = $this->requestHandler->handleRequest($configurableApi);

            if ($response->hasError()) {
                throw new \Exception($response->getResult()->getReasonPhrase());
            }

            $responseResult = $response->getResult();
            $decodedResponse = $this->serializer->unserialize(
                $responseResult->getBody()->getContents()
            );

            if (!empty($decodedResponse['errors'])) {
                throw new \Exception(
                    implode(', ', $decodedResponse['errors'])
                );
            }

            $productsList = $decodedResponse['data']['products']['edges'] ?? [];

            if (empty($productsList)) {
                throw new LocalizedException(__('Unable to process product list'));
            }

            $tmpData = [];
            array_map(function ($productNode) use (&$tmpData, $accountId) {
                $product = $productNode['node'];
                $tmpData[] = [
                    'item_type' => 'product',
                    'item_id' => preg_replace("/[^0-9]/", "", $product['id']),
                    'product_data' => $this->serializer->serialize($product),
                    'associate_products' => $this->serializer->serialize(
                        $product['variants'] ?? []
                    ),
                    'rule_id' => $accountId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'error_context' => null,
                    'error_timestamp' => null
                ];
            }, $productsList);

            $connection->insertOnDuplicate(
                $connection->getTableName('wk_mpmultishopify_tempshopify'),
                $tmpData,
                array_keys(current($tmpData))
            );

            $nextPageInfo = (
                ($decodedResponse['data']['products']['pageInfo']['hasNextPage'] ?? false) === true ?
                    $decodedResponse['data']['products']['pageInfo']['endCursor'] : ''
            );

            $resultData = [
                'data' => $productsList,
                'error_msg' => false,
                'next_page_info' => $nextPageInfo,
                'total_imported' => 0,
            ];

            return $json->setData($resultData);
        } catch (\Exception $e) {
            return $json->setData([
                'data' => '',
                'error_msg' => __($e->getMessage())
            ]);
        }
    }
}
