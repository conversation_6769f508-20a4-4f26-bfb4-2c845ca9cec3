<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Seller;

use Comave\SellerOnboarding\Model\Category\DataSourceUiManager;
use Comave\ShopifyAccounts\Console\ProgressBar;
use Comave\ShopifyAccounts\Console\ProgressBarFactory;
use Comave\ShopifyAccounts\Service\Seller\CategoryFetcher;
use Comave\ShopifyAccounts\Service\Token;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\SerializerInterface;
use Symfony\Component\Console\Output\OutputInterface;


class CategoryProcessor
{
    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    /**
     * @param \Comave\ShopifyAccounts\Service\Seller\CategoryFetcher $categoryFetcher
     * @param \Comave\ShopifyAccounts\Service\Token $tokenService
     * @param \Comave\ShopifyAccounts\Console\ProgressBarFactory $progressBarFactory
     * @param \Comave\SellerOnboarding\Model\Category\DataSourceUiManager $dataSourceManager
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     */
    public function __construct(
        private readonly CategoryFetcher $categoryFetcher,
        private readonly Token $tokenService,
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly DataSourceUiManager $dataSourceManager,
        private readonly SerializerInterface $serializer,
    ) {
    }

    /**
     * @param array $shopifySeller
     * @return void
     */
    public function process(array $shopifySeller): void
    {
        if (empty($shopifySeller['seller_id']) || empty($shopifySeller['account_id'])) {
            return;
        }

        $progressBar = null;
        $sellerId = (int)$shopifySeller['seller_id'];
        $sellerCategories = $this->getSellerCategories($shopifySeller['account_id']);

        if ($this->hasOutput()) {
            /** @var $progressBar ProgressBar */
            $progressBar = $this->progressBarFactory->create();
            $progressBar->setOutput($this->getOutput());
            $progressBar->start($shopifySeller['account_id']);
        }

        foreach ($sellerCategories as $category) {
            try {
                $dataSource = $this->dataSourceManager->getBySellerId(
                    $category['id'],
                    $sellerId
                );
                $dataSource->setSellerId($sellerId);
                $dataSource->setSourceCategoryId($category['id']);
                $dataSource->setSourceCategoryName($category['title']);
                $dataSource->setSourceMetadata($this->serializer->serialize($category));
                $this->dataSourceManager->save($dataSource);
            } catch (LocalizedException|NoSuchEntityException $exception) {
                $this->hasOutput() && $this->getOutput()->writeln(
                    sprintf(
                        "<error>%s</error>",
                        __('Seller category cannot be processed.')
                    )
                );
            }

            if ($this->hasOutput() && !is_null($progressBar)) {
                $progressBar->advance($shopifySeller['account_id']);
            }
        }

        if ($this->hasOutput() && !is_null($progressBar)) {
            $progressBar->finish($shopifySeller['account_id']);
        }
    }

    /**
     * @param null|OutputInterface $output
     */
    public function setOutput(?OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @return bool
     */
    private function hasOutput(): bool
    {
        return !is_null($this->output);
    }

    /**
     * @param $accountId
     * @return array
     */
    private function getSellerCategories($accountId): array
    {
        if (empty($accountId)) {
            return [];
        }

        if (!$this->tokenService->readTokenScopes($accountId, Token::ACCESS_TYPE_CATEGORY)) {
            return [];
        }

        return $this->categoryFetcher->getCategories($accountId);
    }
}
