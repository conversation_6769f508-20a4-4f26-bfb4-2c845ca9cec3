<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\ShopifyWebhookInstaller\Model\ConfigProvider;
use Magento\Framework\UrlInterface;

class GetCategoryProductsGraphqlString
{
    /**
     * @param string|null $after
     * @return string
     */
    public function get(?string $after = null): string {
        return sprintf(
            'query {
    products(first: 100, query: "%s"%s) {
    pageInfo {
        hasNextPage
        endCursor
    }
    edges {
      node {
        id
        handle
        title
        category {
            id
            name
        }
        productType
      }
    }
  }
}',
            "(product_publication_status:published) AND (status:ACTIVE)",
            $after !== null ? sprintf(', after:"%s"', $after) : '',
        );
    }
}
