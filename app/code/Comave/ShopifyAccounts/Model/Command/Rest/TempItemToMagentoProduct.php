<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command\Rest;

use Comave\ShopifyAccounts\Model\Command\CategoryMapper;
use Comave\ShopifyAccounts\Model\Command\InventoryRegistry;
use Comave\ShopifyAccounts\Model\Command\MediaGalleryFormatter;
use Comave\ShopifyAccounts\Model\Handlers\Rest\VariationProduct;
use Magento\Catalog\Api\AttributeSetRepositoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Eav\Api\Data\AttributeSetInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Magento\Tax\Helper\Data;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data as ShopifyHelper;
use Webkul\MpMultiShopifyStoreMageConnect\Model\Importedtmpproduct;

class TempItemToMagentoProduct
{
    /**
     * @var array
     */
    private array $loadedAccountConfiguration = [];

    /**
     * @param InventoryRegistry $inventoryRegistry
     * @param CategoryMapper $categoryMapper
     * @param ShopifyHelper $shopifyHelper
     * @param MediaGalleryFormatter $mediaGalleryFormatter
     * @param SerializerInterface $serializer
     * @param ProductInterfaceFactory $productFactory
     * @param Data $taxHelper
     * @param ProductRepositoryInterface $productRepository
     * @param WebsiteRepositoryInterface $websiteRepository
     * @param AttributeSetRepositoryInterface $attributeSetRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param VariationProduct $variationProduct
     * @param ShopifyToMagentoMapper $shopifyToMagentoMapper
     */
    public function __construct(
        private readonly InventoryRegistry $inventoryRegistry,
        private readonly CategoryMapper $categoryMapper,
        private readonly ShopifyHelper $shopifyHelper,
        private readonly MediaGalleryFormatter $mediaGalleryFormatter,
        private readonly SerializerInterface $serializer,
        private readonly ProductInterfaceFactory $productFactory,
        private readonly Data $taxHelper,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly WebsiteRepositoryInterface $websiteRepository,
        private readonly AttributeSetRepositoryInterface $attributeSetRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly VariationProduct $variationProduct,
        private readonly ShopifyToMagentoMapper $shopifyToMagentoMapper,

    ) {
    }

    /**
     * @param Importedtmpproduct $tmpProduct
     * @param string $shopifyAccountId
     * @return ProductInterface|null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Importedtmpproduct $tmpProduct, string $shopifyAccountId): ?ProductInterface
    {
        $unserialisedData = $this->serializer->unserialize($tmpProduct->getProductData() ?: '{}');
        if (empty($unserialisedData)) {
            return null;
        }

        if (!isset($this->loadedAccountConfiguration[$shopifyAccountId])) {
            $this->loadedAccountConfiguration[$shopifyAccountId] = $this->shopifyHelper->getShopifyConfiguration($shopifyAccountId);
        }

        $productSku = sprintf('%s-%s', $unserialisedData['id'], $shopifyAccountId);

        $accountAttributeSetId = $this->shopifyHelper->getAttributeSetId(
            $shopifyAccountId
        );
        $website = $this->shopifyHelper->getDefaultWebsiteCode($shopifyAccountId) ?? 'base';
        $productType = $this->getProductType($unserialisedData);
        $productVisibility = $this->getProductVisibility($unserialisedData);

        /** @var Product $product */
        try {
            $product = $this->productRepository->get($productSku, true, null, true);
        } catch (\Exception) {
            $product = $this->productFactory->create();
            $product->setUrlKey($productSku);
            $assignedWebsite = $this->websiteRepository->get($website);
            if (empty($accountAttributeSetId)) {
                $attributeSetList = $this->attributeSetRepository->getList(
                    $this->searchCriteriaBuilder->addFilter(
                        'attribute_set_name',
                        'Default'
                    )->create()
                );
                /** @var AttributeSetInterface $attributeSet */
                $attributeSet = current($attributeSetList->getItems());
                $accountAttributeSetId = $attributeSet->getAttributeSetId();
            }

            $product->setTypeId($productType)
                ->setStatus(
                    Product\Attribute\Source\Status::STATUS_DISABLED
                )->setAttributeSetId($accountAttributeSetId)
                ->setVisibility($productVisibility)
                ->setTaxClassId(
                    $this->taxHelper->getDefaultProductTaxClass()
                )->setWebsiteIds([$assignedWebsite->getId()])
                ->setAttributeSetId($accountAttributeSetId)
                ->setWeight(0.1)
                ->setSku($productSku);
        }

        $product
            ->setName($unserialisedData['title'])
            ->setCustomAttribute(
                'description',
                strip_tags($unserialisedData['body_html'] ?? '')
            )->setCustomAttribute(
                'short_description',
                $unserialisedData['tags'] ?? ''
            );

        $specialPrice = (float) ($unserialisedData['special_price'] ?? 0);
        if ($specialPrice > 0) {
            $product->setCustomAttribute(
                'special_price',
                $specialPrice
            );
        }

        if ($productType === 'simple') {
            if (isset($unserialisedData['inventory_item_id'])) {
                $this->inventoryRegistry->set(
                    $productSku,
                    (string) $unserialisedData['inventory_item_id']
                );
            }

            if (isset($unserialisedData['inventory_quantity'])) {
                $product->setData(
                    'quantity_and_stock_status',
                    [
                        'is_in_stock' => $unserialisedData['inventory_quantity'] > 0 ? 1 : 0,
                        'qty' => $unserialisedData['inventory_quantity'],
                    ]
                );
            }

            if (isset($unserialisedData['price'])) {
                $product->setPrice(
                    (float) $unserialisedData['price']
                );
            }
        }

        if ($this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'] > 0) {
            $product->setData(
                'assign_seller',
                [
                    'seller_id' => $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
                ]
            );
        }

        if ($product->getTypeId() !== $productType) {
            $product->setTypeId($productType);
            $product->setVisibility($productVisibility);
        }

        $categoryId = $this->categoryMapper->getByShopifyName(
            $productSku,
            $unserialisedData['category']['name'] ?: $unserialisedData['product_type'],
            $shopifyAccountId,
            $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
            $unserialisedData,
        );

        if (!empty($categoryId)) {
            $product->setCategoryIds([$categoryId]);
        }

        $variants = $this->getProductVariants($unserialisedData);

        if (!empty($variants) && $product->getTypeId() === 'configurable') {
            $product = $this->variationProduct->handle(
                $shopifyAccountId,
                $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
                $product,
                $variants['variants'],
                $variants['options']
            );
        }

        $this->shopifyToMagentoMapper->addMapping(
            $shopifyAccountId,
            $product,
            $tmpProduct
        );

        if (empty($unserialisedData['images'])) {
            return $product;
        }

        $this->mediaGalleryFormatter->execute(
            $product,
            $unserialisedData,
            $shopifyAccountId
        );

        return $product;
    }

    /**
     * @param array $unserialisedData
     * @return string
     */
    private function getProductType(array $unserialisedData): string
    {
        if (isset($unserialisedData['variations'])) {
            return !empty($unserialisedData['variations']) &&
                count($unserialisedData['variations']) > 1 ?
                    'configurable' : 'simple';
        }

        if (isset($unserialisedData['variants'])) {
            return !empty($unserialisedData['variants']) &&
                count($unserialisedData['variants']) > 1 ?
                    'configurable' : 'simple';
        }

        return 'simple';
    }

    /**
     * @param array $unserialisedData
     * @return int
     */
    private function getProductVisibility(array $unserialisedData): int
    {
        if (isset($unserialisedData['variations'])) {
            return empty($unserialisedData['variations']) ||
                (count($unserialisedData['variations']) <= 1 || count($unserialisedData['variations']) > 1) ?
                    Product\Visibility::VISIBILITY_BOTH :
                    Product\Visibility::VISIBILITY_NOT_VISIBLE;
        }

        if (isset($unserialisedData['variants'])) {
            return empty($unserialisedData['variants']) ||
                (count($unserialisedData['variants']) <= 1 || count($unserialisedData['variants']) > 1) ?
                    Product\Visibility::VISIBILITY_BOTH :
                    Product\Visibility::VISIBILITY_NOT_VISIBLE;
        }

        return Product\Visibility::VISIBILITY_BOTH;
    }

    /**
     * @param array $unserialisedData
     * @return array
     */
    private function getProductVariants(array $unserialisedData): array
    {
        if (isset($unserialisedData['variations'])) {
            return [
                'variants' => $unserialisedData['variations'],
                'options' => $unserialisedData['options'],
            ];
        }

        if (isset($unserialisedData['variants'])) {
            return [
                'variants' => $unserialisedData['variants'],
                'options' => $unserialisedData['options'],
            ];
        }

        return [];
    }
}
