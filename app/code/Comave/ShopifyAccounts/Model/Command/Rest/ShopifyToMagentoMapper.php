<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command\Rest;

use Comave\ShopifyAccounts\Model\Command\InventoryRegistry;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Model\Importedtmpproduct;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data;

class ShopifyToMagentoMapper
{
    private array $mapping = [];

    /**
     * @param Data $sellerHelper
     * @param LoggerInterface $logger
     * @param InventoryRegistry $inventoryRegistry
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly Data $sellerHelper,
        private readonly LoggerInterface $logger,
        private readonly InventoryRegistry $inventoryRegistry,
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param string $shopifyAccountId
     * @param ProductInterface $product
     * @param Importedtmpproduct|null $importedtmpproduct
     * @param array|null $variationData
     * @return void
     */
    public function addMapping(
        string $shopifyAccountId,
        ProductInterface $product,
        ?Importedtmpproduct $importedtmpproduct = null,
        ?array $variationData = null
    ): void {
        if (isset($variationData['inventory_item_id'])) {
            $this->inventoryRegistry->set(
                $product->getSku(),
                (string) $variationData['inventory_item_id']
            );
        }

        if (isset($this->mapping[$product->getSku()]) && $product->getEntityId()) {
            $productData = [
                'magento_pro_id' => $product->getEntityId(),
            ];
            if ($product->hasData('_shopify_variants')) {
                $productData['shopify_variant_map'] = json_encode(
                    $product->getData('_shopify_variants')
                );
            }

            $this->mapping[$product->getSku()] = array_merge(
                $this->mapping[$product->getSku()],
                $productData
            );

            return;
        }

        if ($importedtmpproduct === null && $variationData === null) {
            return;
        }

        $config = $this->sellerHelper->getShopifyConfiguration(
            $shopifyAccountId
        );
        $mageCatId =  current($product->getCategoryIds() ?? []) ?: (
            $config['default_cate'] ?? 2
        );
        $this->mapping[$product->getSku()] = [
            'magento_pro_id' => $product->getEntityId(),
            'shopify_pro_id' => is_array($variationData) ?
                $variationData['id'] :
                $importedtmpproduct?->getItemId(),
            'name' => $product->getName(),
            'product_club' => $config['product_club'] ?? 0,
            'seller_id' => $config['seller_id'] ?? 0,
            'product_type' => $product->getTypeId(),
            'price' => $product->getPrice(),
            'mage_cat_id' => is_array($mageCatId) ? implode(',', $mageCatId) : $mageCatId,
            'last_imported' => date('Y-m-d H:i:s'),
            'change_status' => 0,
            'rule_id' => $shopifyAccountId,
            'created' => date('Y-m-d H:i:s'),
            'shopify_variant_map' => $product->hasData('_shopify_variants') ?
                json_encode(
                    $product->getData('_shopify_variants')
                ) : "{}"
        ];
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function processMapping(): void
    {
        $finalMapping = [];
        $clubMapping = [];

        foreach ($this->mapping as &$mapping) {
            if (!isset($mapping['magento_pro_id'])) {
                continue;
            }

            $clubMapping[] = [
                'club_id' => $mapping['product_club'],
                'product_id' => $mapping['magento_pro_id'],
                'position' => 0
            ];

            unset($mapping['product_club']);
            $finalMapping[] = $mapping;
        }

        if (empty($finalMapping)) {
            $this->mapping = [];
            return;
        }

        $connection = $this->resourceConnection->getConnection();
        $connection->beginTransaction();

        try {
            $connection->insertOnDuplicate(
                $connection->getTableName('wk_mpmultishopifysynchronize_product'),
                $finalMapping,
                [
                    'shopify_variant_map',
                    'last_imported',
                    'mage_cat_id',
                    'magento_pro_id',
                    'price',
                    'product_type',
                    'seller_id',
                    'name'
                ]
            );

            $connection->delete(
                $connection->getTableName('comave_club_product'),
                [
                    'product_id IN (?)' => array_column($clubMapping, 'product_id'),
                ]
            );

            $connection->insertOnDuplicate(
                $connection->getTableName('comave_club_product'),
                $clubMapping
            );

            $connection->commit();
            $this->inventoryRegistry->process();
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ComaveImportShopify] Rollback detected',
                [
                    'message' => $e->getMessage()
                ]
            );
            $connection->rollBack();
        } finally {
            $this->mapping = [];
        }
    }
}
