<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\ShopifyWebhookInstaller\Model\ConfigProvider;
use Magento\Framework\UrlInterface;

class GetVariantMediaGraphqlString
{
    /**
     * @param string|null $after
     * @return string
     */
    public function get(string $shopifyProductId): string {
        return sprintf(
            'query {
  product(id: "%s") {
    title
    variants(first: 100) {
      edges {
        node {
          id
          selectedOptions {
            name
            value
          }
          media(first: 100) {
            edges {
              node {
                alt
                mediaContentType
                status
                __typename
                ... on MediaImage {
                  id
                  preview {
                    image {
                      url
                    }
                  }
                  __typename
                }
              }
            }
          }
        }
      }
    }
  }
}',
            $shopifyProductId
        );
    }
}
