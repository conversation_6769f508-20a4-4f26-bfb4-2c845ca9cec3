<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\ShopifyWebhookInstaller\Model\ConfigProvider;
use Magento\Framework\UrlInterface;

class GetProductsGraphqlString
{
    /**
     * @param string|null $after
     * @return string
     */
    public function get(?string $after = null): string {
        return sprintf(
            'query {
    products(first: 100, query: "%s"%s) {
    pageInfo {
        hasNextPage
        endCursor
    }
    edges {
      node {
        id
        title
        status
        handle
        category {
            id
            name
        }
        priceRangeV2 {
            minVariantPrice {
                amount
                currencyCode
            }
            maxVariantPrice {
                amount
                currencyCode
            }
        }
        description
        descriptionHtml
        productType
        tags
        handle
        images(first: 5) {
          edges {
            node {
              src
              altText
            }
          }
        }
        variants(first: 10) {
          edges {
            node {
              id
              title
              sku
              price
              inventoryQuantity
              inventoryItem {
                id
                sku
                requiresShipping
                tracked
                inventoryLevels(first: 10) {
                  edges {
                    node {
                      id
                      quantities(names:["available"]) {
                        name
                        quantity
                      }
                    }
                  }
                }
              }
              selectedOptions {
                name
                value
              }
            }
          }
        }
        totalInventory
      }
    }
  }
}',
            "(product_publication_status:published) AND (status:ACTIVE)",
            $after !== null ? sprintf(', after:"%s"', $after) : '',
        );
    }
}
