<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Magento\Framework\Stdlib\ArrayManager;

class FieldGetter
{
    /**
     * @param ArrayManager $arrayManager
     */
    public function __construct(
        private readonly ArrayManager $arrayManager
    ) {
    }

    /**
     * @param array $unserialisedData
     * @param string $arrayPath
     * @param bool $multiple
     * @return mixed
     */
    public function get(array $unserialisedData, string $arrayPath, bool $multiple = false): mixed
    {
        $method = $multiple ? 'findPaths' : 'findPath';
        $foundPath = $this->arrayManager->{$method}($arrayPath, $unserialisedData);

        if (!$foundPath) {
            return null;
        }

        return $this->arrayManager->get($foundPath, $unserialisedData);
    }
}
