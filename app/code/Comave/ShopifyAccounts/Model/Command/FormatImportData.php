<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Model\Importedtmpproduct;
use Magento\Catalog\Api\Data\ProductInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ImportedtmpproductRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data;

class FormatImportData
{
    /**
     * @var array
     */
    private array $loadedAccountConfiguration = [];

    /**
     * @param LoggerInterface $logger
     * @param ImportedtmpproductRepositoryInterface $repository
     * @param Data $configurations
     * @param TmpDeleteItems $tmpDeleteItems
     * @param TempItemToMagentoProduct $tmpToProduct
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ImportedtmpproductRepositoryInterface $repository,
        private readonly Data $configurations,
        private readonly TmpDeleteItems $tmpDeleteItems,
        private readonly TempItemToMagentoProduct $tmpToProduct
    ) {
    }

    /**
     * @param string $shopifyAccountId
     * @return ProductInterface[]
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(string $shopifyAccountId): array
    {
        if (!isset($this->loadedAccountConfiguration[$shopifyAccountId])) {
            $this->loadedAccountConfiguration[$shopifyAccountId] = $this->configurations->getShopifyConfiguration(
                $shopifyAccountId,
            );
        }

        $loadedProductArr = $this->repository->getCollectionByProductTypeAndRuleId(
            'product',
            $shopifyAccountId
        )->setPageSize(5)
        ->setCurPage(1);

        if (!$loadedProductArr->getSize()) {
            return ['__n'];
        }

        $products = [];

        /** @var Importedtmpproduct $tmpItem */
        foreach ($loadedProductArr->getItems() as $tmpItem) {
            try {
                $this->tmpDeleteItems->addTmpItem($tmpItem->getEntityId());
                /** @var Product $product */
                $product = $this->tmpToProduct->execute($tmpItem, $shopifyAccountId);
                $products[$tmpItem->getEntityId()] = $product;
            } catch (\Throwable $e) {
                $this->logger->warning(
                    '[ComaveImportProcess] Error encountered',
                    [
                        'message' => $e->getMessage()
                    ]
                );
                $this->tmpDeleteItems->addErrorContext(
                    (string) $tmpItem->getItemId(),
                    (string) $tmpItem->getId(),
                    (string) $e->getMessage(),
                );
            }
        }

        return $products;
    }
}
