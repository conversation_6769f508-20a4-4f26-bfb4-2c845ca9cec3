<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Magento\Framework\Exception\NoSuchEntityException;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterfaceFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifyaccounts;

class GetSellerByDomain
{
    private array $loadedAccount = [];

    /**
     * @param Shopifyaccounts $resourceModel
     * @param ShopifyaccountsInterfaceFactory $accountFactory
     */
    public function __construct(
        private readonly Shopifyaccounts $resourceModel,
        private readonly ShopifyaccountsInterfaceFactory $accountFactory
    )
    {
    }

    /**
     * @param string $domainName
     * @return array
     * @throws NoSuchEntityException
     */
    public function get(string $domainName): array
    {
        if (isset($this->loadedAccount[$domainName])) {
            return $this->loadedAccount[$domainName];
        }

        /** @var ShopifyaccountsInterface $shopifyAccount */
        $shopifyAccount = $this->accountFactory->create();
        $this->resourceModel->load(
            $shopifyAccount,
            $domainName,
            'shopify_domain_name'
        );

        if (!$shopifyAccount->getId()) {
            throw new NoSuchEntityException(
                __('No shopify account with such domain')
            );
        }

        $this->loadedAccount[$domainName] = $shopifyAccount->getData();
        return $this->loadedAccount[$domainName];
    }
}
