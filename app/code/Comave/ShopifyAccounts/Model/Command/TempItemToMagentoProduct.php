<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\ShopifyAccounts\Model\Command\GraphQl\GetVariantMedia;
use Comave\ShopifyAccounts\Model\Handlers\VariationProduct;
use Magento\Catalog\Api\AttributeSetRepositoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Eav\Api\Data\AttributeSetInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Magento\Tax\Helper\Data;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data as ShopifyHelper;
use Webkul\MpMultiShopifyStoreMageConnect\Model\Importedtmpproduct;

class TempItemToMagentoProduct
{
    /**
     * @var array
     */
    private array $loadedAccountConfiguration = [];

    /**
     * @param FieldGetter $fieldGetter
     * @param InventoryRegistry $inventoryRegistry
     * @param CategoryMapper $categoryMapper
     * @param ShopifyHelper $shopifyHelper
     * @param MediaGalleryFormatter $mediaGalleryFormatter,
     * @param SerializerInterface $serializer
     * @param ProductInterfaceFactory $productFactory
     * @param Data $taxHelper
     * @param ProductRepositoryInterface $productRepository
     * @param WebsiteRepositoryInterface $websiteRepository
     * @param AttributeSetRepositoryInterface $attributeSetRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param VariationProduct $variationProduct
     * @param ShopifyToMagentoMapper $shopifyToMagentoMapper
     * @param GetVariantMedia $getVariantMedia
     */
    public function __construct(
        private readonly FieldGetter $fieldGetter,
        private readonly InventoryRegistry $inventoryRegistry,
        private readonly CategoryMapper $categoryMapper,
        private readonly ShopifyHelper $shopifyHelper,
        private readonly MediaGalleryFormatter $mediaGalleryFormatter,
        private readonly SerializerInterface $serializer,
        private readonly ProductInterfaceFactory $productFactory,
        private readonly Data $taxHelper,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly WebsiteRepositoryInterface $websiteRepository,
        private readonly AttributeSetRepositoryInterface $attributeSetRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly VariationProduct $variationProduct,
        private readonly ShopifyToMagentoMapper $shopifyToMagentoMapper,
        private readonly GetVariantMedia $getVariantMedia,
    ) {
    }

    /**
     * @param Importedtmpproduct $tmpProduct
     * @param string $shopifyAccountId
     * @return ProductInterface|null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Importedtmpproduct $tmpProduct, string $shopifyAccountId): ?ProductInterface
    {
        $unserialisedData = $this->serializer->unserialize($tmpProduct->getProductData() ?: '{}');
        if (empty($unserialisedData)) {
            return null;
        }

        if (!isset($this->loadedAccountConfiguration[$shopifyAccountId])) {
            $this->loadedAccountConfiguration[$shopifyAccountId] = $this->shopifyHelper->getShopifyConfiguration($shopifyAccountId);
        }

        $productId = (string) preg_replace("/[^0-9]/", '', (string) $unserialisedData['id']);
        $productSku = sprintf('%s-%s', $productId, $shopifyAccountId);

        $accountAttributeSetId = $this->shopifyHelper->getAttributeSetId(
            $shopifyAccountId
        );
        $website = $this->shopifyHelper->getDefaultWebsiteCode($shopifyAccountId) ?? 'base';
        $productType = $this->getProductType($unserialisedData);
        $productVisibility = empty($unserialisedData['variants']) ||
            (count($unserialisedData['variants']) <= 1 || count($unserialisedData['variants']) > 1) ?
                Product\Visibility::VISIBILITY_BOTH :
                Product\Visibility::VISIBILITY_NOT_VISIBLE;

        $maxPrice = $this->fieldGetter->get($unserialisedData, 'minVariantPrice')['amount'];
        $minPrice = $this->fieldGetter->get($unserialisedData, 'maxVariantPrice')['amount'];

        /** @var Product $product */
        try {
            $product = $this->productRepository->get($productSku, true, null, true);
        } catch (\Exception) {
            $product = $this->productFactory->create();
            $product->setUrlKey($productSku);
            $assignedWebsite = $this->websiteRepository->get($website);
            if (empty($accountAttributeSetId)) {
                $attributeSetList = $this->attributeSetRepository->getList(
                    $this->searchCriteriaBuilder->addFilter(
                        'attribute_set_name',
                        'Default'
                    )->create()
                );
                /** @var AttributeSetInterface $attributeSet */
                $attributeSet = current($attributeSetList->getItems());
                $accountAttributeSetId = $attributeSet->getAttributeSetId();
            }

            $product->setTypeId($productType)
                ->setVisibility($productVisibility)
                ->setTaxClassId(
                    $this->taxHelper->getDefaultProductTaxClass()
                )->setWebsiteIds([$assignedWebsite->getId()])
                ->setWeight(0.1)
                ->setSku($productSku)
                ->setStatus(
                    Product\Attribute\Source\Status::STATUS_DISABLED
                )->setAttributeSetId($accountAttributeSetId);
        }

        if ($productType === 'simple') {
            $inventory = $this->fieldGetter->get($unserialisedData, 'inventoryItem');

            if ($inventory) {
                $this->inventoryRegistry->set(
                    $productSku,
                    (string) preg_replace("/[^0-9]/", '', (string) $inventory['id'])
                );
            }

            if (isset($unserialisedData['totalInventory'])) {
                $product->setData(
                    'quantity_and_stock_status',
                    [
                        'is_in_stock' => $unserialisedData['totalInventory'] > 0 ? 1 : 0,
                        'qty' => $unserialisedData['totalInventory']
                    ]
                );
            }

            $product->setPrice(
                (float) (max(0, $maxPrice, $minPrice))
            );
            $specialPrice = $minPrice !== $maxPrice ? $minPrice : 0;

            if ($specialPrice > 0) {
                $product->setCustomAttribute(
                    'special_price',
                    (float) $specialPrice
                );
            }
        }

        $product->setName($unserialisedData['title'])
            ->setCustomAttribute(
                'description',
                strip_tags($unserialisedData['descriptionHtml'] ?? '')
            )->setCustomAttribute(
                'short_description',
                $unserialisedData['description'] ?? ''
        );

        if ($this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'] > 0) {
            $product->setData(
                'assign_seller',
                [
                    'seller_id' => $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
                ]
            );
        }

        if ($product->getTypeId() !== $productType) {
            $product->setTypeId($productType);
            $product->setVisibility($productVisibility);
        }

        $categoryIds = $this->categoryMapper->getByShopifyName(
            $productSku,
            $unserialisedData['category']['name'] ?? $unserialisedData['productType'],
            $shopifyAccountId,
            $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
            $unserialisedData,
        );

        if (!empty($categoryIds)) {
            $product->setCategoryIds($categoryIds);
        }

        if (!empty($unserialisedData['variants']) && $product->getTypeId() !== 'simple') {
            $product = $this->variationProduct->handle(
                $shopifyAccountId,
                $this->loadedAccountConfiguration[$shopifyAccountId]['seller_id'],
                $product,
                $unserialisedData['variants']['edges']
            );
        }

        $this->shopifyToMagentoMapper->addMapping(
            $shopifyAccountId,
            $product,
            $tmpProduct
        );

        if (empty($unserialisedData['images']['edges'])) {
            return $product;
        }

        $mediaData = [
            'images' => []
        ];

        foreach ($unserialisedData['images']['edges'] as $imageNode) {
            $mediaData['images'][] = [
                'src' => $imageNode['node']['src']
            ];
        }

        if (!empty($unserialisedData['variants']) && $product->getTypeId() !== 'simple') {
            $variantMedia = $this->getVariantMedia->get(
                $shopifyAccountId,
                $unserialisedData['id']
            );

            if (!empty($variantMedia)) {
                $mediaData = array_merge_recursive(
                    $mediaData,
                    $variantMedia
                );
            }
        }

        $this->mediaGalleryFormatter->execute(
            $product,
            $mediaData,
            $shopifyAccountId
        );

        return $product;
    }

    /**
     * @param array $unserialisedData
     * @return string
     */
    private function getProductType(array $unserialisedData): string
    {
        if (empty($unserialisedData['variants'])) {
            return 'simple';
        }

        return count($unserialisedData['variants']['edges']) > 1 ? 'configurable' : 'simple';
    }
}
