<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Command;

use Comave\SellerApi\Model\MediaGalleryRegistry;
use Magento\Catalog\Api\Data\ProductInterface;

class MediaGalleryFormatter
{
    /**
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     */
    public function __construct(private readonly MediaGalleryRegistry $mediaGalleryRegistry)
    {
    }

    /**
     * @param ProductInterface $product
     * @param array $unserialisedData
     * @param string $shopifyAccountId
     * @return void
     */
    public function execute(
        ProductInterface $product,
        array $unserialisedData,
        string $shopifyAccountId
    ): void {
        $mediaGallery = [];

        foreach ($unserialisedData['images'] as $imageData) {
            if (!empty($imageData['variant_ids'])) {
                foreach ($imageData['variant_ids'] as $variantId) {
                    $variantId = (string) preg_replace("/[^0-9]/", '', (string) $variantId);
                    $variantSku = sprintf('%s-%s', $variantId, $shopifyAccountId);
                    $this->checkImage($mediaGallery, $imageData, $variantSku);
                }

                continue;
            }

            $this->checkImage($mediaGallery, $imageData, $product->getSku());
        }

        if (!empty($mediaGallery)) {
            foreach ($mediaGallery as $productSku => $mediaGalleryEntries) {
                $this->mediaGalleryRegistry->add([
                    'sku' => $productSku,
                    'media_gallery_entries' => $mediaGalleryEntries
                ]);
            }
        }
    }

    /**
     * @param array $mediaGallery
     * @param array $imageData
     * @param string $productSku
     * @return void
     */
    private function checkImage(
        array &$mediaGallery,
        array $imageData,
        string $productSku
    ): void {
        if (!is_string($imageData['src'] ?? false)) {
            return;
        }

        try {
            $imageFileContents = file_get_contents($imageData['src']);

            if ($imageFileContents !== false && !in_array($imageData['src'], $mediaGallery)) {
                if (!isset($mediaGallery[$productSku])) {
                    $mediaGallery[$productSku] = [];
                }

                $mediaGallery[$productSku][] = $imageData['src'];
            }
        } catch (\Exception) {}
    }
}
