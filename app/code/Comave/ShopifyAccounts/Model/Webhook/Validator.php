<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Webhook;

use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Comave\ShopifyAccounts\Model\Command\GetSellerByDomain;
use Comave\ShopifyOrderSync\Helper\Shopify as ShopifyHelper;
use Magento\Framework\App\HttpRequestInterface;
use Psr\Log\LoggerInterface;

class Validator implements WebhookValidatorInterface
{
    /**
     * @param ShopifyHelper $shopifyHelper
     * @param LoggerInterface $logger
     * @param GetSellerByDomain $getSellerByDomain
     */
    public function __construct(
        private readonly ShopifyHelper $shopifyHelper,
        private readonly LoggerInterface $logger,
        private readonly GetSellerByDomain $getSellerByDomain
    ) {
    }

    /**
     * @param HttpRequestInterface $httpRequest
     * @return bool
     * @throws InvalidWebhookRequestException
     */
    public function validate(HttpRequestInterface $httpRequest): bool
    {
        /** @var string $requestHmac */
        $requestHmac = $httpRequest->getHeader(self::HASHMAC_HEADER, '');
        $jsonParam = file_get_contents("php://input");

        $topicName = $httpRequest->getHeader(
            WebhookValidatorInterface::EVENT_TOPIC_HEADER
        );

        $this->logger->info(
            '[WebhookValidator] Beginning validation process for topic',
            [
                'topic' => $topicName,
            ]
        );

        $enabledWebhook = $this->shopifyHelper->enabledWebhook();

        if (!$enabledWebhook) {
            $this->logger->info(
                '[WebhookValidator] Webhooks not enabled from system configuration',
                [
                    'path' => 'mpmultishopifystoremageconnect/general_settings/enable_webhook'
                ]
            );

            throw new InvalidWebhookRequestException(
                __('Invalid request')
            );
        }

        if (empty($jsonParam)) {
            $this->logger->warning(
                '[WebhookValidator] Invalid request, no body found',
            );

            throw new InvalidWebhookRequestException(
                __('Invalid request')
            );
        }

        $accData = $this->getSellerByDomain->get(
            $httpRequest->getHeader(self::SELLER_DOMAIN_HEADER)
        );

        if (empty($accData) || !isset($accData['webhook_verify_key'])) {
            $this->logger->warning(
                '[WebhookValidator] Unable to determine shopify account',
                [
                    'store' => $httpRequest->getHeader(self::SELLER_DOMAIN_HEADER)
                ]
            );

            throw new InvalidWebhookRequestException(
                __('Invalid request')
            );
        }

//      @todo - not really sure how to do this since magento seems to alter the raw request no matter what we do
//
//        if (!$this->verifyWebhookRequest($requestHmac, $jsonParam, $accData['webhook_verify_key'])) {
//            $this->logger->warning(
//                '[WebhookValidator] Invalid signature detected',
//                [
//                    'account' => $accData['store_name']
//                ]
//            );
//
//            throw new InvalidWebhookRequestException(
//                __('Invalid request')
//            );
//        }

        return true;
    }

    /**
     * @param string $requestHmac
     * @param string $data
     * @param string $webhookVerifyKey
     * @return bool
     */
    private function verifyWebhookRequest(string $requestHmac, string $data, string $webhookVerifyKey): bool
    {
        $calculatedHmac = base64_encode(
            hash_hmac(
                'sha256',
                $data,
                $webhookVerifyKey,
                true
            )
        );

        return hash_equals($requestHmac, $calculatedHmac);
    }
}
