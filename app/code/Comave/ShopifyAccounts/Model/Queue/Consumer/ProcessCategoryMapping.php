<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Queue\Consumer;

use Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterfaceFactory;
use Comave\SellerOnboarding\Model\Category\DataSourceUiManager;
use Comave\ShopifyAccounts\Model\Seller\CategoryProcessor;
use Magento\Catalog\Api\CategoryLinkManagementInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\State;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Locale\Resolver;
use Magento\Framework\Model\AbstractModel;
use Magento\Store\Model\App\Emulation;
use Psr\Log\LoggerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifyaccountsRepositoryInterface;

class ProcessCategoryMapping
{
    public const string TOPIC_NAME = 'shopify.category.matcher';

    /**
     * @param MappingInterfaceFactory $mappingFactory
     * @param ManagerInterface $eventManager
     * @param State $state
     * @param CategoryProcessor $categoryProcessor
     * @param Resolver $localeResolver
     * @param Emulation $emulation
     * @param ShopifyaccountsRepositoryInterface $shopifyaccountsRepository
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param CategoryMatcherServiceInterface $categoryMatcherService
     * @param CategoryLinkManagementInterface $categoryLinkManagement
     */
    public function __construct(
        private readonly DataSourceUiManager $dataSourceUiManager,
        private readonly MappingInterfaceFactory $mappingFactory,
        private readonly ManagerInterface $eventManager,
        private readonly State $state,
        private readonly CategoryProcessor $categoryProcessor,
        private readonly Resolver $localeResolver,
        private readonly Emulation $emulation,
        private readonly ShopifyaccountsRepositoryInterface $shopifyaccountsRepository,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly CategoryMatcherServiceInterface $categoryMatcherService,
        private readonly CategoryLinkManagementInterface $categoryLinkManagement,
    ) {
    }

    /**
     * @param string $accountId
     * @return int
     * @throws \Exception
     */
    public function execute(string $accountId): int
    {
        /** @var ShopifyaccountsInterface|AbstractModel $shopifyAccount */
        $shopifyAccount = $this->shopifyaccountsRepository->getConfigurationById(
            (int) $accountId
        );
        $allRows = $this->getCategoriesToMatch($shopifyAccount);

        if (empty($allRows)) {
            $this->logger->info(
                '[ShopifyCategoryMatcher] No categories to map for account',
                [
                    'accountId' => $accountId,
                ]
            );

            return 0;
        }

        $lang = 'en';

        try {
            $this->emulation->startEnvironmentEmulation(
                (int) $shopifyAccount->getDefaultStoreView(),
                Area::AREA_FRONTEND,
                true
            );
        } catch (\Exception) {
        }

        try {
            $this->localeResolver->_resetState();
            $locale = $this->localeResolver->emulate(
                (int) $shopifyAccount->getDefaultStoreView()
            );
            $this->localeResolver->_resetState();
            $lang = strtolower(current(explode('_', $locale)));
            $this->emulation->stopEnvironmentEmulation();
        } catch (\Exception) {
        }

        $matchResult = $this->categoryMatcherService->matchCategories($allRows, $lang);
        $mappingList = [];
        $errors = [];
        $skus = [];

        foreach ($matchResult as $sku => $categoryData) {
            $this->processCategoryAccuracy(
                $sku,
                $mappingList,
                $categoryData,
                $allRows,
                $shopifyAccount
            );
            $assignedCategory = end($categoryData['categoryIds']);

            try {
                $this->categoryLinkManagement->assignProductToCategories(
                    $sku,
                    [$assignedCategory]
                );
                $skus[] = $sku;
                $this->logger->info(
                    '[ShopifyCategoryMatching] Assigned product to category',
                    [
                        'sku' => $sku,
                        'magentoCategory' => $assignedCategory
                    ]
                );
            } catch (\Exception $e) {
                $errors[$sku] = sprintf('Error assigning product to category: %s - SKU: %s', $e->getMessage(), $sku);
                $this->logger->error(
                    '[ShopifyCategoryMatching] Error assigning product to category',
                    [
                        'exception' => $e->getMessage(),
                        'sku' => $sku
                    ]
                );
            }
        }

        if (!empty($mappingList)) {
            $this->logger->error(
                '[ShopifyCategoryMatching] Adding mapping accuracy',
            );

            $this->eventManager->dispatch(
                'category_matcher_accuracy',
                [
                    'mapping_list' => $mappingList
                ]
            );
        }

        $connection = $this->resourceConnection->getConnection();
        $connection->update(
            $connection->getTableName('comave_shopify_category_matcher'),
            [
                'processed' => 1,
            ],
            [
                'account_id = ?' => $accountId,
                'sku IN (?)' => $skus
            ]
        );

        if ($this->state->getAreaCode() === Area::AREA_ADMINHTML && !empty($errors)) {
            throw new \Exception(implode(PHP_EOL, $errors));
        }

        return count($allRows);
    }

    /**
     * @param string $productSku
     * @param array $mappingList
     * @param array $categoryData
     * @param array $categorySources
     * @return void
     */
    private function processCategoryAccuracy(
        string $productSku,
        array &$mappingList,
        array $categoryData,
        array $categorySources,
        ShopifyaccountsInterface $shopifyAccount
    ): void {
        $source = array_search(
            $productSku,
            array_column($categorySources, 'sku')
        );

        if ($source === false) {
            return;
        }

        $sourceId = $categorySources[$source]['source_id'];

        if (empty($mappingList[$sourceId] ?? '')) {
            $dataSource = $this->dataSourceUiManager->getBySellerId(
                md5($categorySources[$source]['word']),
                (int) $shopifyAccount->getSellerId()
            );
            $dataSource->setSourceCategoryId(
                md5($categorySources[$source]['word'])
            )->setSellerId(
                (int) $shopifyAccount->getSellerId()
            )->setSourceCategoryName(
                $categorySources[$source]['word']
            )->setSourceMetadata(
                json_encode($categorySources[$source])
            );
            $this->dataSourceUiManager->save($dataSource);
            $sourceId = $dataSource->getId();
        }

        $accuracy = $categoryData['accuracy'];
        $categoryId = end($categoryData['categoryIds']);

        /** @var MappingInterface $mapping */
        $mapping = $this->mappingFactory->create();
        $mapping->setMappingCategoryId(
            (int) $categoryId
        )->setMappingType(
            MappingInterface::MAPPING_TYPE_AUTO
        )->setMappingAccuracy(
            $accuracy * 100
        )->setMappingSourceId(
            (int) $sourceId
        );
        $mappingList[$sourceId] = $mapping;
    }

    /**
     * @param ShopifyaccountsInterface $shopifyAccount
     * @return array
     */
    private function getCategoriesToMatch(ShopifyaccountsInterface $shopifyAccount): array
    {
        $this->categoryProcessor->process([
            'seller_id' => $shopifyAccount->getData('seller_id'),
            'account_id' => $shopifyAccount->getId()
        ]);

        $connection = $this->resourceConnection->getConnection();
        $getAllCategories = $connection->select()
            ->from(
                ['main' => $connection->getTableName('comave_shopify_category_matcher')],
                [
                    'sku',
                    'word'
                ]
            )->where(
                'account_id = ?',
                $shopifyAccount->getId()
            )->where(
                'processed = ?',
                0
            );

        if ($connection->isTableExists('comave_seller_onboarding_category_data_source')) {
            $getAllCategories->joinLeft(
                ['cds' => $connection->getTableName('comave_seller_onboarding_category_data_source')],
                'cds.source_category_name = main.word AND cds.seller_id = ' . $shopifyAccount->getData('seller_id'),
                [
                    'source_id'
                ]
            );
        }

        return $connection->fetchAll($getAllCategories) ?: [];
    }
}
