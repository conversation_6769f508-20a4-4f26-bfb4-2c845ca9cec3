<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Queue\Consumer;

use Comave\SellerApi\Service\RequestHandler;
use Comave\ShopifyAccounts\Model\Command\InventoryRegistry;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\FlagManager;
use Psr\Log\LoggerInterface;

class InventoryUpdate
{
    public const string TOPIC_NAME = 'shopify.inventory.update';
    private const string FLAG_INVENTORY_UPDATE = 'shopify_inventory_update';

    /**
     * @param FlagManager $flagManager
     * @param ProductRepositoryInterface $productRepository
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param InventoryRegistry $inventoryRegistry
     * @param RequestHandler $requestHandler
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly FlagManager $flagManager,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly InventoryRegistry $inventoryRegistry,
        private readonly RequestHandler $requestHandler,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param string $accountId
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function update(string $accountId): void
    {
        $this->logger->info(
            '[ShopifyInventoryCron] Starting inventory sync process',
            [
                'accountId' => $accountId,
            ]
        );
        $itemIds = $this->inventoryRegistry->getInventoryItemIds($accountId);

        if (empty($itemIds)) {
            $this->logger->notice(
                '[ShopifyInventoryCron] Finished inventory sync process, no items found',
                [
                    'accountId' => $accountId,
                ]
            );

            return;
        }

        $inventoryItems = array_keys($itemIds);
        $inventorySkus = [];

        try {
            $params = [
                'inventory_item_ids' => implode(
                    ',',
                    $inventoryItems
                )
            ];
            $lastUpdate = $this->getLastUpdate($accountId);

            if (!empty($lastUpdate)) {
                $params['updated_at_min'] = $lastUpdate;
            }

            $configurableApi = $this->configurableApiFactory->create([
                'shopifyAccountId' => $accountId
            ])->setEndpoint(
                ConfigurableApi::SHOPIFY_INVENTORY_LEVEL_ENDPOINT_TYPE
            )->setParams($params)
            ->setMethod('GET');
            $response = $this->requestHandler->handleRequest($configurableApi);
            $decodedResponse = json_decode(
                $response->getResult()->getBody()->getContents() ?: '{}',
                true
            );

            if ($response->hasError()) {
                $this->logger->error(
                    '[ShopifyInventoryCron] Sync API return with error',
                    [
                        'accountId' => $accountId,
                        'errorCode' => $response->getResult()->getReasonPhrase(),
                        'httpCode' => $response->getResult()->getStatusCode(),
                        'response' => $decodedResponse
                    ]
                );

                return;
            }

            if (empty($decodedResponse['inventory_levels'])) {
                $this->logger->info(
                    '[ShopifyInventoryCron] No items to update',
                    [
                        'last_update' => $lastUpdate ?: 'N/A'
                    ]
                );

                return;
            }

            foreach ($decodedResponse['inventory_levels'] as $inventoryLevel) {
                if (!isset($itemIds[$inventoryLevel['inventory_item_id']])) {
                    continue;
                }

                $productSku = (string) $itemIds[$inventoryLevel['inventory_item_id']]['product_sku'];

                if (!isset($inventorySkus[$productSku])) {
                    $inventorySkus[$productSku] = $inventoryLevel['available'];
                } else {
                    $inventorySkus[$productSku] += $inventoryLevel['available'];
                }
            }
        } catch (\Exception $e) {
            $this->logger->error(
                '[ShopifyInventoryCron] Application failure',
                [
                    'accountId' => $accountId,
                    'error' => $e->getMessage(),
                ]
            );

            return;
        }

        if (empty($inventorySkus)) {
            $this->logger->warning(
                '[ShopifyInventoryCron] Finished inventory sync process, no updates found',
                [
                    'accountId' => $accountId
                ]
            );

            return;
        }

        try {
            $this->processInventories($inventorySkus);
        } catch (\Exception $e) {
            $this->logger->error(
                '[ShopifyInventoryCron] Error processing magento stock',
                [
                    'accountId' => $accountId,
                    'error' => $e->getMessage(),
                ]
            );

            return;
        }

        $this->logger->info(
            '[ShopifyInventoryCron] Finished inventory sync process',
            [
                'accountId' => $accountId,
                'updateCount' => count($inventorySkus)
            ]
        );

        $this->updateLastUpdate($accountId);
    }

    /**
     * @param array $inventorySkus
     * @return void
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Validation\ValidationException
     */
    private function processInventories(array $inventorySkus): void
    {
        foreach ($inventorySkus as $inventorySku => $quantity) {
            try {
                $qty = (float) (
                    max($quantity, 0)
                );
                $isInStock = $quantity > 0;
                $product = $this->productRepository->get($inventorySku);
                $product->setData(
                    'quantity_and_stock_status',
                    [
                        'qty' => $qty,
                        'is_in_stock' => $isInStock,
                    ]
                );
                $product->save();
            } catch (\Exception $e) {
                $this->logger->error(
                    '[ShopifyInventoryCron] Update stock failed for product',
                    [
                        'inventorySku' => $inventorySku,
                        'message' => $e->getMessage(),
                    ]
                );
            }
        }
    }

    /**
     * @param string $accountId
     * @return string
     */
    private function getLastUpdate(string $accountId): string
    {
        $lastUpdate = $this->flagManager->getFlagData(self::FLAG_INVENTORY_UPDATE);

        if (empty($lastUpdate)) {
            return '';
        }

        try {
            $lastUpdateArr = json_decode(
                is_string($lastUpdate) ? $lastUpdate : '{}',
                true,
                512,
                JSON_THROW_ON_ERROR
            );

            return $lastUpdateArr[$accountId] ?? '';
        } catch (\Exception) {
            $this->flagManager->deleteFlag(self::FLAG_INVENTORY_UPDATE);
        }

        return '';
    }

    /**
     * @param string $accountId
     * @return void
     */
    private function updateLastUpdate(string $accountId): void
    {
        $lastUpdate = $this->flagManager->getFlagData(self::FLAG_INVENTORY_UPDATE);
        $lastDate = (new \DateTime())->format('Y-m-d\TH:i:sP');

        if (empty($lastUpdate)) {
            $lastUpdate = [
                $accountId => $lastDate
            ];

            $this->flagManager->saveFlag(
                self::FLAG_INVENTORY_UPDATE,
                json_encode($lastUpdate)
            );

            return;
        }

        try {
            $lastUpdate = json_decode(
                $lastUpdate,
                true,
                512,
                JSON_THROW_ON_ERROR
            );

            $lastUpdate[$accountId] = $lastDate;
        } catch (\Exception) {
            $lastUpdate = [
                $accountId => (new \DateTime())->format('Y-m-d\TH:i:sP')
            ];
        }

        $this->flagManager->saveFlag(
            self::FLAG_INVENTORY_UPDATE,
            json_encode($lastUpdate)
        );
    }
}
