<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Handlers\Rest;

use Magento\Catalog\Api\Data\EavAttributeInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;

class DefaultConfigurableAttributes
{
    /**
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     */
    public function __construct(
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository
    ) {
    }

    /**
     * @param array $shopifyAttributes
     * @return array
     */
    public function get(array $shopifyAttributes): array
    {
        $attributeEntities = [];
        //@todo remove the manual mapping, only required now for testing purposes
        $mapping = [
            'Taille' => 'size',
            'Coloris' => 'color'
        ];

        foreach ($shopifyAttributes as $shopifyAttribute) {
            try {
                $attributeCode = $mapping[$shopifyAttribute['name']] ?? strtolower($shopifyAttribute['name']);
                $attributeEntities[$shopifyAttribute['position']] = $this->productAttributeRepository->get(
                    $attributeCode
                );
            } catch (\Exception) {
                continue;
            }
        }

        if (empty($attributeEntities)) {
            return [];
        }

        $res = [];
        /** @var EavAttributeInterface $eavAttribute */
        foreach ($attributeEntities as $position => $eavAttribute) {
            $res[$position] = [
                'attribute_id' => $eavAttribute->getAttributeId(),
                'label' => $eavAttribute->getDefaultFrontendLabel(),
                'values' => []
            ];
        }

        return $res;
    }
}
