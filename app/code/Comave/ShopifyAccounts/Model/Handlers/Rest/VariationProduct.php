<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Handlers\Rest;

use Comave\ShopifyAccounts\Model\Command\Rest\ShopifyToMagentoMapper;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\ConfigurableProduct\Helper\Product\Options\Factory;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class VariationProduct
{
    /**
     * @param DefaultConfigurableAttributes $defaultConfigurableAttributes
     * @param ShopifyToMagentoMapper $shopifyToMagentoMapper
     * @param ProductRepositoryInterface $productRepository
     * @param AttributeOptionManagementInterface $attributeOptionManagement
     * @param AttributeOptionInterfaceFactory $attributeOptionFactory
     * @param Factory $extensionOptionsFactory
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     */
    public function __construct(
        private readonly DefaultConfigurableAttributes $defaultConfigurableAttributes,
        private readonly ShopifyToMagentoMapper $shopifyToMagentoMapper,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly AttributeOptionManagementInterface $attributeOptionManagement,
        private readonly AttributeOptionInterfaceFactory $attributeOptionFactory,
        private readonly Factory $extensionOptionsFactory,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
    ) {
    }

    /**
     * @param string $shopifyAccountId
     * @param string $sellerId
     * @param ProductInterface $configurableProduct
     * @param array $variations
     * @param array $options
     * @return ProductInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function handle(
        string $shopifyAccountId,
        string $sellerId,
        ProductInterface $configurableProduct,
        array $variations,
        array $options
    ): ProductInterface {
        $variationMap = [];
        $typeInstance = $configurableProduct->getTypeInstance();
        $simpleProductIds = $typeInstance instanceof Configurable &&
        $configurableProduct->getId() ?
            current($typeInstance->getChildrenIds($configurableProduct->getId())) :
            [];

        $configurableAttributesData = $typeInstance instanceof Configurable &&
        $configurableProduct->getId() ?
            $typeInstance->getConfigurableAttributesAsArray($configurableProduct) :
            $this->defaultConfigurableAttributes->get($options);

        if (empty($configurableAttributesData)) {
            throw new LocalizedException(
                __('Unable to locate variation attributes, %1', json_encode($options))
            );
        }

        foreach ($variations as $variation) {
            $childName =  sprintf(
                '%s - %s',
                $configurableProduct->getName(),
                $variation['title']
            );
            $variationSku = $this->getVariationSku($shopifyAccountId, $variation, $childName);

            try {
                $simpleProduct = $this->productRepository->get(
                    $variationSku,
                    true,
                    null,
                    true
                );
            } catch (NoSuchEntityException) {
                $simpleProduct = clone $configurableProduct;
                $simpleProduct->setUrlKey('')
                    ->unsetData('entity_id')
                    ->unsetData('row_id')
                    ->setVisibility(Visibility::VISIBILITY_NOT_VISIBLE)
                    ->setWeight(0.1)
                    ->setSku($variationSku)
                    ->setStatus(Status::STATUS_DISABLED)
                    ->setTypeId('simple');
            }

            $this->manageAttributeValues(
                $simpleProduct,
                $shopifyAccountId,
                $configurableAttributesData,
                $variation
            );

            if ($sellerId > 0) {
                $simpleProduct->setData(
                    'assign_seller',
                    [
                        'seller_id' => $sellerId
                    ]
                );
            }

            $simpleProduct->setPrice((float) $variation['price'])
                ->setName($childName);

            if (isset($variation['inventory_quantity'])) {
                $simpleProduct->setData(
                    'quantity_and_stock_status',
                    [
                        'is_in_stock' => $variation['inventory_quantity'] > 0 ? 1 : 0,
                        'qty' => $variation['inventory_quantity']
                    ]
                );
            }

            $simpleProduct->save();
            // Seems that this is a magento core issue that wasn't treated
            // Repository save doesn't trigger vendor/magento/module-catalog-inventory/Observer/SaveInventoryDataObserver.php
            // Model save does ....
//            $simpleProduct = $this->productRepository->save($simpleProduct);
//            $this->manageStock($simpleProduct, $variation);

            if (!in_array($simpleProduct->getId(), $simpleProductIds)) {
                $simpleProductIds[] = $simpleProduct->getId();
            }

            $variationMap[$variationSku] = $variation['id'];
            $this->shopifyToMagentoMapper->addMapping(
                $shopifyAccountId,
                $simpleProduct,
                null,
                $variation
            );
        }

        $extensionAttributes = $configurableProduct->getExtensionAttributes();
        $configurableOptions = $this->extensionOptionsFactory->create($configurableAttributesData);
        $extensionAttributes->setConfigurableProductLinks($simpleProductIds);
        $extensionAttributes->setConfigurableProductOptions($configurableOptions);
        $configurableProduct->setCanSaveConfigurableAttributes(true);
        $configurableProduct->setConfigurableAttributesData($configurableAttributesData);
        $configurableProduct->setExtensionAttributes($extensionAttributes);
        $configurableProduct->setData('_shopify_variants', $variationMap);

        return $configurableProduct;
    }

    /**
     * @param string $attributeCode
     * @param string|int $attributeValue
     * @return string
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function createOption(
        string $attributeCode,
        string|int $attributeValue
    ): string {
        $option = $this->attributeOptionFactory->create();
        $option->setSortOrder(0);
        $option->setIsDefault(false);
        $option->setLabel($attributeValue);

        // Add the option to the attribute
        return $this->attributeOptionManagement->add(
            \Magento\Catalog\Model\Product::ENTITY,
            $attributeCode,
            $option
        );
    }

    /**
     * @param string $shopifyAccountId
     * @param array $variation
     * @param string $productName
     * @return string
     */
    private function getVariationSku(
        string $shopifyAccountId,
        array $variation,
        string $productName
    ): string {
        return sprintf('%s-%s', $variation['id'], $shopifyAccountId);
    }

    /**
     * @param ProductInterface $simpleProduct
     * @param string $shopifyAccountId
     * @param array $configurableAttributesData
     * @param array $variation
     * @return void
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function manageAttributeValues(
        ProductInterface $simpleProduct,
        string $shopifyAccountId,
        array &$configurableAttributesData,
        array $variation
    ): void {
        $optionPosition = 1;
        foreach ($configurableAttributesData as &$attributeData) {
            if (!isset($variation['option'. $optionPosition])) {
                throw new LocalizedException(__('Unable to detect %1 variant value', $attributeData['attribute_code']));
            }

            $attributeCode = strtolower($attributeData['label']);
            $magentoAttribute = $this->productAttributeRepository->get($attributeCode);
            $attributeValue = trim($variation['option'. $optionPosition]);

            if ($magentoAttribute->usesSource()) {
                $optionId = $magentoAttribute->getSource()->getOptionId($attributeValue);
                if (empty($optionId)) {
                    $optionId = $this->createOption(
                        $attributeCode,
                        $attributeValue
                    );
                }

                $attributeValue = $optionId;
            }

            $simpleProduct->setCustomAttribute(
                $attributeCode,
                $attributeValue
            );
            $valueIndex = $attributeValue . $simpleProduct->getId();

            if (empty($attributeData['values']) || !array_key_exists($valueIndex, $attributeData['values'])) {
                $attributeData['values'][$valueIndex] = [
                    'value_index' => $valueIndex
                ];
            }

            $optionPosition++;
        }
    }
}
