<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Comave\SellerOnboarding\Model\CategoryMappingProcessorPool">
        <arguments>
            <argument xsi:type="array" name="processors">
                <item xsi:type="object" name="shopify">Comave\ShopifyAccounts\Model\CategoryMappingProcessor\Proxy</item>
            </argument>
        </arguments>
    </type>

    <preference for="Comave\ShopifyAccounts\Api\WebhookValidatorInterface" type="Comave\ShopifyAccounts\Model\Webhook\Validator"/>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="shopify_error_list_data_source" xsi:type="string">ErrorGridCollection</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ErrorGridCollection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">wk_mpmultishopify_tempshopify</argument>
            <argument name="resourceModel" xsi:type="string">Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Importedtmpproduct\Collection</argument>
        </arguments>
    </virtualType>

    <type name="Comave\ShopifyAccounts\Controller\Events\Index">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Webhook\Validator">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Webhook\Processors\Order">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Webhook\Processors\InventoryUpdate">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Webhook\Processors\ProductUpdate">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Webhook\TopicProcessorPool">
        <arguments>
            <argument xsi:type="object" name="logger">WebhookLogger</argument>
            <argument xsi:type="array" name="topicProcessors">
                <item xsi:type="object" name="orders/update">Comave\ShopifyAccounts\Model\Webhook\Processors\Order\Proxy</item>
                <item xsi:type="object" name="draft_orders/update">Comave\ShopifyAccounts\Model\Webhook\Processors\Order\Proxy</item>
                <item xsi:type="object" name="inventory_levels/update">Comave\ShopifyAccounts\Model\Webhook\Processors\InventoryUpdate\Proxy</item>
                <item xsi:type="object" name="products/update">Comave\ShopifyAccounts\Model\Webhook\Processors\ProductUpdate\Proxy</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="WebhookLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ShopifyWebhookLogger</argument>
            <argument name="loggerPath" xsi:type="string">shopify_webhooks</argument>
        </arguments>
    </virtualType>

    <virtualType name="InventoryUpdateLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ShopifyInventoryUpdateLogger</argument>
            <argument name="loggerPath" xsi:type="string">shopify_inventory_updates</argument>
        </arguments>
    </virtualType>

    <virtualType name="ShopifyMarketplaceIntegration" type="Comave\SellerApi\Model\BaseIntegration" shared="false">
        <arguments>
            <argument xsi:type="string" name="integrationType">shopify</argument>
            <argument xsi:type="string" name="mainProductLinkTable">wk_mpmultishopifysynchronize_product</argument>
            <argument xsi:type="string" name="productColumnIdentifier">magento_pro_id</argument>
            <argument xsi:type="string" name="sellerColumnIdentifier">seller_id</argument>
            <!-- order configuration -->
            <argument xsi:type="string" name="orderTableLink">wk_mpmultishopifysynchronize_order</argument>
            <argument xsi:type="string" name="orderTableLinkId">mage_order_id</argument>
            <argument xsi:type="string" name="orderTableLinkField">shopify_order_id</argument>
            <argument xsi:type="string" name="externalLink">/admin/draft_orders/%s</argument>
            <!-- end -->
        </arguments>
    </virtualType>

    <type name="Comave\SellerApi\Api\IntegrationInterface">
        <plugin name="addDomainToExternalLink" type="Comave\ShopifyAccounts\Plugin\AddDomainToShopifySeller"/>
    </type>

    <type name="Comave\SellerApi\Model\IntegrationTypePool">
        <arguments>
            <argument xsi:type="array" name="integrationPool">
                <item xsi:type="object" name="shopify">ShopifyMarketplaceIntegration</item>
            </argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\OrderSynchronizer">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SellerApi\Model\Queue\MetaDataProcessorPool">
        <arguments>
            <argument xsi:type="array" name="metaDataProcessors">
                <item xsi:type="object" name="shopify">Comave\ShopifyAccounts\Model\Queue\MetaDataProcessor\Proxy</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ShopifySellersCollectCategoryListCommand" type="Comave\ShopifyAccounts\Console\Command\CollectCategoryList">
        <arguments>
            <argument name="name" xsi:type="string">comave:shopify-sellers:collect-category-list</argument>
        </arguments>
    </virtualType>

    <type name="Comave\ShopifyAccounts\Console\Command\CollectCategoryList">
        <arguments>
            <argument name="sellerCategoryProcessor" xsi:type="object">Comave\ShopifyAccounts\Model\Seller\CategoryProcessor</argument>
            <argument name="shopifyAccountsFactory" xsi:type="object">Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Cron\CategoryCollector">
        <arguments>
            <argument name="sellerCategoryProcessor" xsi:type="object">Comave\ShopifyAccounts\Model\Seller\CategoryProcessor</argument>
            <argument name="shopifyAccountsFactory" xsi:type="object">Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="reEncryptTokens" xsi:type="object">Comave\ShopifyAccounts\Console\Command\ReEncryptTokens</item>
                <item name="collectCategoryList" xsi:type="object">ShopifySellersCollectCategoryListCommand</item>
            </argument>
        </arguments>
    </type>


    <type name="Comave\ShopifyAccounts\Model\Queue\Consumer\ProcessCategoryMapping">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Command\FormatImportData">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

	<type name="Comave\ShopifyAccounts\Model\Command\ShopifyToMagentoMapper">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\CategoryMappingProcessor">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Model\AbstractModel">
        <plugin name="preventCacheDeleteOnImport" type="Comave\ShopifyAccounts\Plugin\PreventCacheClearing"/>
    </type>

    <type name="Comave\ShopifyAccounts\Plugin\PreventCacheClearing">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Model\Queue\Consumer\InventoryUpdate">
        <arguments>
            <argument xsi:type="object" name="logger">InventoryUpdateLogger</argument>
        </arguments>
    </type>

    <type name="Comave\ShopifyAccounts\Cron\InventoryUpdates">
        <arguments>
            <argument xsi:type="object" name="logger">InventoryUpdateLogger</argument>
        </arguments>
    </type>
</config>
