<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_shopify_category_matcher" resource="default" engine="innodb" comment="Main Link Table">
        <column xsi:type="varchar" name="sku" nullable="false" length="100" comment="Product SKU"/>
        <column xsi:type="varchar" name="word" nullable="false" length="100" comment="Shopify Category Name"/>
        <column xsi:type="int" name="account_id" nullable="false" comment="Shopify Account ID"/>
        <column xsi:type="boolean" name="processed" nullable="false" default="0" comment="Processed flag"/>
    </table>

    <table name="wk_mpmultishopify_seller_details">
        <column xsi:type="int" name="product_club" unsigned="true" nullable="true" padding="10" identity="false" comment="Sports Club Id"/>
        <column xsi:type="varchar" name="shopify_domain_name" nullable="true" length="255" comment="Shopify Domain"/>
        <constraint xsi:type="unique" referenceId="SELLER_DETAILS_DOMAIN">
            <column name="shopify_domain_name"/>
        </constraint>
    </table>

    <table name="comave_shopify_inventory_map" resource="default" engine="innodb" comment="Main Link Table">
        <column xsi:type="varchar" length="255" name="product_sku" comment="Inventory mapping for SKU"/>
        <column xsi:type="varchar" length="255" name="shopify_inventory_id" comment="Inventory mapping for SKU"/>
        <index referenceId="COMAVE_INVENTORY_MAP_INDEX" indexType="btree">
            <column name="product_sku"/>
            <column name="shopify_inventory_id"/>
        </index>

        <constraint xsi:type="unique" referenceId="COMAVE_INVENTORY_MAP_SKU_SHOPIFY_INVENTORY">
            <column name="product_sku"/>
        </constraint>
    </table>

    <table name="wk_mpmultishopify_tempshopify">
        <column xsi:type="varchar" length="255" nullable="false" name="item_id" comment="shopify Item Id"/>
        <column xsi:type="json" nullable="true" name="error_context" comment="Import Error Context"/>
        <column xsi:type="timestamp" nullable="true" name="error_timestamp" comment="Import Error Context Timestamp" on_update="false"/>
        <constraint xsi:type="unique" referenceId="IMPORT_TMP_PRODUCT_SHOPIFY_ID">
            <column name="item_id"/>
            <column name="rule_id"/>
        </constraint>
    </table>

    <table name="wk_mpmultishopifysynchronize_product">
        <column xsi:type="varchar" length="255" nullable="false" name="shopify_pro_id" comment="shopify Item Id"/>
        <column xsi:type="timestamp" nullable="true" name="last_imported" comment="Last Imported" on_update="true"/>
        <constraint xsi:type="unique" referenceId="SHOPIFY_ACCOUNT_PRODUCT_SHOPIFY_ID">
            <column name="shopify_pro_id"/>
            <column name="rule_id"/>
        </constraint>
    </table>
</schema>
