<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\ShopifyAccounts\Service\Seller;

use Comave\SellerApi\Service\RequestHandler;
use Comave\ShopifyAccounts\Model\Command\GetShopCategoriesGraphqlString;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Exception;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class CategoryFetcher
{
    /**
     * @param SerializerInterface $serializer
     * @param GetShopCategoriesGraphqlString $getShopCategoriesGraphqlString
     * @param RequestHandler $requestHandler
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly GetShopCategoriesGraphqlString $getShopCategoriesGraphqlString,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly LoggerInterface $logger,
    ) {
    }


    /**
     * @param $accountId
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCategories($accountId): array
    {
        if (empty($accountId)) {
            return [];
        }

        $categories = [];
        $queryJson = json_encode([
            'query' => trim($this->getShopCategoriesGraphqlString->get())
        ]);

        $configurableApi = $this->configurableApiFactory->create([
            'shopifyAccountId' => $accountId,
        ])->setEndpoint(ConfigurableApi::GRAPHQL)
        ->setParams($queryJson)
        ->setMethod(Request::METHOD_POST);

        try {
            $response = $this->requestHandler->handleRequest($configurableApi);
            if ($response->hasError()) {
                throw new Exception($response->getResult()->getReasonPhrase());
            }

            $decodedResponse = $this->serializer->unserialize(
                $response->getResult()->getBody()->getContents()
            );

            if (!isset($decodedResponse['data']['shop']['allProductCategoriesList'])) {
                return [];
            }

            $categoriesList = $decodedResponse['data']['shop']['allProductCategoriesList'];

            foreach ($categoriesList as $categoryItem) {
                $categories[] = [
                    'id' => md5($categoryItem['name']), //ignore this, not required but we need to match it somehow
                    'title' => $categoryItem['name'],
                    'metadata' => $categoryItem
                ];
            }
        } catch (Exception|ClientExceptionInterface $exception) {
            $this->logger->warning($exception->getMessage(), []);
        }

        return $categories;
    }
}
