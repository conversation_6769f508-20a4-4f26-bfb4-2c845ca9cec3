<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Plugin;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Data\Form;
use Magento\Framework\Registry;
use Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tab\GeneralConfiguration;

class AppendProductsClub
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param Registry $registry
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly Registry $registry,
    ) {
    }

    /**
     * @param GeneralConfiguration $generalConfiguration
     * @param Form $generalConfigForm
     * @return array|null
     */
    public function beforeSetForm(
        GeneralConfiguration $generalConfiguration,
        Form $generalConfigForm
    ): ?array {
        $generalConfigForm->getElement('generalConfiguration_fieldset')
            ?->addField(
                'product_club',
                'select',
                [
                    'label' => __('Add Products To Club'),
                    'title' => __('Add Products To Club'),
                    'required' => false,
                    'index' => 'product_club',
                    'name' => 'product_club',
                    'options' => $this->getProductClubOptions()
                ]
            );

        $shopifyAccount = $this->registry->registry('shopifyaccount_info');

        if ($shopifyAccount?->getId()) {
            $generalConfigForm->addValues([
                'product_club' => $shopifyAccount->getData('product_club')
            ]);
        }

        return [$generalConfigForm];
    }

    /**
     * @return array
     */
    private function getProductClubOptions(): array
    {
        $connection = $this->resourceConnection->getConnection();
        $clubSelect = $connection->select()
            ->from(
                $connection->getTableName('comave_club'),
                [
                    'club_id',
                    'name'
                ]
            );

        $allClubs = $connection->fetchAssoc($clubSelect) ?: [];
        if (empty($allClubs)) {
            return [];
        }

        $result = [
            '' => __('--- Select a club ---')
        ];
        foreach ($allClubs as $clubId => $clubArr) {
            $result[$clubId] = $clubArr['name'];
        }

        return $result;
    }
}
