<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Plugin;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Registry;
use Psr\Log\LoggerInterface;

class PreventCacheClearing
{
    /**
     * @param LoggerInterface $logger
     * @param Registry $registry
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly Registry $registry
    ) {
    }

    /**
     * @param AbstractModel $abstractModel
     * @param callable $proceed
     * @return ?AbstractModel
     */
    public function aroundCleanModelCache(
        AbstractModel $abstractModel,
        callable $proceed
    ): ?AbstractModel {
        if ($this->registry->registry('shopify_prevent_cached_tags')) {
            $this->logger->info(
                '[ComaveShopifyImport] Running import, bypassing cache clean on batches'
            );

            return $abstractModel;
        }

        return $proceed();
    }
}
