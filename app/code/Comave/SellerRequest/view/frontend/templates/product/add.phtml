<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

/** @var $block \Webkul\Marketplace\Block\Product\Create */
/** @var \Magento\Framework\Escaper $escaper */

$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$product_hint_status = $helper->getProductHintStatus();
$currency_code = $helper->getCurrentCurrencyCode();
$currency_symbol = $helper->getCurrencySymbol();
$set = $block->getRequest()->getParam('set');
$type = $block->getRequest()->getParam('type');
$skuType = $helper->getSkuType();
$skuPrefix = $helper->getSkuPrefix();
$data = $block->getPersistentData();
$weightUnit = $helper->getWeightUnit();
if (!empty($data['set'])) {
    $set = $data['set'];
}

$helperData = $this->helper('Comave\SellerRequest\Helper\Data');
$CurrentBaseUrl = $helperData->getCurrentBaseUrl();
$allowedSets = $helper->getAllowedSets();
$blockName = $block->getLayout()->createBlock('Comave\Club\Block\Banner');
$curWebsite = $blockName->getWebName();
?>

<form action="<?= $escaper->escapeUrl($block
->getUrl('marketplace/product/save', ['_secure' => $block->getRequest()->isSecure()])) ?>"
 enctype="multipart/form-data" method="post" id="edit-product" data-form="edit-product"
  data-mage-init='{"validation":{}}'>
    <div class="wk-mp-design" id="wk-bodymain">
        <fieldset class="fieldset info wk-mp-fieldset">
            <div data-mage-init='{"formButtonAction": {}}' class="wk-mp-page-title legend">
                <span><?= $escaper->escapeHtml(__('Add Product')) ?></span>
                <button class="button wk-mp-btn" title="<?= $escaper->escapeHtml(__('Save')) ?>"
                 type="submit" id="save-btn">
                    <span><span><?= $escaper->escapeHtml(__('Save')) ?></span></span>
                </button>
                <a href="#" class="button wk-mp-btn" title="<?= $escaper->escapeHtml(__('Preview')) ?>" id="preview-btn">
                    <span><span><?= $escaper->escapeHtml(__('Preview')) ?></span></span>
                </a>
                <button class="button wk-mp-btn"
                title="<?= $escaper->escapeHtml(__('Save & Duplicate')) ?>"
                type="button" id="wk-mp-save-duplicate-btn">
                    <span><span><?= $escaper->escapeHtml(__('Save & Duplicate')) ?></span></span>
                </button>
            </div>
            <?= $block->getBlockHtml('formkey')?>
            <?= $block->getBlockHtml('seller.formkey')?>
            <input id="product_type_id" name="type" type="hidden"
             value="<?= $escaper->escapeHtml($type)?>" value="<?= $escaper->escapeHtml($data['type'])?>">
            <?php if (count($helper->getAllowedSets()) > 1): ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('Attribute Set')) ?>:</label>
                    <span class="tooltip">ℹ️
                            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the attribute set for your product.')) ?></span>
                    </span>
                    <div class="control">
                        <select name="set" id="attribute-set-id" class="required-entry">
                        <?php foreach ($allowedSets as $setval): ?>
                            <option value="<?= $escaper->escapeHtml($setval['value']) ?>"
                             <?php if ($set==$setval['value']) { ?> selected="selected" <?php } ?>>
                                <?= $escaper->escapeHtml($setval['label'])?>
                            </option>
                        <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php else: ?>
                <?php $allowedSets = $helper->getAllowedSets(); ?>
                <input type="hidden" name="set" id="attribute-set-id"
                value="<?= $escaper->escapeHtml($allowedSets[0]['value']) ?>" />
            <?php endif; ?>
            <!-- to ask admin to add categories -->
            <div class="wk-mp-page-title page-title">
                <a href="#" class="button askque"  id="askque" title="<?= $escaper->escapeHtml(__('Add Categories')) ?>">
                    <span><?= $escaper->escapeHtml(__('Add Categories')) ?></span>
                </a>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Product Category')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the category for your product.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintCategory()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                     class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintCategory()) ?>"/>
                <?php endif; ?>
                <?php if ($helper->getIsAdminViewCategoryTree()) { ?>
                    <div data-bind="scope: 'sellerCategory'">
                        <!-- ko template: getTemplate() --><!-- /ko -->
                    </div>
                    <script type="text/x-magento-init">
                        {
                            "*": {
                                "Magento_Ui/js/core/app": {
                                    "components": {
                                        "sellerCategory": {
                                            "component": "Webkul_Marketplace/js/product/seller-category-tree",
                                            "template" : "Webkul_Marketplace/seller-category-tree",
                                            "filterOptions": true,
                                            "levelsVisibility": "1",
                                            "options": <?= /* @noEscape */ $block->getCategoriesTree()?>,
                                            "value": <?= /* @noEscape */ json_encode($data['product']['category_ids'])?>
                                        }
                                    }
                                }
                            }
                        }
                    </script>
                <?php } else { ?>
                    <div class="wk-field wk-category">
                        <div class="wk-for-validation">
                            <div id="wk-category-label"><?= $escaper->escapeHtml(__("CATEGORIES")); ?></div>
                            <?php
                            $categories = $data['product']['category_ids'];
                            foreach ($categories as $value) { ?>
                                <input type="hidden" name="product[category_ids][]"
                                value="<?= $escaper->escapeHtml($value); ?>"
                                 id="wk-cat-hide<?= $escaper->escapeHtml($value); ?>"/>
                            <?php } ?>
                            <?php
                            if ($helper->getAllowedCategoryIds()) {
                                $storeconfig_catids = explode(',', trim($helper->getAllowedCategoryIds()));
                                foreach ($storeconfig_catids as $storeconfig_catid) {
                                    $cat_model = $block->getCategory()->load($storeconfig_catid);
                                    if (isset($cat_model["entity_id"]) && $cat_model["entity_id"]) {
                                        ?>
                                        <div class="wk-cat-container">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($cat_model->getName()) ?>
                                            </span>
                                            <?php
                                            if (in_array($cat_model["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value=<?= $escaper->escapeHtml($cat_model['entity_id']) ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value='<?= $escaper->escapeHtml($cat_model['entity_id']) ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    }
                                }
                            } else {
                                $count = 0;
                                $category_helper = $viewModel->getCategoryHelper();
                                $category_model = $block->getCategory();
                                $_categories = $category_helper->getStoreCategories();
                                foreach ($_categories as $_category) {
                                    $count++;
                                    if (count($category_model->getAllChildren($category_model
                                    ->load($_category['entity_id'])))-1 > 0) { ?>
                                        <div class="wk-cat-container" style="margin-left:0px;">
                                            <span class="wk-plus">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($_category->getName()) ?>
                                            </span>
                                            <?php
                                            if (in_array($_category["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                 value=<?= $escaper->escapeHtml($_category['entity_id']) ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value='<?= $escaper->escapeHtml($_category['entity_id']) ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    } else { ?>
                                        <div class="wk-cat-container">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($_category->getName()) ?></span>
                                            <?php
                                            if (in_array($_category["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value=<?= $escaper->escapeHtml($_category['entity_id']) ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                 name="product[category_ids][]"
                                                 value='<?= $escaper->escapeHtml($_category['entity_id']) ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    }
                                }
                            } ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Product Name')) ?>:</label>
                <span class="tooltip">ℹ️
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the name of your product.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintName()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                        class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintName()) ?>"/>
                <?php } ?>
                <div class="control">
                    <input type="text" class="required-entry input-text" name="product[name]"
                        id="name" value="<?= $escaper->escapeHtml($data['product']['name'])?>"/>
                </div>
            </div>

            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Description')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please provide a brief description of the product.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintDesc()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?=  $escaper->escapeHtml($helper->getProductHintDesc()) ?>"/>
                    <?php
                } ?>
                <div class="control wk-border-box-sizing">
                    <textarea name="product[description]" class="required-entry input-text"
                    id="description" rows="5" cols="75" >
                    <?= /* @noEscape */  $data['product']['description']?></textarea>
                    <?php if ($helper->isWysiwygEnabled()): ?>
                        <script>
                            require([
                                "jquery",
                                "mage/translate",
                                "mage/adminhtml/events",
                                "mage/adminhtml/wysiwyg/tiny_mce/setup"
                            ], function(jQuery) {
                                wysiwygDescription = new wysiwygSetup("description", {
                                    "width" : "100%",
                                    "height" : "200px",
                                    "plugins" : [{"name":"image"}],
                                    "tinymce" : {
                                        "toolbar":"formatselect | bold italic underline | "+
                                        "alignleft aligncenter alignright |" +
                                        "bullist numlist |"+
                                        "link table charmap","plugins":"advlist "+
                                        "autolink lists link charmap media noneditable table "+
                                        "contextmenu paste code help table",
                                    },
                                    files_browser_window_url: "<?= /* @noEscape */$block->getWysiwygUrl();?>"
                                });
                                wysiwygDescription.setup("exact");
                            });
                        </script>
                    <?php endif; ?>
                </div>
            </div>

            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Short Description')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please provide a short description of the product.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintShortDesc()) { ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                     class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintShortDesc()) ?>"/>
                <?php } ?>
                <div class="control wk-border-box-sizing">
                    <textarea name="product[short_description]" class="input-text"
                     id="short_description" rows="5" cols="75" >
                     <?= /* @noEscape */ $data['product']['short_description']?></textarea>
                    <?php if ($helper->isWysiwygEnabled()): ?>
                        <script>
                            require([
                                "jquery",
                                "mage/translate",
                                "mage/adminhtml/events",
                                "mage/adminhtml/wysiwyg/tiny_mce/setup"
                            ], function(jQuery) {
                                wysiwygShortDescription = new wysiwygSetup("short_description", {
                                    "width" : "100%",
                                    "height" : "200px",
                                    "plugins" : [{"name":"image"}],
                                    "tinymce" : {
                                        "toolbar":"formatselect | bold italic underline | "+
                                        "alignleft aligncenter alignright |" +
                                        "bullist numlist |"+
                                        "link table charmap","plugins":"advlist "+
                                        "autolink lists link charmap media noneditable table "+
                                        "contextmenu paste code help table",
                                    },
                                    files_browser_window_url: "<?= /* @noEscape */$block->getWysiwygUrl();?>"
                                });
                                wysiwygShortDescription.setup("exact");
                            });
                        </script>
                    <?php endif; ?>
                </div>
            </div>
            <?php if ($skuType == 'static'): ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('SKU')) ?>:
                        <span class="tooltip">ℹ️
                            <span class="tooltiptext"><?= $escaper->escapeHtml(__('SKU stands for Stock Keeping Unit. It is a unique identifier assigned to each product to track inventory and sales.')) ?></span>
                        </span>
                    </label>
                    <?php
                    if ($skuPrefix) {
                        /* @noEscape */ echo "(Prefix - ".$skuPrefix.")";
                    }
                    ?>
                    <?php if ($product_hint_status && $helper->getProductHintSku()): ?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintSku()) ?>"/>
                    <?php endif; ?>
                    <div class="control">
                        <input name="product[sku]" id="sku"
                        class="required-entry validate-length maximum-length-64 input-text" type="text"
                        value="<?= $escaper->escapeHtml($data['product']['sku'])?>"/>
                    </div>
                    <div id="skuavail" >
                        <span class="success-msg skuavailable"><?= $escaper->escapeHtml(__('SKU Available')) ?></span>
                    </div>
                    <div id="skunotavail" >
                        <span class="error-msg skunotavailable">
                            <?= $escaper->escapeHtml(__('SKU Already Exist')) ?></span>
                    </div>
                </div>
            <?php endif; ?>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Price')) ?><b>
                    <?= /* @noEscape */ " (".$currency_symbol.")"; ?></b>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the price for the product.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintPrice()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintPrice()) ?>"/>
                <?php endif; ?>
                <div class="control">
                    <input type="text" class="required-entry validate-zero-or-greater input-text"
                    name="product[price]" id="price" value="<?= /* @noEscape */ $data['product']['price']?>"/>
                </div>
            </div>
            <div class="actions">
                <button id="tier_price" title="Advanced Pricing" type="button" class="action-default scalable">
                    <span>Advanced Pricing</span>
                </button>
            </div>
            <div class="field" id="tierPriceFields" style="display: none;">
                <div class="control">
                    <div class="admin__field">
                        <label class="admin__field-label">
                            <span>Advanced Pricing</span>
                        </label>

                        <div class="admin__field-control" data-role="grid-wrapper" data-index="tier_price">
                            <div class="admin__control-table-wrapper">
                                <table class="admin__dynamic-rows admin__control-table" data-role="grid" data-index="tier_price">
                                    <thead>
                                        <tr>
                                            <th>
                                                <span>Website</span>
                                            </th>
                                            <th>
                                                <span>Customer Group</span>
                                            </th>
                                            <th class="_required">
                                                <span>Quantity</span>
                                            </th>
                                            <th>
                                                <span>Price</span>
                                            </th>
                                            <th>
                                                <span></span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="tierPriceBody">
                                        <!-- Rows will be dynamically added here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="actions">
                    <button id="addTierPrice" title="Add Tier Price" type="button" class="action-default scalable">
                        <span>Add Tier Price</span>
                    </button>
                </div>
            </div>

            <script>
                var advancedPricingBtn = document.getElementById('tier_price');
                var tierPriceFields = document.getElementById('tierPriceFields');

                advancedPricingBtn.addEventListener('click', function () {
                    if (tierPriceFields.style.display === 'none') {
                        tierPriceFields.style.display = 'block';
                    } else {
                        tierPriceFields.style.display = 'none';
                    }
                });

                var addTierPriceBtn = document.getElementById('addTierPrice');
                var tierPriceBody = document.getElementById('tierPriceBody');
                var index = 0;

                addTierPriceBtn.addEventListener('click', function () {
                    var newRow = document.createElement('tr');
                    newRow.classList.add('data-row');
                    newRow.innerHTML = `
                    <td>
                        <div class="admin__field" data-index="website_id">
                            <div class="admin__field-control">
                                <select class="admin__control-select" name="product[tier_price][${index}][website_id]">
                                    <option value="0">All Websites [USD]</option>
                                </select>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="admin__field" data-index="cust_group">
                            <div class="admin__field-control">
                                <select class="admin__control-select" name="product[tier_price][${index}][cust_group]">
                                    <option value="32000">ALL GROUPS</option>
                                    <option value="0">NOT LOGGED IN</option>
                                    <option value="1">General</option>
                                    <option value="2">Wholesale</option>
                                    <option value="3">Retailer</option>
                                </select>
                            </div>
                        </div>
                    </td>
                    <td class="_required">
                        <div class="admin__field" data-index="price_qty">
                            <div class="admin__field-control">
                                <input class="admin__control-text" type="text" name="product[tier_price][${index}][price_qty]">
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="admin__field" data-index="price_value">
                            <select class="admin__control-select" name="product[tier_price][${index}][value_type]">
                                <option value="percent">Percentage</option>
                            </select>
                        </div>
                    </td>
                    <td class="control-grouped admin__control-fields">
                        <fieldset class="admin__field" data-index="value_type">
                            <div class="admin__field-control control-grouped admin__control-fields">
                                <div class="admin__control-addon">
                                    <input class="admin__control-text" type="text" name="product[tier_price][${index}][percentage_value]">
                                </div>
                            </div>
                        </fieldset>
                        <button class="deleteTierPrice">Delete</button>
                    </td>
                `;
                    tierPriceBody.appendChild(newRow);
                    index++;
                });

                tierPriceBody.addEventListener('click', function (event) {
                    if (event.target.classList.contains('deleteTierPrice')) {
                        event.target.closest('tr').remove();
                        index--;
                    }
                });
            </script>
            <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price')) ?><b>
                        <?= /* @noEscape */ " (".$currency_symbol.")"; ?></b>:</label>
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a special price for the product.')) ?></span>
                    </span>
                    <?php if ($product_hint_status && $helper->getProductHintSpecialPrice()): ?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintSpecialPrice()) ?>"/>
                    <?php endif; ?>
                    <div class="control">
                        <input type="text" class="widthinput input-text validate-zero-or-greater"
                         name="product[special_price]" id="special-price"
                         value="<?= /* @noEscape */ $data['product']['special_price']?>"/>
                    </div>
                </div>
                <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price From')) ?>:</label>
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the date from which the special price for this product will be applicable.')) ?></span>
                    </span>
                    <?php if ($product_hint_status && $helper->getProductHintStartDate()): ?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintStartDate()) ?>"/>
                    <?php endif; ?>
                    <div class="control">
                        <input type="text" name="product[special_from_date]" id="special-from-date"
                        class="input-text" value="<?= /* @noEscape */ $data['product']['special_from_date']?>"/>
                    </div>
                </div>
                <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price To')) ?>:</label>
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the end date for the special price of the product.')) ?></span>
                    </span>
                    <?php if ($product_hint_status && $helper->getProductHintEndDate()): ?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintEndDate()) ?>"/>
                    <?php endif; ?>
                    <div class="control">
                        <input type="text" name="product[special_to_date]" id="special-to-date"
                        class="input-text" value="<?= /* @noEscape */ $data['product']['special_to_date']?>"/>
                    </div>
                </div>
            <input id="inventory_manage_stock" type="hidden" name="product[stock_data][manage_stock]" value="1">
            <input type="hidden" value="1" name="product[stock_data][use_config_manage_stock]"
            id="inventory_use_config_manage_stock">
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Stock')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please specify the available quantity of the product.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintQty()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintQty()) ?>"/>
                <?php endif; ?>
                <div class="control">
                    <input type="text" class="required-entry validate-number input-text"
                    name="product[quantity_and_stock_status][qty]" id="qty"
                    value="<?= /* @noEscape */ $data['product']['quantity_and_stock_status']['qty']?>"/>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Stock Availability')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Indicate whether the product is currently in stock or out of stock.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintStock()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                    class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintStock()) ?>"/>
                <?php endif; ?>
                <div class="control">
                    <select id="" class="select" name="product[quantity_and_stock_status][is_in_stock]">
                        <option value="1" <?php if ($data['product']['quantity_and_stock_status']['is_in_stock'] == 1) {
                             echo "selected='selected'";}?>><?= $escaper->escapeHtml(__("In Stock")); ?></option>
                        <option value="0" <?php if ($data['product']['quantity_and_stock_status']['is_in_stock'] == 0) {
                             echo "selected='selected'";}?>><?= $escaper->escapeHtml(__("Out of Stock")); ?></option>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Visibility')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the visibility option for your product. This determines where your product will be displayed.')) ?></span>
                </span>
                <div class="control">
                    <select id="visibility" class=" required-entry required-entry select" name="product[visibility]">
                        <option value=""><?= $escaper->escapeHtml(__('Please Select'))?></option>
                        <?php $product_visibility = $helper->getVisibilityOptionArray(); ?>
                        <?php foreach ($product_visibility as $key => $value): ?>
                            <option value="<?= /* @noEscape */ $key ?>"
                             <?php if ($key==$data['product']['visibility']) {
                                    echo "selected='selected'";}?>><?=  /* @noEscape */ $value?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Tax Class')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the tax class for your product. This determines the tax rate applied to the product during purchase.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintTax()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                     class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintTax()) ?>"/>
                <?php endif; ?>
                <div class="control">
                    <select id="tax-class-id" class=" required-entry required-entry select"
                    name="product[tax_class_id]">
                        <option value="0"><?= $escaper->escapeHtml(__('None'))?></option>
                        <?php $taxes = $helper->getTaxClassModel(); ?>
                        <?php foreach ($taxes as $tax): ?>
                            <option value="<?= $escaper->escapeHtml($tax->getId()) ?>"
                            <?php if ($tax->getId()==$data['product']['tax_class_id']) { echo "selected='selected'";}?>>
                            <?= $escaper->escapeHtml($tax->getClassName())?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Weight')) ?> (<?= $escaper->escapeHtml($weightUnit)?>):</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the weight of the product in ' . $weightUnit . '. This is used for shipping calculations and other purposes.')) ?></span>
                </span>
                <?php if ($product_hint_status && $helper->getProductHintWeight()): ?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintWeight()) ?>"/>
                <?php endif; ?>
                <div data-role="weight-switcher">
                    <label data-ui-id="product-tabs-attributes-tab-element-radios-product-product-has-weight-label"
                     for="weight-switcher">
                        <span><?= $escaper->escapeHtml(__('Does this have a weight?'))?></span>
                    </label>
                    <div class="control">
                        <div class="control">
                            <input type="radio" <?php if ($type!='virtual' || $type!='downloadable' ||
                             $data['product']['product_has_weight']==1) { ?> checked="checked" <?php } ?>
                              class="weight-switcher" id="weight-switcher1" value="1"
                              name="product[product_has_weight]">
                            <label for="weight-switcher1">
                                <span><?= $escaper->escapeHtml(__('Yes'))?></span>
                            </label>
                        </div>
                        <div class="control">
                            <input type="radio" class="weight-switcher" id="weight-switcher0" value="0"
                            name="product[product_has_weight]" <?php if ($type=='virtual' || $type=='downloadable' ||
                             $data['product']['product_has_weight']==0) { ?> checked="checked" <?php } ?>>
                            <label for="weight-switcher0">
                                <span><?= $escaper->escapeHtml(__('No'))?></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="control">
                    <input type="text" class="validate-zero-or-greater input-text" name="product[weight]"
                    id="weight" value="<?= /* @noEscape */ $data['product']['weight']?>" placeholder="Enter weight in lbs"/>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Url Key')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a unique identifier for the product in the URL. Use lowercase letters, hyphens (-), and underscores (_) for better SEO. Avoid spaces and special characters.')) ?></span>
                </span>
                <div class="control">
                    <input type="text" class="input-text" name="product[url_key]" id="meta_title"
                     value="<?= /* @noEscape */ $data['product']['url_key']?>"/>
                </div>
            </div>
            <?php if (!$helper->getCustomerSharePerWebsite()): ?>
                <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Product in Websites')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the websites where the product should be visible. This determines the storefronts where the product will be available for purchase.')) ?></span>
                </span>
                    <div class="control">
                        <select id="websites" class="required-entry select" name="product[website_ids][]" multiple>
                            <?php $websites = $helper->getAllWebsites(); ?>
                            <?php foreach ($websites as $website): ?>
                                <?php if($curWebsite == $website->getName()){ ?>
                                    <option value="<?= /* @noEscape */ $website->getWebsiteId() ?>">
                                    <?=  /* @noEscape */ $website->getName()?></option>
                                <?php } ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Title')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a brief and descriptive title for the product. This title appears in search engine results and browser tabs, influencing click-through rates and SEO.')) ?></span>
                </span>
                <div class="control">
                    <input type="text" class="input-text" name="product[meta_title]" id="meta_title"
                     value="<?= /* @noEscape */ $data['product']['meta_title']?>"/>
                </div>

            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Keywords')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter relevant keywords separated by commas that describe the product. These keywords can help improve the product visibility in search engine results')) ?></span>
                </span>
                <div class="control">
                    <textarea class="textarea" id="meta_keyword" name="product[meta_keyword]">
                        <?= /* @noEscape */ $data['product']['meta_keyword']?></textarea>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Description')) ?>:</label>
                <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a concise and informative description of the product. This description appears in search engine results below the title, influencing click-through rates and SEO.')) ?></span>
                </span>
                <div class="control">
                    <textarea class="textarea" id="meta_description" name="product[meta_description]">
                        <?= /* @noEscape */ $data['product']['meta_description']?></textarea>
                </div>
            </div>
            <?= $block->getChildHtml(); ?>
        </fieldset>
    </div>
</form>
<?php
$formData = [
    'countryPicSelector' => '#country-pic',
    'verifySkuAjaxUrl' => $block->getUrl('marketplace/product/verifysku', ['_secure' => $block
    ->getRequest()->isSecure()]),
    'categoryTreeAjaxUrl' => $block->getUrl('marketplace/product/categorytree/', ['_secure' => $block
    ->getRequest()->isSecure()])
];
$serializedFormData = $viewModel->getJsonHelper()->jsonEncode($formData);
?>

<script type="text/x-magento-init">
    {
        "*": {
            "sellerAddProduct": <?= /* @noEscape */ $serializedFormData; ?>
        }
    }
</script>
<?php
/** @var $block \Webkul\Marketplace\Block\Account\Dashboard */
$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$customerID =  $helper->getCustomerId();
$captchaEnableStatus = $helper->getCaptchaEnable();
?>
<?php if ($helper->getSellerProfileDisplayFlag()) { ?>
    <?= $block->getChildHtml('marketplace_dashboard_customer_review')?>
<?php } ?>
<div class="ask-que">
    <div id="wk-mp-ask-data">
        <div class="wk-mp-modals-wrapper">
            <aside tabindex="0" data-type="popup" data-role="modal"
            class="modal-popup modal-slide _inner-scroll wk-mp-model-popup">
                <div tabindex="0" data-role="focusable-start"></div>
                <div data-role="focusable-scope" class="modal-inner-wrap">
                    <header class="modal-header">
                        <h4 class="modal-title"><?= /* @noEscape */ __('Add Categories') ?></h4>
                        <button type="button" data-role="closeBtn" class="action-close wk-close">
                            <span>Close</span>
                        </button>
                        <span  class="wk-clear" ></span>
                    </header>
                    <form id="ask-form" method="post" action="#" class="fieldset"
                    data-role="ask-form" data-mage-init='{"validation":{}}'>
                        <div class="modal-body form-list field required">
                            <label class="label"><?= /* @noEscape */ __('Subject') ?> :</label>
                            <select name="subject" id="subject" class="wk-contact_input_fields required-entry">
                                <option value="add-category">Add Category</option>
                            </select>

                             <label class="label"><?= /* @noEscape */ __('Main Category') ?>:</label>
                            <input type="text" name="top_level_category" class="ask-form-input required-entry" />

                            <label class="labels"><?= /* @noEscape */ __('Sub Category') ?>:</label>
                            <input type="text" name="second_level_category" class="ask-form-input" />

                            <label class="labels"><?= /* @noEscape */ __('Child Category') ?>:</label>
                            <input type="text" name="child_level_category" class="ask-form-input" />

                            <label class="label"><?= /* @noEscape */ __('Your Query') ?> :</label>
                            <textarea name="quiry" class="queryquestion wk-contact_input_fields required-entry" style="width:100%;"></textarea>
                            <input type="hidden" name="seller_id" value="<?= /* @noEscape */ $customerID; ?>" />
                            <?php if ($captchaEnableStatus) { ?>
                                <div>
                                    <span>
                                        <label for="wk-mp-captcha">
                                            <span id="wk-mp-captchalable1"><?= /* @noEscape */ rand(1, 20) ?></span> +
                                            <span id="wk-mp-captchalable2"><?= /* @noEscape */ rand(1, 20) ?></span> =
                                        </label>
                                    </span>
                                    <input type="text" class="required-entry wk-contact_input_fields" name="wk-mp-captcha" id="wk-mp-captcha" />
                                </div>
                            <?php } ?>
                        </div>

                        <div class="modal-footer">
                            <span class="error"></span>
                            <span class="errormail"></span>
                            <input type="reset" value="<?= /* @noEscape */ __('Reset') ?>"
                             id="resetbtn" class="wk-btn wk-btn_default"/>
                            <input type="submit" value="<?= /* @noEscape */ __('Submit') ?>"
                             id="askbtn" class="wk-btn wk-btn-primary clickask"/>
                            <span class="wk-clear"></span>
                        </div>
                    </form>
                </div>
                <div tabindex="0" data-role="focusable-end"></div>
            </aside>
        </div>
    </div>
</div>
<?php
$formData = [
    'loader'                =>$block->getViewFileUrl('images/loader-2.gif'),
    'mpLocationChartSelector' => '#wk-location-chart',
    'mpYearLocationChartSelector' => '#wk-location-chart-year',
    'mpAskDataSelector'     => '#wk-mp-ask-data',
    'askQueSelector'        => '#askque',
    'askFormSelector'       => '#ask-form',
    'askFormInputSelector'  => '#ask-form input',
    'askFormTextareaSelector' =>'#ask-form textarea',
    'pageWrapperSelector'   => '.wk-mp-page-wrapper',
    'mpModelPopupSelector'  => '.wk-mp-model-popup',
    'showClass'             => '_show',
    'mageErrorClass'        => 'mage-error',
    'resetBtnSelector' => '#resetbtn',
    'wkCloseSelector' => '.wk-close',
    'validationFailedSelector' => '.validation-failed',
    'askFormValidationFailedSelector'   =>  '#ask-form .validation-failed',
    'askFormValidationAdviceSelector'   =>  '#ask-form .validation-advice',
    'askFormErrorMailSelector'      => '#ask-form .errormail',
    'askBtnSelector'        =>  '#askbtn',
    'mpCaptcha1Selector'    =>  '#wk-mp-captchalable1',
    'mpCaptcha2Selector'    =>  '#wk-mp-captchalable2',
    'mpCaptchaSelector'     =>  '#wk-mp-captcha',
    'mailProcssClass'       =>  'mail-procss',
    'captchaEnableStatus'   =>  $captchaEnableStatus,
    'ajaxMailSendUrl'       =>  $block->getUrl("sellerrequest/seller/savesellerrequest", ["_secure" => $block
    ->getRequest()->isSecure()]),
    'ajaxChartUrl'          =>  $block->getUrl("marketplace/account/chart", ["_secure" => $block
    ->getRequest()->isSecure()])
];
$serializedFormData = $viewModel->getJsonHelper()->jsonEncode($formData);
?>
<script type="text/x-magento-init">
    {
        "*": {
            "Comave_SellerRequest/js/selller-request": <?= /* @noEscape */ $serializedFormData; ?>
        }
    }
</script>
<script type='text/javascript'>
    require(['jquery', 'prototype', 'domReady!'], function($) {
        var qty = $('#qty'),
            productType = $('#product_type_id').val(),
            stockAvailabilityField = $('#quantity_and_stock_status'),
            manageStockField = $('#inventory_manage_stock'),
            useConfigManageStockField = $('#inventory_use_config_manage_stock'),
            fieldsAssociations = {
                'qty': 'inventory_qty',
                'quantity_and_stock_status': 'inventory_stock_availability'
            };

        var qtyDefaultValue = qty.val();
    })
</script>
<script>
    require([
        "jquery",
        "Webkul_Marketplace/catalog/type-events"
    ], function($, TypeSwitcher){
        var $form = $('[data-form=edit-product]');
        $form.data('typeSwitcher', TypeSwitcher.init());
    });
</script>
<script type="text/x-magento-init">
    {
        "*": {
            "Webkul_Marketplace/js/product/weight-handler": {},
            "Webkul_Marketplace/catalog/apply-to-type-switcher": {}
        }
    }
</script>
<script type="text/javascript">
    require(['jquery', 'mage/translate', 'Magento_Ui/js/modal/modal'], function($, mageTranslate, modal){
        $(document).ready(function () {
            $('#preview-btn').click(function (e) {
                e.preventDefault(); // Prevent default form submission

                var formData = new FormData($('#edit-product')[0]);

                var baseurl = '<?php echo $CurrentBaseUrl; ?>';
                var currentImageIndex = 0; // Track the current image index

                // Send AJAX request
                $.ajax({
                    url: '<?php echo $block->getUrl('sellerrequest/sellerproducts/preview'); ?>',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function(response) {
                        console.log(response);
                        var detailsHtml = '<div class="product-details">';
                        detailsHtml += '<div class="product_images">';

                        // Append images to the detailsHtml
                        if (response.media_gallery && response.media_gallery.length > 0) {
                            response.media_gallery.forEach(function(imageUrl, index) {
                                // Construct the full image URL using the base URL
                                var fullImageUrl = baseurl + imageUrl;
                                // Append the image HTML with data-index attribute
                                detailsHtml += '<img src="' + fullImageUrl + '" alt="Product Image" data-index="' + index + '" style="display: none;">';
                            });

                            // Show the previous and next buttons only if there is more than one image
                            if (response.media_gallery.length > 1) {
                                detailsHtml += '<div class="product-navigation">';
                                detailsHtml += '<button class="prev navigation-button"><i class="icon-chevron-left"></i></button>';
                                detailsHtml += '<button class="next navigation-button"><i class="icon-chevron-right"></i></button>';
                                detailsHtml += '</div>';
                            }
                        }
                        detailsHtml += '</div>';
                        detailsHtml += '<div class="product_info">';
                        detailsHtml += '<div class="product-name"><h2>Name: <span>' + (response.Name ? response.Name : '') + '</span></h2></div>';

                        detailsHtml += '<div class="price_sku">';
                        detailsHtml += '<div class="product-price"><p>Price: <span>' + (response.Price ? response.Price : '') + '</span></p></div>';
                        detailsHtml += '<div class="stock_details">';
                        detailsHtml += '<div class="product-stock-availability"><p>Stock Availability: <span>' + (response['Stock Availability'] ? response['Stock Availability'] : '') + '</span></p></div>';
                        detailsHtml += '<div class="product-sku"><p>SKU: <span>' + (response.Sku ? response.Sku : '') + '</span></p></div>';
                        detailsHtml += '</div>';
                        detailsHtml += '</div>';

                        detailsHtml += '<div class="product-quantity"><p>Quantity: <span>' + (response.Quantity ? response.Quantity : '') + '</span></p></div>';
                        detailsHtml += '<div class="product-visibility"><p>Visibility: <span>' + (response.Visibility ? response.Visibility : '') + '</span></p></div>';
                        detailsHtml += '<div class="product-weight"><p>Weight: <span>' + (response.Weight ? response.Weight : '') + '</span></p></div>';
                        detailsHtml += '<div class="product-categories">';
                        if (response.category_ids && response.category_ids.length > 0) {
                            detailsHtml += '<div>';
                            detailsHtml += 'Categories: ';
                            detailsHtml += response.category_ids.map(function(category) {
                                return '<span>' + category + '</span>';
                            }).join(', '); // Joining categories with comma
                            detailsHtml += '</div>';
                        }
                        detailsHtml += '</div>';

                        detailsHtml += '<div class="seller-categories">';
                        if (response.seller_category_ids && response.seller_category_ids.length > 0) {
                            detailsHtml += '<div>';
                            detailsHtml += 'Seller Categories: ';
                            detailsHtml += response.seller_category_ids.map(function(category) {
                                return '<span>' + category + '</span>';
                            }).join(', '); // Joining categories with comma
                            detailsHtml += '</div>';
                        }
                        detailsHtml += '</div>';

                        // Close the product-details div
                        detailsHtml += '</div>';
                         detailsHtml += '</div>';

                        detailsHtml += '<div class="product_info_description">';
                        detailsHtml += '<div class="product-description"><p>Description: <span>' + (response.Description ? response.Description : '') + '</span></p></div>';
                        detailsHtml += '<div class="product-short-description"><p>Short Description: <span>' + (response.short_description ? response.short_description : '') + '</span></p></div>';
                        detailsHtml += '</div>';
                        // Show modal popup
                        var options = {
                            type: 'popup',
                            responsive: true,
                            innerScroll: true,
                            title: mageTranslate('Product Preview'),
                            buttons: [{
                                text: mageTranslate('Close'),
                                class: '',
                                click: function () {
                                    this.closeModal();
                                }
                            }]
                        };

                        var popup = modal(options, $('#custom-modal'));
                        $('#custom-modal .modal-content').html(detailsHtml);
                        $('#custom-modal').modal('openModal');

                        // Attach event handlers for previous and next buttons
                        $('.navigation-button.prev').click(function() {
                            showImage(currentImageIndex - 1);
                        });

                        $('.navigation-button.next').click(function() {
                            showImage(currentImageIndex + 1);
                        });

                        // Show the first image initially
                        showImage(currentImageIndex);
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    },
                    complete: function() {
                        // Stop loader
                        $('body').trigger('processStop');
                    }
                });

                // Function to show the image at a specific index
                function showImage(index) {
                    var $images = $('.product-details img');
                    if (index < 0) {
                        index = $images.length - 1; // Loop back to the last image
                    } else if (index >= $images.length) {
                        index = 0; // Loop back to the first image
                    }
                    currentImageIndex = index;
                    $images.hide().eq(index).show();
                }
            });
        });
    });
</script>

<div id="custom-modal">
    <div class="modal-inner-wrap">
        <div class="modal-content"></div>
    </div>
</div>
