<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var $block \Webkul\Marketplace\Block\Product\Create */

$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$product_hint_status = $helper->getProductHintStatus();
$currency_code = $helper->getCurrentCurrencyCode();
$currency_symbol = $helper->getCurrencySymbol();
$product_id=$block->getRequest()->getParam('id');
$product_coll = $block->getProduct($product_id);

$websiteIds= $product_coll->getWebsiteIds();
$attribute_set_id = $product_coll['attribute_set_id'];
if ($block->getRequest()->getParam('set')) {
    $attribute_set_id = $block->getRequest()->getParam('set');
}
$skuType = $helper->getSkuType();
$weightUnit = $helper->getWeightUnit();

$blockName = $block->getLayout()->createBlock('Comave\Club\Block\Banner');
$curWebsite = $blockName->getWebName();
?>
<form action="<?= $escaper->escapeUrl($block->getUrl('marketplace/product/save', ['_secure' => $block
->getRequest()->isSecure()])) ?>" enctype="multipart/form-data" method="post"
id="edit-product" data-form="edit-product" data-mage-init='{"validation":{}}'>
    <div class="wk-mp-design" id="wk-bodymain">
        <fieldset class="fieldset info wk-mp-fieldset">
            <div data-mage-init='{"formButtonAction": {}}' class="wk-mp-page-title legend">
                <span><?= $escaper->escapeHtml(__('Edit Product')) ?></span>
                <button class="button wk-mp-btn" title="<?= $escaper->escapeHtml(__('Save')) ?>"
                type="submit" id="save-btn">
                    <span><span><?= $escaper->escapeHtml(__('Save')) ?></span></span>
                </button>
                <button class="button wk-mp-btn"
                title="<?= $escaper->escapeHtml(__('Save & Duplicate')) ?>" type="button"
                id="wk-mp-save-duplicate-btn">
                    <span><span><?= $escaper->escapeHtml(__('Save & Duplicate')) ?></span></span>
                </button>
            </div>
            <?= $block->getBlockHtml('formkey')?>
            <?= $block->getBlockHtml('seller.formkey')?>
            <input id="product_type_id" name="type" type="hidden"
            value="<?= /* @noEscape */ $product_coll['type_id']?>">
            <input type="hidden" name="id" value="<?= /* @noEscape */ $product_id; ?>" />
            <input type="hidden" name="product_id" value="<?= /* @noEscape */ $product_id; ?>" />
            <?php if (count($helper->getAllowedSets()) > 1) { ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('Attribute Set')) ?>:</label>
                    <div class="tooltip">
                            <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                            <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the attribute set for update product ')) ?></span>
                    </div>
                    <div class="control">
                        <select name="set" id="attribute-set-id"  class="required-entry">
                        <?php foreach ($helper->getAllowedSets() as $set) {?>
                            <option value="<?= /* @noEscape */ $set['value'] ?>"
                            <?php if ($attribute_set_id==$set['value']) { ?> selected="selected" <?php } ?>>
                            <?= /* @noEscape */ $set['label']?></option>
                        <?php } ?>
                        </select>
                    </div>
                </div>
                <?php
            } else {
                $allowedSets = $helper->getAllowedSets();
                if (!empty($allowedSets)) { ?>
                    <input type="hidden" name="set" id="attribute-set-id"
                    value="<?= /* @noEscape */ $allowedSets[0]['value'] ?>" />
                    <?php
                } else { ?>
                    <input type="hidden" name="set" id="attribute-set-id"
                     value="<?= /* @noEscape */ $attribute_set_id ?>" />
                    <?php
                }
            } ?>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Product Category')) ?>:</label>
               <span class="tooltip">ℹ️
               <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the category for edit your product')) ?></span>
               </span>
                <?php
                if ($product_hint_status && $helper->getProductHintCategory()) {?>
                    <img src="<?= $escaper->escapeUrl($block->
                    getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                     title="<?= $escaper->escapeHtml($helper->getProductHintCategory()) ?>"/>
                    <?php
                } ?>

                <?php if ($helper->getIsAdminViewCategoryTree()) { ?>
                    <div data-bind="scope: 'sellerCategory'">
                        <!-- ko template: getTemplate() --><!-- /ko -->
                    </div>
                    <?php $categories = $product_coll->getCategoryIds();?>
                    <script type="text/x-magento-init">
                        {
                            "*": {
                                "Magento_Ui/js/core/app": {
                                    "components": {
                                        "sellerCategory": {
                                            "component": "Webkul_Marketplace/js/product/seller-category-tree",
                                            "template" : "Webkul_Marketplace/seller-category-tree",
                                            "filterOptions": true,
                                            "levelsVisibility": "1",
                                            "options": <?= /* @noEscape */ $block->getCategoriesTree()?>,
                                            "value": <?= /* @noEscape */ json_encode($categories)?>
                                        }
                                    }
                                }
                            }
                        }
                    </script>
                <?php } else { ?>
                    <div class="wk-field wk-category">
                        <div class="wk-for-validation">
                            <div id="wk-category-label"><?= $escaper->escapeHtml(__("CATEGORIES")); ?></div>
                            <?php
                            $categories = $product_coll->getCategoryIds();
                            $cat_ids=implode(",", $categories);
                            foreach ($categories as $value) {
                                ?>
                                <input type="hidden" name="product[category_ids][]"
                                value="<?= /* @noEscape */ $value; ?>"
                                id="wk-cat-hide<?= /* @noEscape */ $value; ?>"/>
                                <?php
                            }
                            ?>
                            <?php
                            if ($helper->getAllowedCategoryIds()) {
                                $storeconfig_catids = explode(',', trim($helper->getAllowedCategoryIds()));
                                foreach ($storeconfig_catids as $storeconfig_catid) {
                                    $cat_model = $block->getCategory()->load($storeconfig_catid);
                                    if (isset($cat_model["entity_id"]) && $cat_model["entity_id"]) {
                                        ?>
                                        <div class="wk-cat-container">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($cat_model->getName()) ?></span>
                                            <?php
                                            if (in_array($cat_model["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value=<?= /* @noEscape */ $cat_model['entity_id'] ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value='<?= /* @noEscape */ $cat_model['entity_id'] ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    }
                                }
                            } else {
                                $count = 0;
                                $category_helper = $viewModel->getCategoryHelper();
                                $category_model = $block->getCategory();
                                $_categories = $category_helper->getStoreCategories();
                                foreach ($_categories as $_category) {
                                    $count++;
                                    if (count($category_model->getAllChildren($category_model
                                    ->load($_category['entity_id'])))-1 > 0) { ?>
                                        <div class="wk-cat-container" style="margin-left:0px;">
                                            <span class="wk-plus">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($_category->getName()) ?></span>
                                            <?php
                                            if (in_array($_category["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value=<?= $escaper->escapeHtml($_category['entity_id']) ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value='<?= $escaper->escapeHtml($_category['entity_id']) ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    } else { ?>
                                        <div class="wk-cat-container">
                                            </span><span class="wk-foldersign"></span>
                                            <span class="wk-elements wk-cat-name">
                                                <?= $escaper->escapeHtml($_category->getName()) ?></span>
                                            <?php
                                            if (in_array($_category["entity_id"], $categories)) {?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                 value=<?= $escaper->escapeHtml($_category['entity_id']) ?> checked />
                                                <?php
                                            } else { ?>
                                                <input class="wk-elements" type="checkbox"
                                                name="product[category_ids][]"
                                                value='<?= $escaper->escapeHtml($_category['entity_id']) ?>'/>
                                                <?php
                                            } ?>
                                        </div>
                                        <?php
                                    }
                                }
                            } ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Product Name')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Edit the name of your product')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintName()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                     title="<?= $escaper->escapeHtml($helper->getProductHintName()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <input type="text" class="required-entry input-text" name="product[name]" id="name"
                     value="<?= $escaper->escapeHtml($product_coll->getName()); ?>"/>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Description')) ?>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please provide a brief description of the product')) ?></span>
                </span>:</label>
                <?php
                if ($product_hint_status && $helper->getProductHintDesc()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                     class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintDesc()) ?>"/>
                    <?php
                } ?>
                <div class="control wk-border-box-sizing">
                    <textarea name="product[description]" class="required-entry input-text" id="description"
                    rows="5" cols="75" ><?= /* @noEscape */ $product_coll->getDescription(); ?></textarea>
                    <?php if ($helper->isWysiwygEnabled()): ?>
                        <script>
                            require([
                                "jquery",
                                "mage/translate",
                                "mage/adminhtml/events",
                                "mage/adminhtml/wysiwyg/tiny_mce/setup"
                            ], function(jQuery) {
                                wysiwygDescription = new wysiwygSetup("description", {
                                    "width" : "100%",
                                    "height" : "200px",
                                    "plugins" : [{"name":"image"}],
                                    "tinymce" : {
                                        "toolbar":"formatselect | bold italic underline | "+
                                        "alignleft aligncenter alignright |" +
                                        "bullist numlist |"+
                                        "link table charmap","plugins":"advlist "+
                                        "autolink lists link charmap media noneditable table "+
                                        "contextmenu paste code help table",
                                    },
                                    files_browser_window_url: "<?= /* @noEscape */$block->getWysiwygUrl();?>"
                                });
                                wysiwygDescription.setup("exact");
                            });
                        </script>
                    <?php endif; ?>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Short Description')) ?>:</label>
                <div class="tooltip">
                <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please provide short description for edit product ')) ?></span>
                </div>
                <?php
                if ($product_hint_status && $helper->getProductHintShortDesc()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= /* @noEscape */ $helper->getProductHintShortDesc() ?>"/>
                    <?php
                } ?>
                <div class="control wk-border-box-sizing">
                    <textarea name="product[short_description]" class="input-text" id="short_description"
                    rows="5" cols="75" ><?= /* @noEscape */ $product_coll->getShortDescription(); ?></textarea>
                    <?php if ($helper->isWysiwygEnabled()): ?>
                        <script>
                            require([
                                "jquery",
                                "mage/translate",
                                "mage/adminhtml/events",
                                "mage/adminhtml/wysiwyg/tiny_mce/setup"
                            ], function(jQuery) {
                                wysiwygShortDescription = new wysiwygSetup("short_description", {
                                    "width" : "100%",
                                    "height" : "200px",
                                    "plugins" : [{"name":"image"}],
                                    "tinymce" : {
                                        "toolbar":"formatselect | bold italic underline | "+
                                        "alignleft aligncenter alignright |" +
                                        "bullist numlist |"+
                                        "link table charmap","plugins":"advlist "+
                                        "autolink lists link charmap media noneditable table "+
                                        "contextmenu paste code help table",
                                    },
                                    files_browser_window_url: "<?= /* @noEscape */$block->getWysiwygUrl();?>"
                                });
                                wysiwygShortDescription.setup("exact");
                            });
                        </script>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            $mpProStatus = 0;
            $mpProColl = $helper->getSellerProductDataByProductId($product_id);
            foreach ($mpProColl as $key => $value) {
                $mpProStatus = $value['status'];
            }
            if (!$helper->getIsProductEditApproval() && $mpProStatus==1) { ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('Status')) ?>:</label>
                    <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the status of this product')) ?></span>
                    </span>
                    <?php
                    if ($product_hint_status && $helper->getProductHintEnable()) {?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintStatus()) ?>"/>
                        <?php
                    } ?>
                    <?php
                    $enableStatusCheck = "";
                    $disableStatusCheck ="";
                    if ($product_coll->getStatus()==1) {
                        $enableStatusCheck =  'checked="checked"';
                    }
                    if ($product_coll->getStatus()==2) {
                        $disableStatusCheck =  'checked="checked"';
                    }
                    ?>
                    <div class="control">
                        <input type="radio" name="status" id="status1" value="1"
                        <?= /* @noEscape */ $enableStatusCheck ?>/><?= $escaper->escapeHtml(__("Enable")); ?><br>
                        <input type="radio" name="status" id="status2" value="2"
                        <?= /* @noEscape */ $disableStatusCheck ?>/><?= $escaper->escapeHtml(__("Disable")); ?>
                    </div>
                </div>
                <?php
            } ?>
            <?php
            if ($skuType == 'static') { ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('SKU')) ?>:</label>
                    <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('It is a unique identifier assigned to each product')) ?></span>
                    </span>
                    <?php
                    if ($product_hint_status && $helper->getProductHintSku()) {?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                         title="<?= $escaper->escapeHtml($helper->getProductHintSku()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <input name="product[sku]" id="sku"
                        class="required-entry validate-length maximum-length-64 input-text" type="text"
                         value="<?= $escaper->escapeHtml($product_coll->getsku()); ?>"/>
                    </div>
                    <div id="skuavail" >
                        <span class="success-msg skuavailable"><?= $escaper->escapeHtml(__('SKU Available')) ?></span>
                    </div>
                    <div id="skunotavail" >
                        <span class="error-msg skunotavailable">
                            <?= $escaper->escapeHtml(__('SKU Already Exist')) ?></span>
                    </div>
                </div>
                <?php
            } ?>
            <div class="field required <?php if ($product_coll['type_id']=='configurable') { ?> no-display <?php } ?>">
                <label class="label"><?= $escaper->escapeHtml(__('Price')) ?><b>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the price for edit product.')) ?></span>
                </span>
                    <?= /* @noEscape */ " (".$currency_symbol.")"; ?></b>:</label>
                <?php
                if ($product_hint_status && $helper->getProductHintPrice()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>"
                     class='questimg' title="<?= $escaper->escapeHtml($helper->getProductHintPrice()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <input type="text" class="required-entry validate-zero-or-greater input-text"
                     name="product[price]" id="price" value="<?= /* @noEscape */ $product_coll->getPrice() ?>"
                      data-ui-id="product-tabs-attributes-tab-fieldset-element-text-product-price"/>
                </div>
            </div>
                <div class="actions">
                    <button id="tier_price" title="Advanced Pricing" type="button" class="action-default scalable">
                        <span>Advanced Pricing</span>
                    </button>
                </div>

                <div class="field" id="tierPriceFields" style="display: none;">
                    <div class="control">
                        <div class="admin__field">
                            <label class="admin__field-label">
                                <span>Advanced Pricing</span>
                            </label>
                            <div class="admin__field-control" data-role="grid-wrapper" data-index="tier_price">
                                <div class="admin__control-table-wrapper">
                                    <table class="admin__dynamic-rows admin__control-table" data-role="grid" data-index="tier_price">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <span>Website</span>
                                                </th>
                                                <th>
                                                    <span>Customer Group</span>
                                                </th>
                                                <th class="_required">
                                                    <span>Quantity</span>
                                                </th>
                                                <th>
                                                    <span>Price</span>
                                                </th>
                                                <th>
                                                    <span></span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody id="tierPriceBody">
                                            <!-- Rows will be dynamically added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="actions">
                        <button id="addTierPrice" title="Add Tier Price" type="button" class="action-default scalable">
                            <span>Add Tier Price</span>
                        </button>
                    </div>
                </div>

<?php $tierprice = $product_coll->getTierPrice(); ?>
<script>
    var tierPriceBody = document.getElementById('tierPriceBody');
    var tierPriceFields = document.getElementById('tierPriceFields');
    var advancedPricingBtn = document.getElementById('tier_price');
    var addTierPriceBtn = document.getElementById('addTierPrice');

    // Function to check if the tier price data is empty
    function isEmpty(obj) {
        for(var key in obj) {
            if(obj.hasOwnProperty(key))
                return false;
        }
        return true;
    }

    // Display existing tier prices if available
    <?php if (!empty($tierprice)): ?>
        <?php foreach ($tierprice as $key => $value): ?>
            var newRow = document.createElement('tr');
            newRow.classList.add('data-row');
            newRow.innerHTML = `
                <td>
                    <div class="admin__field" data-index="website_id">
                        <div class="admin__field-control">
                            <select class="admin__control-select" name="product[tier_price][<?php echo $key; ?>][website_id]">
                                <option value="0">All Websites [USD]</option>
                            </select>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="admin__field" data-index="cust_group">
                        <div class="admin__field-control">
                            <select class="admin__control-select" name="product[tier_price][<?php echo $key; ?>][cust_group]">
                                <?php foreach (['32000' => 'ALL GROUPS', '0' => 'NOT LOGGED IN', '1' => 'General', '2' => 'Wholesale', '3' => 'Retailer'] as $optionValue => $optionLabel): ?>
                                    <?php if ($optionValue == $value['cust_group']): ?>
                                        <option value="<?php echo $optionValue; ?>" selected><?php echo $optionLabel; ?></option>
                                    <?php else: ?>
                                        <option value="<?php echo $optionValue; ?>"><?php echo $optionLabel; ?></option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </td>
                <td class="_required">
                    <div class="admin__field" data-index="price_qty">
                        <div class="admin__field-control">
                            <input class="admin__control-text" type="text" name="product[tier_price][<?php echo $key; ?>][price_qty]" value="<?php echo $value['price_qty']; ?>">
                        </div>
                    </div>
                </td>
                <td>
                    <div class="admin__field" data-index="price_value">
                        <select class="admin__control-select" name="product[tier_price][<?php echo $key; ?>][value_type]">
                            <option value="percent">Percentage</option>
                        </select>
                    </div>
                </td>
                <td class="control-grouped admin__control-fields">
                    <fieldset class="admin__field" data-index="value_type">
                        <div class="admin__field-control control-grouped admin__control-fields">
                            <div class="admin__control-addon">
                                <input class="admin__control-text" type="text" name="product[tier_price][<?php echo $key; ?>][percentage_value]" value="<?php echo $value['percentage_value']; ?>">
                            </div>
                        </div>
                    </fieldset>
                   <button class="deleteTierPrice">Delete</button>
                </td>
            `;
            tierPriceBody.appendChild(newRow);
        <?php endforeach; ?>
    <?php endif; ?>

    // Toggle display of tier pricing fields when clicking on Advanced Pricing button
    advancedPricingBtn.addEventListener('click', function () {
        if (tierPriceFields.style.display === 'none') {
            tierPriceFields.style.display = 'block';
        } else {
            tierPriceFields.style.display = 'none';
        }
    });

    // Add Tier Price button click event
    addTierPriceBtn.addEventListener('click', function () {
        var newRow = document.createElement('tr');
        newRow.classList.add('data-row');
        newRow.innerHTML = `
            <td>
                <div class="admin__field" data-index="website_id">
                    <div class="admin__field-control">
                        <select class="admin__control-select" name="product[tier_price][new][website_id]">
                            <option value="0">All Websites [USD]</option>
                        </select>
                    </div>
                </div>
            </td>
            <td>
                <div class="admin__field" data-index="cust_group">
                    <div class="admin__field-control">
                        <select class="admin__control-select" name="product[tier_price][new][cust_group]">
                            <option value="32000">ALL GROUPS</option>
                            <option value="0">NOT LOGGED IN</option>
                            <option value="1">General</option>
                            <option value="2">Wholesale</option>
                            <option value="3">Retailer</option>
                        </select>
                    </div>
                </div>
            </td>
            <td class="_required">
                <div class="admin__field" data-index="price_qty">
                    <div class="admin__field-control">
                        <input class="admin__control-text" type="text" name="product[tier_price][new][price_qty]">
                    </div>
                </div>
            </td>
            <td>
                <div class="admin__field" data-index="price_value">
                    <select class="admin__control-select" name="product[tier_price][new][value_type]">
                        <option value="percent">Percentage</option>
                    </select>
                </div>
            </td>
            <td class="control-grouped admin__control-fields">
                <fieldset class="admin__field" data-index="value_type">
                    <div class="admin__field-control control-grouped admin__control-fields">
                        <div class="admin__control-addon">
                            <input class="admin__control-text" type="text" name="product[tier_price][new][percentage_value]">
                        </div>
                    </div>
                </fieldset>
                <button class="deleteTierPrice">Delete</button>
            </td>
        `;
        tierPriceBody.appendChild(newRow);
    });

    // Delete Tier Price button click event
    tierPriceBody.addEventListener('click', function (event) {
        if (event.target.classList.contains('deleteTierPrice')) {
            event.target.closest('tr').remove();
        }
    });
</script>




            <?php if ($product_coll['type_id']!='configurable') { ?>
                <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price')) ?><b>
                        <?= /* @noEscape */  " (".$currency_symbol.")"; ?></b>:</label>
                        <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the special price for edit product.')) ?></span>
                    </span>
                    <?php
                    if ($product_hint_status && $helper->getProductHintSpecialPrice()) {?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintSpecialPrice()) ?>"/>
                        <?php
                    } ?>
                    <div class="control">
                        <input type="text" class="widthinput input-text validate-zero-or-greater"
                        name="product[special_price]" id="special-price"
                        value="<?= /* @noEscape */ $product_coll->getSpecialPrice() ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price From')) ?>:</label>
                    <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the date from which the special price for this product will be applicable.')) ?></span>
                    </span>
                    <?php
                    if ($product_hint_status && $helper->getProductHintStartDate()) {?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintStartDate()) ?>"/>
                        <?php
                    } ?>
                    <?php
                    if ($product_coll->getData('special_from_date')) {
                        $special_from_date = $block->formatDate($product_coll->getData('special_from_date'));
                    } else {
                        $special_from_date = '';
                    } ?>
                    <div class="control">
                        <input type="text" name="product[special_from_date]" id="special-from-date" class="input-text"
                        value="<?= /* @noEscape */ $special_from_date; ?>"/>
                    </div>
                </div>
                <div class="field">
                    <label class="label"><?= $escaper->escapeHtml(__('Special Price To')) ?>:</label>
                    <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the end date for the special price of the product.')) ?></span>
                    </span>
                    <?php
                    if ($product_hint_status && $helper->getProductHintEndDate()) {?>
                        <img src="<?= $escaper->escapeUrl($block
                        ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                        title="<?= $escaper->escapeHtml($helper->getProductHintEndDate()) ?>"/>
                        <?php
                    } ?>
                    <?php
                    if ($product_coll->getData('special_to_date')) {
                        $special_to_date = $block->formatDate($product_coll->getData('special_to_date'));
                    } else {
                        $special_to_date = '';
                    } ?>
                    <div class="control">
                        <input type="text" name="product[special_to_date]" id="special-to-date"
                        class="input-text" value="<?= /* @noEscape */ $special_to_date; ?>" />
                    </div>
                </div>
                <?php
            } ?>
            <input id="inventory_manage_stock" type="hidden" name="product[stock_data][manage_stock]" value="1">
            <input type="hidden" value="1" name="product[stock_data][use_config_manage_stock]"
            id="inventory_use_config_manage_stock">
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Stock')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Please specify the available quantity of the product.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintQty()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintQty()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <input type="text" class="required-entry validate-number input-text"
                    name="product[quantity_and_stock_status][qty]" id="qty"
                    value="<?= /* @noEscape */ $product_coll['quantity_and_stock_status']['qty'] ?>"/>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Stock Availability')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Indicate whether the product is currently in stock or out of stock.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintStock()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintStock()) ?>"/>
                    <?php
                } ?>
                <?php
                $stockSelect = "";
                $outStockSelect ="";
                $is_in_stock = $product_coll['quantity_and_stock_status']['is_in_stock'];
                if ($is_in_stock==1) {
                    $stockSelect = "selected";
                }
                if ($is_in_stock==0) {
                    $outStockSelect = "selected";
                }
                ?>
                <div class="control">
                    <select id="" class="select" name="product[quantity_and_stock_status][is_in_stock]">
                        <option <?= /* @noEscape */  $stockSelect?> value="1">
                        <?= $escaper->escapeHtml(__("In Stock")); ?></option>
                        <option <?= /* @noEscape */  $outStockSelect ?> value="0">
                        <?= $escaper->escapeHtml(__("Out of Stock")); ?></option>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Visibility')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the visibility option for your product. This determines where your product will be displayed.')) ?></span>
                </span>
                <div class="control">
                    <select id="visibility" class=" required-entry required-entry select"
                    name="product[visibility]">
                        <option value=""><?= $escaper->escapeHtml(__('Please Select'))?></option>
                        <?php
                        $product_visibility = $helper->getVisibilityOptionArray();
                        foreach ($product_visibility as $key => $value) {
                            $visSelect = "";
                            if ($key==$product_coll->getVisibility()) { $visSelect = "selected='selected'";}
                            ?>
                            <option value="<?= $escaper->escapeHtml($key) ?>"
                            <?= /* @noEscape */ $visSelect?>>
                            <?= $escaper->escapeHtml($value)?></option>
                            <?php
                        } ?>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Tax Class')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the tax class for your product. This determines the tax rate applied to the product during purchase.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintTax()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                     title="<?= $escaper->escapeHtml($helper->getProductHintTax()) ?>"/>
                    <?php
                } ?>
                <div class="control">
                    <select id="tax-class-id" class=" required-entry required-entry select"
                     name="product[tax_class_id]">
                        <option value="0"><?= $escaper->escapeHtml(__('None'))?></option>
                        <?php
                        $taxid=$product_coll->getData('tax_class_id');
                        $taxes=$helper->getTaxClassModel();
                        foreach ($taxes as $tax) {
                            ?>
                            <option <?= $taxid==$tax->getId()? 'selected':''; ?>
                            value="<?= $escaper->escapeHtml($tax->getId()) ?>">
                            <?= $escaper->escapeHtml($tax->getClassName())?></option>
                            <?php
                        } ?>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label class="label"><?= $escaper->escapeHtml(__('Weight')) ?> (<?= $escaper
                ->escapeHtml($weightUnit)?>):</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter the weight of the product in lbs This is used for shipping calculations and other purposes.')) ?></span>
                </span>
                <?php
                if ($product_hint_status && $helper->getProductHintWeight()) {?>
                    <img src="<?= $escaper->escapeUrl($block
                    ->getViewFileUrl('Webkul_Marketplace::images/quest.png')); ?>" class='questimg'
                    title="<?= $escaper->escapeHtml($helper->getProductHintWeight()) ?>"/>
                    <?php
                } ?>
                <div data-role="weight-switcher">
                    <label data-ui-id="product-tabs-attributes-tab-element-radios-product-product-has-weight-label"
                     for="weight-switcher">
                        <span><?= $escaper->escapeHtml(__('Does this have a weight?'))?></span>
                    </label>
                    <div class="control">
                        <div class="control">
                            <input type="radio" <?php if ($product_coll['type_id']=='simple' ||
                             ($product_coll['type_id']=='configurable' && !empty($product_coll['weight']))) {
                                    ?>checked="checked" <?php } ?> class="weight-switcher" id="weight-switcher1"
                                  value="1" name="product[product_has_weight]">
                            <label for="weight-switcher1">
                                <span><?= $escaper->escapeHtml(__('Yes'))?></span>
                            </label>
                        </div>
                        <div class="control">
                            <input type="radio" <?php if ($product_coll['type_id']=='downloadable' ||
                             $product_coll['type_id']=='virtual' || ($product_coll['type_id']=='configurable'
                              && empty($product_coll['weight']))) { ?>checked="checked" <?php } ?>
                              class="weight-switcher" id="weight-switcher0" value="0"
                              name="product[product_has_weight]">
                            <label for="weight-switcher0">
                                <span><?= $escaper->escapeHtml(__('No'))?></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="control">
                    <input type="text" class="validate-zero-or-greater input-text"
                    name="product[weight]" id="weight"
                    value="<?= $escaper->escapeHtml($product_coll->getWeight());?>"
                     <?php if ($product_coll['type_id']=='downloadable' || $product_coll['type_id']=='virtual') {
                            ?>disabled="disabled" <?php } ?>/>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Url Key')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a unique identifier for the product in the URL. Use lowercase letters, hyphens (-), and underscores (_) for better SEO. Avoid spaces and special characters.')) ?></span>
                </span>
                <div class="control">
                    <input type="text" class="input-text" name="product[url_key]" id="url_key"
                    value="<?= $escaper->escapeHtml($product_coll['url_key']) ?>"/>
                </div>
            </div>
            <?php if (!$helper->getCustomerSharePerWebsite()): ?>
                <div class="field required">
                    <label class="label"><?= $escaper->escapeHtml(__('Product in Websites')) ?>:</label>
                    <span class="tooltip">ℹ️
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select the websites where the product should be visible. This determines the storefronts where the product will be available for purchase.')) ?></span>
                    </span>
                    <div class="control">
                        <select id="websites" class="required-entry select" name="product[website_ids][]" multiple>
                            <?php $websites = $helper->getAllWebsites(); ?>
                            <?php foreach ($websites as $website): ?>
                                <?php if($curWebsite == $website->getName()){ ?>
                                    <option value="<?= /* @noEscape */ $website->getWebsiteId() ?>"
                                    <?= in_array($website->getWebsiteId(), $websiteIds)? 'selected':''; ?>  >
                                    <?=  /* @noEscape */ $website->getName()?></option>
                                <?php } ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Title')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a brief and descriptive title for the product. This title appears in search engine results and browser tabs, influencing click-through rates and SEO.')) ?></span>
                </span>
                <div class="control">
                    <input type="text" class="input-text" name="product[meta_title]" id="meta_title"
                    value="<?= $escaper->escapeHtml($product_coll['meta_title']) ?>"/>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Keywords')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter relevant keywords separated by commas that describe the product. These keywords can help improve the product visibility in search engine results')) ?></span>
                </span>
                <div class="control">
                    <textarea class="textarea" id="meta_keyword" name="product[meta_keyword]">
                        <?= $escaper->escapeHtml($product_coll['meta_keyword']) ?></textarea>
                </div>
            </div>
            <div class="field">
                <label class="label"><?= $escaper->escapeHtml(__('Meta Description')) ?>:</label>
                <span class="tooltip">ℹ️
                <span class="tooltiptext"><?= $escaper->escapeHtml(__('Enter a concise and informative description of the product. This description appears in search engine results below the title, influencing click-through rates and SEO.')) ?></span>
                </span>
                <div class="control">
                    <textarea class="textarea" id="meta_description" name="product[meta_description]">
                        <?= $escaper->escapeHtml($product_coll['meta_description']) ?></textarea>
                </div>
            </div>
            <?= $block->getChildHtml(); ?>
        </fieldset>
    </div>
</form>
<?php
$formData = [
    'productTypeId' => $product_coll['type_id'],
    'categories' => implode(',', $categories),
    'countryPicSelector' => '#country-pic',
    'verifySkuAjaxUrl' => $block->getUrl('marketplace/product/verifysku', ['_secure' => $block
    ->getRequest()->isSecure()]),
    'productid'  => $product_id,
    'categoryTreeAjaxUrl' => $block->getUrl('marketplace/product/categorytree/', ['_secure' => $block
    ->getRequest()->isSecure()])
];
$serializedFormData = $viewModel->getJsonHelper()->jsonEncode($formData);
?>

<script type="text/x-magento-init">
    {
        "*": {
            "sellerEditProduct": <?= /* @noEscape */ $serializedFormData; ?>
        }
    }
</script>
<script type='text/javascript'>
    require(['jquery', 'prototype', 'domReady!'], function($) {
        var qty = $('#qty'),
            productType = $('#product_type_id').val(),
            stockAvailabilityField = $('#quantity_and_stock_status'),
            manageStockField = $('#inventory_manage_stock'),
            useConfigManageStockField = $('#inventory_use_config_manage_stock'),
            fieldsAssociations = {
                'qty': 'inventory_qty',
                'quantity_and_stock_status': 'inventory_stock_availability'
            };

        var qtyDefaultValue = qty.val();
    })
</script>
<script>
    require([
        "jquery",
        "Webkul_Marketplace/catalog/type-events"
    ], function($, TypeSwitcher){
        var $form = $('[data-form=edit-product]');
        $form.data('typeSwitcher', TypeSwitcher.init());
    });
</script>
<script type="text/x-magento-init">
    {
        "*": {
            "Webkul_Marketplace/js/product/weight-handler": {},
            "Webkul_Marketplace/catalog/apply-to-type-switcher": {}
        }
    }
</script>
