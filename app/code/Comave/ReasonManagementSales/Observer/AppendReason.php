<?php

declare(strict_types=1);

namespace Comave\ReasonManagementSales\Observer;

use Comave\ReasonManagement\Api\ReasonManagementInterface;
use Comave\ReasonManagement\Model\ConfigProvider;
use Comave\ReasonManagement\Model\ReasonRegistry;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;

class AppendReason implements ObserverInterface
{
    /**
     * @param ReasonRegistry $reasonRegistry
     * @param ConfigProvider $configProvider
     * @param ReasonManagementInterface $reasonManagement
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ReasonRegistry $reasonRegistry,
        private readonly ConfigProvider $configProvider,
        private readonly ReasonManagementInterface $reasonManagement,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        if (!$this->configProvider->isEnabled()) {
            $this->logger->notice(
                '[ReasonObserver] Unable to add reason, config disabled'
            );

            return;
        }

        $entity = $observer->getData('data_object');

        if (!$entity instanceof OrderInterface) {
            $this->logger->warning(
                '[ReasonObserver] Unable to add reason, entity not an order'
            );

            return;
        }

        if (!$entity->isCanceled()) {
            return;
        }

        $entityReason = $this->reasonRegistry->getReason();

        if ($entityReason === null) {
            $this->logger->warning(
                '[ReasonObserver] Unable to add reason, reason missing'
            );

            return;
        }

        $this->reasonManagement->addReason(
            OrderInterface::class,
            $entity->getId(),
            $entityReason->getCode(),
            $entityReason->getContext()
        );

        $this->logger->info(
            '[ReasonObserver] Added reason to entity',
            [
                'entityId' => $entity->getId(),
                'reasonId' => $entityReason->getId()
            ]
        );

        $this->reasonRegistry->clear();
    }
}
