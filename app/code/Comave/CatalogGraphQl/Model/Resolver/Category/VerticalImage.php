<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Magento\Catalog\Model\Category;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Category vertical image resolver
 */
class VerticalImage implements ResolverInterface
{
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(StoreManagerInterface $storeManager)
    {
        $this->storeManager = $storeManager;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model']) || !$value['model'] instanceof Category) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Category $category */
        $category = $value['model'];
        $verticalImage = $category->getData('vertical_image');

        if (!$verticalImage) {
            return null;
        }

        try {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(
                \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
            );
            return $mediaUrl . $verticalImage;
        } catch (\Exception $e) {
            return null;
        }
    }
}
