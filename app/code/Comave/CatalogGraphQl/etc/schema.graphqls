type Query {
    brandsList(
        pageSize: Int = 20
            @doc(
                description: "The maximum number of results to return at once. The default value is 20."
            )
        currentPage: Int = 1
            @doc(
                description: "The page of results to return. The default value is 1."
            )
    ): Brands
        @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Brands")
        @doc(description: "List of available brands")
}

interface ProductInterface {
    visible_attributes: [VisibleFrontendAttribute]
        @doc(
            description: "Returns all attribute values and labels marked as visible on storefront"
        )
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\VisibleAttributes"
        )
    lix_points: Int!
        @doc(
            description: "Returns Lix Points obtained after buying this product"
        )
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward"
        )
    xp_points: Int!
        @doc(
            description: "Returns Comave XP Points obtained after buying this product"
        )
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward"
        )
    seller: Seller
        @doc(description: "Returns the seller's details.")
        @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Seller")
    brand: Brand
        @doc(description: "Returns Product Brand Information")
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Brand"
        )
    status: Int!
        @doc(description: "Returns product status")
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Status"
        )
}

type Brand {
    value: String! @doc(description: "Returns product brand value")
    name: String! @doc(description: "Returns product brand name")
    slug: String! @doc(description: "Returns sroduct brand slug used in urls")
}

type Brands @doc(description: "Contains the available brands list.") {
    items: [Brand] @doc(description: "List of available brands.")
    pageSize: Int @doc(description: "The total number of brands per page.")
    currentPage: Int @doc(description: "Current page.")
    totalPages: Int
        @doc(description: "Total number of pages for given page size.")
    totalCount: Int @doc(description: "The total number of brands in the list.")
}

type ProductImage {
    role: [String]
        @doc(description: "Role of the image")
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\ProductImageRole"
        )
}

type Seller {
    name: String @doc(description: "Seller's full name.")
    address: String
        @doc(
            description: "Seller's default shipping address (Street, County, Country)."
        )
    phone: String @doc(description: "Seller's phone number ('phone_no').")
    email_info: String @doc(description: "Seller's email info ('email_info').")
}

type VisibleFrontendAttribute {
    label: String! @doc(description: "Attribute label")
    values: [String!]
        @doc(
            description: "Attribute values or attribute option values for selects and multiselects"
        )
    type: String! @doc(description: "Attribute type")
}

interface CategoryInterface {
    horizontal_image: String
        @doc(description: "Category horizontal image URL")
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\HorizontalImage"
        )
    vertical_image: String
        @doc(description: "Category vertical image URL")
        @resolver(
            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\VerticalImage"
        )
}

input ProductAttributeFilterInput {
    brand: FilterEqualTypeInput
        @doc(description: "Product Data filter with Brand Value")
    status: FilterEqualTypeInput
        @doc(
            description: "Filter products by status (1 for Enabled, 2 for Disabled)."
        )
}
