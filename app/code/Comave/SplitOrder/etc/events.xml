<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="catalog_product_type_prepare_lite_options">
        <observer name="sellerProductOptionLite" instance="Comave\SplitOrder\Observer\SellerData"/>
    </event>

    <event name="catalog_product_type_prepare_full_options">
        <observer name="sellerProductOptionFull" instance="Comave\SplitOrder\Observer\SellerData"/>
    </event>

    <event name="sales_order_status_history_collection_load_before">
        <observer name="loadRelevantComments" instance="Comave\SplitOrder\Observer\FilterComments"/>
    </event>

    <event name="checkout_submit_before">
        <observer name="preventMultipleItemsInCartBeforeOrder"
                  disabled="true"/>
    </event>

    <event name="marketplace_commission_pre_dispatch">
        <observer name="advanced_commision" instance="Comave\SplitOrder\Observer\AdvancedCommission"/>
    </event>
</config>
