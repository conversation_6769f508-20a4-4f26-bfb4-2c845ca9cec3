<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="comave_logger">
            <group id="split_order" translate="label" type="text" sortOrder="950" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Split Order Logging</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <depends>
                    <field id="comave_logger/general/enabled">1</field>
                </depends>
            </group>
        </section>
    </system>
</config>
