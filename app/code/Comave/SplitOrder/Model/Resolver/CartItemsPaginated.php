<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Resolver;

use Comave\SplitOrder\Api\SellerCartDetailsInterface;
use Comave\SplitOrder\Service\PaginatedSellerItems;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Model\Quote;

/**
 * @inheritdoc
 */
class CartItemsPaginated implements ResolverInterface
{
    private const SORT_ORDER_BY = 'item_id';
    private const SORT_ORDER = 'ASC';

    /**
     * @param PaginatedSellerItems $paginatedSellerItems
     */
    public function __construct(private readonly PaginatedSellerItems $paginatedSellerItems)
    {
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Quote $cart */
        $cart = $value['model'];
        $this->validate($args);

        $pageSize = $args['pageSize'];
        $currentPage = $args['currentPage'];
        $offset = ($currentPage - 1) * $pageSize;
        $order = CartItemsPaginated::SORT_ORDER;
        $orderBy = CartItemsPaginated::SORT_ORDER_BY;

        if (!empty($args['sort'])) {
            $order = $args['sort']['order'];
            $orderBy = mb_strtolower($args['sort']['field']);
        }

        $sellerItems = [];
        $allVisibleItems = $cart->getAllVisibleItems();

        foreach ($allVisibleItems as $item) {
            $sellerOption = $item->getOptionByCode('option_' . SellerCartDetailsInterface::SELLER_OPTION);

            if (empty($sellerOption)) {
                continue;
            }

            $sellerValue = json_decode($sellerOption->getValue() ?? '{}', true);

            if (!isset($sellerItems[$sellerValue['seller_id']])) {
                $sellerItems[$sellerValue['seller_id']] = [];
            }

            $sellerItems[$sellerValue['seller_id']][] = $item;
        }

        if (empty($sellerItems)) {
            return [];
        }

        $result = [];

        foreach ($sellerItems as $sellerId => $sellerItemList) {
            $result[] = $this->paginatedSellerItems->get(
                $cart,
                $currentPage,
                $pageSize,
                $offset,
                $orderBy,
                $order,
                $sellerId,
                $sellerItemList
            );
        }

        return $result;
    }

    /**
     * Validates arguments passed to resolver
     *
     * @param array $args
     * @throws GraphQlInputException
     */
    private function validate(array $args)
    {
        if (isset($args['currentPage']) && $args['currentPage'] < 1) {
            throw new GraphQlInputException(__('currentPage value must be greater than 0.'));
        }
        if (isset($args['pageSize']) && $args['pageSize'] < 1) {
            throw new GraphQlInputException(__('pageSize value must be greater than 0.'));
        }
    }
}
