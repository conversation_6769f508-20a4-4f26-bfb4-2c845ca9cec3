<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Adminhtml\Service;

use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\LoginAsCustomerApi\Api\GenerateAuthenticationSecretInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterface;

/**
 * Generates authentication secret
 */
class GenerateAuthenticationSecret implements GenerateAuthenticationSecretInterface
{
    /**#@+
     * Constants
     */
    private const string CUSTOMER_ID = 'customer_id';
    private const string MARKETPLACE_ORDER = 'marketplace_order';
    private const string ADMIN_ID = 'admin_id';
    private const string TIME_STAMP = 'time_stamp';
    /**#@-*/

    /**
     * @param DateTime $dateTime
     * @param EncryptorInterface $encryptor
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly DateTime $dateTime,
        private readonly EncryptorInterface $encryptor,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * @inheritdoc
     */
    public function execute(AuthenticationDataInterface $authenticationData): string
    {
        $currentTimestamp = $this->dateTime->timestamp();
        $customerId = $authenticationData->getCustomerId();
        $adminId = $authenticationData->getAdminId();
        return $this->encryptor->encrypt($this->serializer->serialize(
            [
                self::ADMIN_ID => $adminId,
                self::CUSTOMER_ID => $customerId,
                self::TIME_STAMP => $currentTimestamp,
                self::MARKETPLACE_ORDER => $authenticationData->getExtensionAttributes()?->getMarketplaceOrder()
            ]
        ));
    }
}
