<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Command;

use Comave\SplitOrder\Plugin\AppendSellerOrderNumber;
use Magento\Framework\App\ResourceConnection;

class GetNextIncrementNumber
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(private readonly ResourceConnection $resourceConnection)
    {
    }

    /**
     * @return int
     */
    public function get(): int
    {
        $connection = $this->resourceConnection->getConnection();
        $selectNextNumber = $connection->select()
            ->from(
                ['main' => $connection->getTableName('marketplace_orders')],
                [
                    'seller_order_number'
                ]
            )->order('entity_id DESC')
            ->limit(1);

        $lastNumber = (int) $connection->fetchOne($selectNextNumber);

        return ($lastNumber ?: AppendSellerOrderNumber::STARTING_NUMBER) + 1;
    }
}
