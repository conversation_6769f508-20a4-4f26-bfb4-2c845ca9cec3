<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Command;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SellerApi\Model\IntegrationTypePool;
use Magento\Framework\App\ResourceConnection;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Model\Order\Item;
use Psr\Log\LoggerInterface;

class GetSellerOrderInfo
{
    private array $loadedData = [];

    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param IntegrationTypePool $integrationTypePool
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly IntegrationTypePool $integrationTypePool,
    ) {
    }

    /**
     * @param Item $orderItem
     * @return array
     */
    public function get(Item $orderItem): array
    {
        if (isset($this->loadedData[$orderItem->getOrderId()][$orderItem->getId()])) {
            $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()]['integration'] = $this->getIntegration($orderItem);

            return $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()];
        }

        $connection = $this->resourceConnection->getConnection('read');
        $orderSelectQuery = $connection->select()
            ->from(
                ['sl' => $connection->getTableName('marketplace_saleslist')],
                [
                    'order_item_id',
                    'seller_id',
                    'entity_id' => 'order_id'
                ]
            )->join(
                ['so' => $connection->getTableName('marketplace_orders')],
                'so.order_id = sl.order_id AND so.seller_id = sl.seller_id',
                [
                    'seller_order_number'
                ]
            )->join(
                ['c' => $connection->getTableName('customer_entity')],
                'c.entity_id = sl.seller_id',
                [
                    'firstname',
                    'lastname'
                ]
            )->where(
                'sl.order_id = ?',
                $orderItem->getOrderId()
            );

        $this->loadedData[$orderItem->getOrderId()] = $connection->fetchAssoc($orderSelectQuery);

        if (
            !isset($this->loadedData[$orderItem->getOrderId()][$orderItem->getId()])
        ) {
            return [];
        }

        $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()]['integration'] = $this->getIntegration($orderItem);

        return $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()];
    }

    /**
     * @param OrderItemInterface $orderItem
     * @return IntegrationInterface|null
     */
    private function getIntegration(OrderItemInterface $orderItem): ?IntegrationInterface
    {
        try {
            $integration = $this->integrationTypePool->identifyIntegration(
                (string) $orderItem->getProductId()
            );

            return $integration->getIntegrationType() !== IntegrationTypePool::NON_INTEGRATED ?
                $integration : null;
        } catch (\Exception $e) {
            $this->logger->notice(
                '[Comave.SplitOrder] Unable to identify external order ID',
                [
                    'order_id' => $orderItem->getOrderId(),
                    'message' => $e->getMessage()
                ]
            );
        }

        return null;
    }
}
