<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Queue\Consumer;

//@todo - refactor into this
use Comave\SplitOrder\Service\MarketplaceOrderService;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Observer\SalesOrderPlaceAfterObserver;

class HandleSplitOrder
{
    public const string TOPIC_NAME = 'seller.order.split';

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param SalesOrderPlaceAfterObserver $salesOrderPlaceAfterObserver
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly SalesOrderPlaceAfterObserver $salesOrderPlaceAfterObserver,
        //@todo refactor this, post MVP
//        private readonly MarketplaceOrderService $marketplaceOrderService,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param string $orderId
     * @return void
     */
    public function execute(string $orderId): void
    {
        try {
            $this->logger->info(
                '[ComaveSplitOrder] Starting placing marketplace order',
                [
                    'order' => $orderId
                ]
            );
            /** @var Order $order */
            $order = $this->orderRepository->get($orderId);
            $this->salesOrderPlaceAfterObserver->orderPlacedOperations(
                $order,
                $orderId
            );
            $this->logger->info(
                '[ComaveSplitOrder] Finished placing marketplace order',
                [
                    'order' => $orderId
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[ComaveSplitOrder] Error placing marketplace order',
                [
                    'message' => $e->getMessage()
                ]
            );
        }
    }
}
