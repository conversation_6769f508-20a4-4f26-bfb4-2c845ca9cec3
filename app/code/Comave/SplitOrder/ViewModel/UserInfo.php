<?php

declare(strict_types=1);

namespace Comave\SplitOrder\ViewModel;

use Magento\Backend\Model\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Service\UserInfoService;

class UserInfo implements ArgumentInterface
{
    /**
     * @param UserInfoService $userInfoService
     * @param UrlInterface $urlBuilder
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly UserInfoService $userInfoService,
        private readonly UrlInterface $urlBuilder,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param int $productId
     * @return array
     */
    public function getUserInfo(int $productId): array
    {
        $defaultSellerInfo = [
            'name' => 'Not set',
            'url' => '#'
        ];

        try {
            $userInfo = $this->userInfoService->get($productId);

            if (empty($userInfo)) {
                return $defaultSellerInfo;
            }

            return [
                'name' => $userInfo['name'],
                'url' => $this->urlBuilder->getUrl(
                    'customer/index/edit',
                    [
                        'id' => $userInfo['id']
                    ]
                )
            ];
        } catch (\Throwable $e) {
            $this->logger->warning(
                '[AdminOrderCreateSeller] Unable to determine seller',
                [
                    'product_id' => $productId,
                    'message' => $e->getMessage(),
                ]
            );

            return $defaultSellerInfo;
        }
    }
}
