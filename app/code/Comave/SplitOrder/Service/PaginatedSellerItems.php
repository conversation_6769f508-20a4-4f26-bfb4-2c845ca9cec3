<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Service;

use Comave\Customer\Service\AddressFormatter;
use Comave\Customer\Service\DefaultShippingAddress;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Quote\Api\Data\CartItemInterface;
use Magento\Quote\Model\Quote;
use Magento\QuoteGraphQl\Model\CartItem\GetItemsData;
use Magento\QuoteGraphQl\Model\CartItem\GetPaginatedCartItems;

class PaginatedSellerItems
{
    /**
     * @param GetPaginatedCartItems $pagination
     * @param DefaultShippingAddress $defaultShippingAddress
     * @param AddressFormatter $addressFormatter
     * @param GetItemsData $getItemsData
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        private readonly GetPaginatedCartItems $pagination,
        private readonly DefaultShippingAddress $defaultShippingAddress,
        private readonly AddressFormatter $addressFormatter,
        private readonly GetItemsData $getItemsData,
        private readonly CustomerRepositoryInterface $customerRepository,
    ) {
    }

    /**
     * @param Quote $cart
     * @param int $pageSize
     * @param int $currentPage
     * @param int $offset
     * @param string $orderBy
     * @param string $order
     * @param CartItemInterface[] $sellerItems
     * @param string|int $sellerId
     * @return mixed[][]
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(
        Quote $cart,
        int $currentPage,
        int $pageSize,
        int $offset,
        string $orderBy,
        string $order,
        string|int $sellerId,
        array $sellerItems,
    ): array {
        $seller = $this->customerRepository->getById($sellerId);
        $defaultShippingAddress = null;
        $defaultShippingAddressId = $seller->getDefaultShipping();

        if ($defaultShippingAddressId) {
            $defaultShippingAddress = $this->defaultShippingAddress->getDefaultShippingAddress(
                (int) $defaultShippingAddressId
            );
        }

        $result = [
            'sellerDetails' => [
                'name' => sprintf('%s %s', $seller->getFirstname(), $seller->getLastname()),
                'email_info' => $seller->getEmail(),
                'address' => $this->addressFormatter->format($defaultShippingAddress),
                'phone' => $seller->getCustomAttribute('phone_no')?->getValue() ?? '',
                'shipping_information' => null
            ],
        ];

        $paginatedCartItems = $this->pagination->execute($cart, $pageSize, (int) $offset, $orderBy, $order);
        $cartItems = [];

        /** @var CartItemInterface $cartItem */
        foreach ($paginatedCartItems['items'] as $cartItem) {
            foreach ($sellerItems as $item) {
                if ($cartItem->getId() == $item->getId()) {
                    $cartItems[] = $item;
                }
            }
        }

        if (empty($cartItems)) {
            return $result;
        }

        $itemsData = $this->getItemsData->execute($cartItems);
        $result = array_merge(
            $result,
            [
                'items' => [
                    'items' => $itemsData,
                    'total_count' => $paginatedCartItems['total'],
                    'page_info' => [
                        'page_size' => $pageSize,
                        'current_page' => $currentPage,
                        'total_pages' => (int) ceil($paginatedCartItems['total'] / $pageSize)
                    ],
                ]
            ]
        );

        $shippingData = json_decode(
            $cart->getData('shipping_data') ?? '{}',
            true
        );

        if (empty($shippingData)) {
            return $result;
        }

        $sellerShippingInfo = current($shippingData[$sellerId] ?? []);

        if (empty($sellerShippingInfo)) {
            return $result;
        }

        $sellerShippingInfo['price'] = [
            'value' => $sellerShippingInfo['price'],
            'currency' => $cart->getQuoteCurrencyCode(),
        ];
        $result['sellerDetails']['shipping_information'] = $sellerShippingInfo;

        return $result;
    }
}
