<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Service;

use Magento\Catalog\Model\Category;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\Sales\Model\Order;
use Webkul\MpAdvancedCommission\Model\ResourceModel\CommissionRules\Collection;
use Webkul\MpAdvancedCommission\Model\ResourceModel\CommissionRules\CollectionFactory;
use Webkul\MpAdvancedCommission\Helper\Data;

//@todo ... too much to refactor
class CommissionFetcher
{
    private const int DEFAULT_ADMIN_COMMISSION_RATE = 10;

    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param Data $commissionHelper
     * @param AttributeRepositoryInterface $attributeRepository
     * @param CollectionFactory $commissionRuleFactory
     * @param ResourceConnection $resourceConnection
     * @param CartRepositoryInterface $cartRepository
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     */
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly Data $commissionHelper,
        private readonly AttributeRepositoryInterface $attributeRepository,
        private readonly CollectionFactory $commissionRuleFactory,
        private readonly ResourceConnection $resourceConnection,
        private readonly CartRepositoryInterface $cartRepository,
        private readonly \Webkul\Marketplace\Helper\Data $marketplaceHelper
    ) {
    }

    /**
     * @param Order\Item $orderItem
     * @return float
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(Order\Item $orderItem): float
    {
        $product = $orderItem->getProduct();

        if (!$product?->getId()) {
            return 0.00;
        }

        $categoryArray = [];
        $quoteId = $orderItem->getOrder()->getQuoteId();

        if (empty($quoteId)) {
            return 0.00;
        }

        $proCommission = 0;
        $quote = $this->cartRepository->get($quoteId);
        /** @var Quote $quote */
        $sellerData = $this->commissionHelper->getSellerData($quote);

        if ($this->commissionHelper->getUseCommissionRule()) {
            $categoryArray = $this->getProCommissionOnCommissionRule($sellerData);
        }

        $categoryCount = count($categoryArray);

        if ($categoryCount !== 0) {
            foreach ($categoryArray as $productId => $value) {
                if ($productId !== $product?->getId()) {
                    continue;
                }

                $proCommission = $value['amount'];
            }
        } else {
            $proCommission = $product->getCustomAttribute(
                'commission_for_product'
            )?->getValue() ?? 0;
            $proCommission= $this->getProCommissionWhenNull($proCommission, $product);
        }

        $productPrice = $product->getFinalPrice();
        $commType =  $this->marketplaceHelper->getCommissionType();
        $mpGlobalCommission = $this->marketplaceHelper->getConfigCommissionRate();

        return $proCommission > $productPrice && $commType === 'fixed' ?
            ($productPrice * $mpGlobalCommission) / 100 :
            $mpGlobalCommission;
    }

    /**
     * @param array $sellerData
     * @return array
     */
    private function getProCommissionOnCommissionRule(array $sellerData): array
    {
        $categoryArray = [];

        foreach ($sellerData as $sellerId => $row) {
            /** @var Collection $commissionRuleCollection */
            $commissionRuleCollection = $this->commissionRuleFactory->create();
            $commissionRuleCollection->addFieldToFilter(
                "price_from",
                ["lteq" => round($row['total'])]
            )->addFieldToFilter(
                "price_to",
                ["gteq" => round($row['total'])]
            );

            if (!$commissionRuleCollection->getSize()) {
                $commissionRuleCollection = $this->commissionRuleFactory->create();
                $commissionRuleCollection->addFieldToFilter(
                    "price_from",
                    ["lteq" => round($row['total'])]
                )
                ->addFieldToFilter(
                    "price_to",
                    "*"
                );
            }

            foreach ($commissionRuleCollection->getItems() as $commissionRule) {
                if ($commissionRule->getCommissionType() == "percent") {
                    foreach ($row['details'] as $item) {
                        $categoryArray[$item['product_id']] = [
                            "amount" => $item['price']*$commissionRule->getAmount()/100,
                            "type" => $commissionRule->getCommissionType()
                        ];
                    }
                } else {
                    foreach ($row['details'] as $item) {
                        $totalSellerAmount = $row['total'];
                        $perComPro = $commissionRule->getAmount() * 100 / $totalSellerAmount;
                        $categoryArray[$item['product_id']] = [
                            "amount" => $item['price'] * $perComPro / 100,
                            "type" => $commissionRule->getCommissionType()
                        ];
                    }
                }
                break;
            }
        }
        return $categoryArray;
    }

    /**
     * @param null|string $proCommission
     * @param $product
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getProCommissionWhenNull(?string $proCommission, $product)
    {
        $productPrice = $product->getFinalPrice();
        $productId = $product->getId();
        $commType =  $this->marketplaceHelper->getCommissionType();

        if (
            empty($proCommission) ||
            (
                $proCommission > $productPrice && $commType == 'fixed'
            )
        ) {
            $sellerProduct = $this->marketplaceHelper->getSellerProductDataByProductId($productId);
            $sellerData = current($sellerProduct->getData());
            $seller =  $this->customerRepository->getById($sellerData['seller_id']);

            if ($categoryCommission = $seller->getCustomAttribute('category_commission')?->getValue() ?? false) {
                $categoryCommission = array_filter(
                    json_decode($categoryCommission, true)
                );
            }

            $categoryCommissionValue = $this->getCategoryCommissions(
                $product->getId(),
                    $categoryCommission ?? []
            );

            if (!empty($categoryCommissionValue)) {
                $proCommission = $categoryCommissionValue;
            }
        }

        return $proCommission;
    }

    /**
     * @param string $productId
     * @param array $categoryCommissions
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getCategoryCommissions(string $productId, array $categoryCommissions = []): string
    {
        $connection = $this->resourceConnection->getConnection('read');
        $categoryCommissionAttr = $this->attributeRepository->get(
            Category::ENTITY,
            'commission_for_admin'
        );
        $defaultValue = self::DEFAULT_ADMIN_COMMISSION_RATE;

        $select = $connection->select()
            ->from(
                ['main' => $connection->getTableName('catalog_category_product')],
                []
            )->joinLeft(
                ['cc' => $connection->getTableName('catalog_category_entity_' . $categoryCommissionAttr->getBackendType())],
                'cc.row_id = main.category_id AND cc.attribute_id = ' . $categoryCommissionAttr->getAttributeId(),
                [
                    'value' => new \Zend_Db_Expr("COALESCE(MAX(cc.value), $defaultValue)")
                ]
            )->where(
                'product_id = ?',
                $productId
            )->group('product_id');

        if (!empty($categoryCommissions)) {
            $select->where(
                'category_id IN (?)',
                array_keys($categoryCommissions)
            );
        }

        return $connection->fetchOne($select) ?: '';
    }
}
