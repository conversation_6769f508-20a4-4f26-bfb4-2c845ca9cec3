<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Observer;

use Comave\SplitOrder\Service\CommissionFetcher;
use Magento\Framework\DataObject;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Module\Manager;
use Webkul\Marketplace\Helper\Data;

class AdvancedCommission implements ObserverInterface
{
    /**
     * @param Manager $moduleManager
     * @param Data $marketplaceHelper
     * @param ManagerInterface $eventManager
     * @param CommissionFetcher $commissionFetcher
     */
    public function __construct(
        private readonly Manager $moduleManager,
        private readonly Data $marketplaceHelper,
        private readonly ManagerInterface $eventManager,
        private readonly CommissionFetcher $commissionFetcher
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Observer $observer): void
    {
        if (!$this->moduleManager->isOutputEnabled('Webkul_MpAdvancedCommission')) {
            return;
        }

        /** @var DataObject $transport */
        $transport = $observer->getTransport();
        /** @var float $totalAmount */
        $totalAmount = $transport->getTotalAmount();
        /** @var \Magento\Sales\Model\Order\Item $item */
        $item = $transport->getItem();
        /** @var array $advanceCommissionRule */
        $advanceCommissionRule = $transport->getAdvancedCommission() ?: [];
        /** @var \Webkul\Marketplace\Model\Saleperpartner $commissionRule */
        $commissionRule = $transport->getCommissionRule();
        $commission = 0;

        if ($this->marketplaceHelper->getUseCommissionRule()) {
            $commission = $advanceCommissionRule[$item->getId()]['type'] === 'fixed' ?
                $advanceCommissionRule[$item->getId()]['amount'] :
                ($totalAmount * $advanceCommissionRule[$item->getId()]['amount']) / 100;

            $commissionRule->setCommission($commission);
            $transport->setCommmissionRule($commissionRule);

            return;
        }

        $this->eventManager->dispatch(
            'mp_advance_commission',
            [
                'id' => $item->getProduct()->getId()
            ]
        );

        $advanceCommission = $this->commissionFetcher->get($item);

        if ($advanceCommission != '') {
            $percent = $advanceCommission;
            $commType = $this->marketplaceHelper->getCommissionType();
            $commission = $commType == 'fixed' ?
                $percent :
                ($totalAmount * $advanceCommission) / 100;

            if ($commission > $totalAmount) {
                $commission = $totalAmount * $this->marketplaceHelper->getConfigCommissionRate() / 100;
            }
        }

        $commissionRule->setCommission($commission);
        $transport->setCommmissionRule($commissionRule);
    }
}
