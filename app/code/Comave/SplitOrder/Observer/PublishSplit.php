<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Observer;

use Comave\SplitOrder\Model\Queue\Consumer\HandleSplitOrder;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Sales\Model\Order;
use Psr\Log\LoggerInterface;

class PublishSplit implements ObserverInterface
{
    /**
     * @param PublisherInterface $publisher
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        //@todo when we refactor the webkul area
        return;
        /** @var Order $order */
        $order = $observer->getOrder();
        $this->logger->info(
            '[ComaveSplitOrder] Publish order for split',
            [
                'orderId' => $order->getIncrementId()
            ]
        );

        $this->publisher->publish(
            HandleSplitOrder::TOPIC_NAME,
            $order->getId()
        );
    }
}
