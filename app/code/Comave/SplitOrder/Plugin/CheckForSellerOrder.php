<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin;

use Magento\Customer\Model\Session;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\LoginAsCustomerApi\Api\AuthenticateCustomerBySecretInterface;

class CheckForSellerOrder
{
    /**
     * @param EncryptorInterface $encryptor
     * @param Session $customerSession
     */
    public function __construct(
        private readonly EncryptorInterface $encryptor,
        private readonly Session $customerSession
    ) {
    }

    /**
     * @param AuthenticateCustomerBySecretInterface $authenticateCustomerBySecret
     * @param $result
     * @param string $secret
     * @return void
     */
    public function afterExecute(
        AuthenticateCustomerBySecretInterface $authenticateCustomerBySecret,
        $result,
        string $secret
    ): void {
        try {
            $details = $this->encryptor->decrypt($secret);
            $unserialisedData = json_decode($details, true);

            if (!isset($unserialisedData['marketplace_order'])) {
                return;
            }

            $this->customerSession->setData(
                'marketplace_order',
                $unserialisedData['marketplace_order']
            );
        } catch (\Throwable) {//phpcs:ignore
        }
    }
}
