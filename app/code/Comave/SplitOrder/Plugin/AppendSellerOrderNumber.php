<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin;

use Comave\SplitOrder\Model\Command\GetNextIncrementNumber;
use Magento\Framework\Model\AbstractModel;
use Webkul\Marketplace\Model\ResourceModel\Orders;

class AppendSellerOrderNumber
{
    public const string STARTING_NUMBER = '100000000';

    /**
     * @param GetNextIncrementNumber $getNextIncrementNumber
     */
    public function __construct(private readonly GetNextIncrementNumber $getNextIncrementNumber)
    {
    }

    /**
     * @param Orders $orderResource
     * @param AbstractModel $orderModel
     * @return array|null
     */
    public function beforeSave(
        Orders $orderResource,
        AbstractModel $orderModel
    ): ?array {
        if ($orderModel->getId()) {
            return null;
        }

        $nextNumber = $this->getNextIncrementNumber->get();
        $orderModel->setData(
            'seller_order_number',
            $nextNumber
        );

        return [$orderModel];
    }
}
