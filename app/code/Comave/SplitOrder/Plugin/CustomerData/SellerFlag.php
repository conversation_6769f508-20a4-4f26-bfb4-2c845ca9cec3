<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin\CustomerData;

use Magento\Customer\Model\Session;
use Magento\Framework\UrlInterface;
use Webkul\Marketplace\Helper\Data;

class SellerFlag
{
    /**
     * @param UrlInterface $urlBuilder
     * @param Data $mpHelperData
     * @param Session $currentCustomer
     */
    public function __construct(
        private readonly UrlInterface $urlBuilder,
        private readonly Data $mpHelperData,
        private readonly Session $currentCustomer,
    ) {
    }

    /**
     * @param \Magento\Customer\CustomerData\Customer $customerSectionProvider
     * @param array $result
     * @return array
     */
    public function afterGetSectionData(
        \Magento\Customer\CustomerData\Customer $customerSectionProvider,
        array $result
    ): array {
        if (!$this->currentCustomer->getCustomerId()) {
            return $result;
        }

        $marketplaceOrderId = $this->currentCustomer->getData('marketplace_order') ?? null;
        return array_merge(
            $result,
            [
                'isSeller' => $this->isSeller(
                    (int) $this->currentCustomer->getCustomerId()
                ),
                'marketplaceOrder' => $marketplaceOrderId ?
                    $this->urlBuilder->getUrl(
                        'marketplace/order/view',
                        [
                            'id' => $marketplaceOrderId,
                        ]
                    ) : false
            ]
        );
    }

    /**
     * Check is seller
     * @param int $customerId
     * @return bool
     */
    private function isSeller(int $customerId): bool
    {
        return $this->mpHelperData->getSellerCollectionObj($customerId)
                ->addFieldToFilter('is_seller', true)
                ->getSize() > 0;
    }
}
