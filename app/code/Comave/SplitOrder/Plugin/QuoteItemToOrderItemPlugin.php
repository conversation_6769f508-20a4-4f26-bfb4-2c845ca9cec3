<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin;

use Comave\SplitOrder\Api\SellerCartDetailsInterface;
use Magento\Catalog\Model\Product\Type\AbstractType;
use Magento\Quote\Model\Quote\Item as QuoteItem;
use Magento\Sales\Model\Order\Item as OrderItem;

class QuoteItemToOrderItemPlugin
{
    /**
     * @param QuoteItem\ToOrderItem $subject
     * @param OrderItem $result
     * @param QuoteItem $quoteItem
     * @return OrderItem
     */
    public function afterConvert(
        \Magento\Quote\Model\Quote\Item\ToOrderItem $subject,
        OrderItem $result,
        QuoteItem $quoteItem
    ): OrderItem {
        $bySellerOption = $quoteItem->getOptionByCode(
            AbstractType::OPTION_PREFIX . SellerCartDetailsInterface::SELLER_OPTION
        );

        if (empty($bySellerOption)) {
            return $result;
        }

        $options = $result->getProductOptions() ?? [];
        $options[SellerCartDetailsInterface::SELLER_OPTION] = $bySellerOption->getValue();
        $result->setProductOptions($options);

        return $result;
    }
}
