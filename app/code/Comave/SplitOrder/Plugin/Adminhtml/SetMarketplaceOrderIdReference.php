<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin\Adminhtml;

use Magento\Framework\App\RequestInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterfaceFactory;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataExtensionInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataExtensionInterfaceFactory;

class SetMarketplaceOrderIdReference
{
    public function __construct(
        private readonly RequestInterface $request,
        private readonly AuthenticationDataExtensionInterfaceFactory $authenticationExtensionFactory,
    ) {
    }

    /**
     * @param AuthenticationDataInterfaceFactory $authenticationDataFactory
     * @param AuthenticationDataInterface $authenticationData
     * @return AuthenticationDataInterface
     */
    public function beforeCreate(
        AuthenticationDataInterfaceFactory $authenticationDataFactory,
        array $data
    ): ?array {
        if (!$this->request->getParam('marketplaceOrderId')) {
            return null;
        }

        /** @var AuthenticationDataInterface $extension */
        $extension = $data['extensionAttributes'] ?? $this->authenticationExtensionFactory->create();
        $extension->setMarketplaceOrder(
            $this->request->getParam('marketplaceOrderId')
        );
        $data['extensionAttributes'] = $extension;

        return [$data];
    }
}
