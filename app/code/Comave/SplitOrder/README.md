# Comave Cart Customizations

Customizes the customerCart and cart graphql endpoints with grouped items per seller

**Query:**

```graphql
query getCart($cartId: String!)
{
  cart(cart_id: $cartId) {
      prices {
        subtotal_including_tax {
            value
            currency
        }
        subtotal_excluding_tax {
            value
            currency
        }
        grand_total {
            value
            currency
        }

           applied_taxes {
          amount {
              value
              currency
          }
      }
      }
    email
    billing_address {
      city
      country {
        code
        label
      }
      firstname
      lastname
      postcode
      region {
        code
        label
      }
      street
      telephone
    }
    shipping_addresses {
        # is_pickup_address
        # pickup_information
      firstname
      lastname
      street
      city
      region {
        code
        label
      }
      country {
        code
        label
      }
      telephone
      available_shipping_methods {
        amount {
          currency
          value
        }
        available
        carrier_code
        carrier_title
        error_message
        method_code
        method_title
        price_excl_tax {
          value
          currency
        }
        price_incl_tax {
          value
          currency
        }
      }
      selected_shipping_method {
        amount {
          value
          currency
        }
        carrier_code
        carrier_title
        method_code
        method_title
      }
    }
    sellerItems {
        sellerDetails {
            name
            email_info
            address
            phone
            shipping_information {
                service_name
                price {
                    currency
                    value
                }
                total_lead_time
                total_weight
            }
        }
        items {
            items {
                prices {
                    discounts {
                amount {
                value
                }
                label
            }
                }
            id
            product {
                name
                sku
            }
            quantity
            errors {
                code
                message
            }
            }
            page_info {
                total_pages
            }
            total_count
        }
    }
    available_payment_methods {
      code
      title
    }
    selected_payment_method {
      code
      title
    }
    applied_coupons {
      code
    }
    prices {
        subtotal_including_tax {
            value
            currency
        }
        subtotal_excluding_tax {
            value
            currency
        }
        grand_total {
            value
            currency
        }
        discounts {
        amount {
          value
        }
        label
      }
      grand_total {
        value
        currency
      }
    }
  }
}
```
