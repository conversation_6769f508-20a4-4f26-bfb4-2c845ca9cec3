<?php
namespace Comave\Sportsclub\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;

class Revenuefilter extends Action
{
    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var OrderCollectionFactory
     */
    protected $orderCollectionFactory;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $customerSession;

    /**
     * Revenuefilter constructor.
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param \Magento\Customer\Model\Customer $customer
     * @param \Magento\Customer\Model\Session $customerSession
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        OrderCollectionFactory $orderCollectionFactory,
        \Magento\Customer\Model\Customer $customer,
        \Magento\Customer\Model\Session $customerSession
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->customer = $customer;
        $this->customerSession = $customerSession;
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute()
    {
        // Initialize revenue data
        $revenueData = ['error' => __('Invalid filter specified.')];

        try {
            $filter = $this->getRequest()->getParam('filter');
            $startDate = $this->getRequest()->getParam('start_date');
            $endDate = $this->getRequest()->getParam('end_date');

            // Calculate difference in days
            $startDateTime = new \DateTime($startDate);
            $endDateTime = new \DateTime($endDate);
            $interval = $startDateTime->diff($endDateTime);
            $differenceInDays = $interval->days;

            // Handle different filters
            switch ($filter) {
                case 'yearly':
                    $revenueData = $this->getYearlyData();
                    break;
                case 'monthly':
                    $revenueData = $this->getMonthlyData();
                    break;
                case 'weekly':
                    $revenueData = $this->getWeeklyData();
                    break;
                case 'daily':
                    if ($differenceInDays > 30) {
                        // If difference between start and end date is more than 30 days, switch to weekly data
                        $revenueData = $this->getWeeklyData();
                    } else {
                        $revenueData = $this->getDailyData();
                    }
                    break;
                default:
                    // If no filter is specified, return an error
                    $revenueData = ['error' => __('Invalid filter specified.')];
                    break;
            }
        } catch (LocalizedException $e) {
            $revenueData['error'] = $e->getMessage();
        } catch (\Exception $e) {
            $revenueData['error'] = __('An error occurred while processing your request. Please try again later.');
        }

        // Return revenue data as JSON
        return $this->resultJsonFactory->create()->setData($revenueData);
    }


    /**
     * Retrieve daily revenue data
     *
     * @return array
     */
    public function getDailyData()
    {
        // Handle daily filter
        $startDate = $this->getRequest()->getParam('start_date');
        $endDate = $this->getRequest()->getParam('end_date');

        // Append timestamp to start and end dates
        $startDate .= " 00:00:00"; // Start of the day
        $endDate .= " 23:59:59"; // End of the day

        // Get order collection based on provided filters
        $orderCollection = $this->orderCollectionFactory->create();

        // Get club identifier for the current customer
        $clubIdentifier = $this->getCustClubIdentifier();

        // Add filters to check club name
        $orderCollection->addFieldToFilter('club_name', $clubIdentifier);
        $orderCollection->addFieldToFilter('created_at', ['from' => $startDate, 'to' => $endDate]);

        // Initialize array to store revenue data
        $revenueData = ['labels' => [], 'comaveData' => []];

        // Create an array to hold daily commission data
        $dailyCommission = [];

        // Get all dates within the date range
        $allDates = $this->getAllDates($startDate, $endDate);

        // Initialize the daily commission array with 0 commission for all dates
        foreach ($allDates as $date) {
            $dailyCommission[$date] = 0;
        }

        // Calculate total commission for each day
        foreach ($orderCollection as $order) {
            $orderDate = date('Y-m-d', strtotime($order->getCreatedAt()));
            $clubCommission = $order->getClubCommission();
            $dailyCommission[$orderDate] += $clubCommission;
        }

        // Generate labels and data based on time interval
        foreach ($dailyCommission as $date => $commission) {
            // Modify the label to include only date and month
            $formattedDate = date('M d', strtotime($date));
            $revenueData['labels'][] = $formattedDate;
            $revenueData['comaveData'][] = $commission;
        }

        return $revenueData;
    }


    /**
     * Retrieve weekly revenue data
     *
     * @return array
     */
    public function getWeeklyData()
    {
        // Handle weekly filter
        $startDate = $this->getRequest()->getParam('start_date');
        $endDate = $this->getRequest()->getParam('end_date');

        // Append timestamp to start and end dates
        $startDate .= " 00:00:00"; // Start of the day
        $endDate .= " 23:59:59"; // End of the day

        // Get order collection based on provided filters
        $orderCollection = $this->orderCollectionFactory->create();

        // Get club identifier for the current customer
        $clubIdentifier = $this->getCustClubIdentifier();

        // Add filters to check club name
        $orderCollection->addFieldToFilter('club_name', $clubIdentifier);
        $orderCollection->addFieldToFilter('created_at', ['from' => $startDate, 'to' => $endDate]);

        // Initialize arrays to store revenue data
        $labels = [];
        $comaveData = [];

        // Create an array to hold commission data for each week
        $weeklyCommissions = [];

        foreach ($orderCollection as $order) {
            // Calculate the week number for the order date
            $weekStartDate = date('Y-m-d', strtotime('monday this week', strtotime($order->getCreatedAt())));
            $weekEndDate = date('Y-m-d', strtotime('sunday this week', strtotime($order->getCreatedAt())));
            $clubCommission = $order->getClubCommission();

            // Store the commission for this week
            $weeklyCommissions[$weekStartDate][$weekEndDate] = isset($weeklyCommissions[$weekStartDate][$weekEndDate]) ? $weeklyCommissions[$weekStartDate][$weekEndDate] + $clubCommission : $clubCommission;
        }

        // Generate labels and data based on time interval
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $startOfWeek = date('Y-m-d', strtotime('monday this week', strtotime($currentDate)));
            $endOfWeek = date('Y-m-d', strtotime('sunday this week', strtotime($currentDate)));

            $startOfWeeknew = date('M d', strtotime('monday this week', strtotime($currentDate)));
            $endOfWeeknew = date('M d', strtotime('sunday this week', strtotime($currentDate)));

            // Use the start date and end date of the week as the label
            $labels[] = $startOfWeeknew . '-' . $endOfWeeknew;


            // If commission data exists for this week, use it, otherwise, use 0
            $comaveData[] = isset($weeklyCommissions[$startOfWeek][$endOfWeek]) ? $weeklyCommissions[$startOfWeek][$endOfWeek] : 0;

            // Move to the next week
            $currentDate = date('Y-m-d', strtotime($endOfWeek . ' +1 day'));
        }

        // Prepare the revenue data array
        $revenueData = [
            'labels' => $labels,
            'comaveData' => $comaveData,
        ];

        return $revenueData;
    }


    /**
     * Retrieve all dates within the specified date range
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    protected function getAllDates($startDate, $endDate)
    {
        $allDates = [];
        $currentDate = strtotime($startDate);
        $endDate = strtotime($endDate);
        while ($currentDate <= $endDate) {
            $allDates[] = date('Y-m-d', $currentDate);
            $currentDate = strtotime('+1 day', $currentDate);
        }
        return $allDates;
    }

    /**
     * Retrieve yearly revenue data
     *
     * @return array
     */
    protected function getYearlyData()
    {
        // Handle yearly filter
        $startDate = $this->getRequest()->getParam('start_date');
        $endDate = $this->getRequest()->getParam('end_date');

        // Append timestamp to start and end dates
        $startDate .= " 00:00:00"; // Start of the day
        $endDate .= " 23:59:59"; // End of the day

        // Get club identifier for the current customer
        $clubIdentifier = $this->getCustClubIdentifier();

        // Initialize arrays to store revenue data
        $labels = [];
        $comaveData = [];

        // Get the start and end years from the provided date range
        $startYear = date('Y', strtotime($startDate));
        $endYear = date('Y', strtotime($endDate));

        // Iterate through each year in the specified date range
        for ($year = $startYear; $year <= $endYear; $year++) {
            // Set the start and end dates for the current year
            $startOfYear = date('Y-m-01 00:00:00', strtotime("$year-01-01"));
            $endOfYear = date('Y-m-t 23:59:59', strtotime("$year-12-31"));

            // Adjust start and end dates if they are beyond the selected range
            if ($startOfYear < $startDate) {
                $startOfYear = $startDate;
            }
            if ($endOfYear > $endDate) {
                $endOfYear = $endDate;
            }

            // Get order collection based on provided filters
            $orderCollection = $this->orderCollectionFactory->create();
            $orderCollection->addFieldToFilter('club_name', $clubIdentifier);
            $orderCollection->addFieldToFilter('created_at', ['from' => $startOfYear, 'to' => $endOfYear]);

            // Calculate revenue for the year
            $totalRevenue = 0;
            foreach ($orderCollection as $order) {
                $totalRevenue += $order->getClubCommission();
            }

            // Store revenue data for the year
            $labels[] = $year;
            $comaveData[] = $totalRevenue;
        }

        // Prepare the revenue data array
        $revenueData = [
            'labels' => $labels,
            'comaveData' => $comaveData,
        ];

        return $revenueData;
    }



    /**
     * Retrieve monthly revenue data
     *
     * @return array
     */
    protected function getMonthlyData()
    {
        // Handle monthly filter
        $startDate = $this->getRequest()->getParam('start_date');
        $endDate = $this->getRequest()->getParam('end_date');

        // Append timestamp to start and end dates
        $startDate .= " 00:00:00"; // Start of the day
        $endDate .= " 23:59:59"; // End of the day

        // Get club identifier for the current customer
        $clubIdentifier = $this->getCustClubIdentifier();

        // Initialize arrays to store revenue data
        $labels = [];
        $comaveData = [];

        // Iterate through each month in the specified date range
        $currentDate = strtotime($startDate);
        $endMonthYear = date('Y-m', strtotime($endDate));
        while (date('Y-m', $currentDate) <= $endMonthYear) {
            $startOfMonth = date('Y-m-01', $currentDate);
            $endOfMonth = date('Y-m-t', strtotime($startOfMonth));

            // Append timestamp to start and end dates
            $startOfMonth .= " 00:00:00"; // Start of the day
            $endOfMonth .= " 23:59:59"; // End of the day

            // Get order collection based on provided filters
            $orderCollection = $this->orderCollectionFactory->create();
            $orderCollection->addFieldToFilter('club_name', $clubIdentifier);
            $orderCollection->addFieldToFilter('created_at', ['from' => $startOfMonth, 'to' => $endOfMonth]);

            // Calculate revenue for the month
            $totalRevenue = 0;
            foreach ($orderCollection as $order) {
                $totalRevenue += $order->getClubCommission();
            }

            // Store revenue data for the month
            $labels[] = date('M Y', $currentDate);
            $comaveData[] = $totalRevenue;

            // Move to the next month
            $currentDate = strtotime('+1 month', $currentDate);
        }

        // Prepare the revenue data array
        $revenueData = [
            'labels' => $labels,
            'comaveData' => $comaveData
        ];

        return $revenueData;
    }


    /**
     * Get club identifier for the current customer
     *
     * @return string|null
     */
    protected function getCustClubIdentifier()
    {
        $clubIdentifier = null;
        $customerId = $this->getCustomerId();
        if ($customerId) {
            $currentCustomer = $this->customer->load($customerId);
            $clubIdentifier = $currentCustomer->getWkv_club_unique_identfier();
        }
        return $clubIdentifier;
    }

    /**
     * Get customer id
     *
     * @return int|null
     */
    protected function getCustomerId()
    {
        return $this->customerSession->getCustomerId();
    }
}
