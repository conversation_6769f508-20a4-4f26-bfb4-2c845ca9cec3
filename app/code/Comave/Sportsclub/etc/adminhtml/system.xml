<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../Config/etc/system_file.xsd">
    <system>
        <tab id="coditron" translate="label" sortOrder="1">
            <label>ComAve Clubs</label>
        </tab>
        <section id="comavesportclub" translate="label" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Sport Club</label>
            <tab>coditron</tab>
            <resource>Comave_Club::config_club</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enable" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="enable_club" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled Club slider</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                     <comment>Enable club product slider only</comment>
                </field>
                <field id="disableCat" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled/Disable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Disable Sponsor and local product carousel from sport club page previous</comment>
                </field>
                <field id="localproductseller" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled/Disable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Allow retails seller to display all products under localproducts</comment>
                </field>
                <field id="sponserproductseller" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled/Disable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Allow retails seller to display all products under Sponsor Products</comment>
                </field>
            </group>
        </section>
    </system>
</config>