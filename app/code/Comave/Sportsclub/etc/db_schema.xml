<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="assigned_seller_to_group" resource="default" engine="innodb" comment="Assigned Seller to Group Table">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="int" name="seller_id" unsigned="true" nullable="false" identity="false" comment="Seller ID"/>
        <column xsi:type="int" name="club_id" unsigned="true" nullable="false" identity="false" comment="Club ID"/>
        <column xsi:type="int" name="local_seller" unsigned="true" nullable="false" identity="false"
                comment="Local Seller"/>
        <column xsi:type="int" name="sponsor_seller" unsigned="true" nullable="false" identity="false"
                comment="Sponsor Seller"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
    </table>
</schema>
