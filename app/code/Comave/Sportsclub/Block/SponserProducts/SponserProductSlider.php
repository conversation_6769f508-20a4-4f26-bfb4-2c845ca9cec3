<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Comave\Sportsclub\Block\SponserProducts;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Catalog\Model\ResourceModel\Product\Collection as ProductCollection;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Webkul\Marketplace\Model\ProductFactory as MpProductModel;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as MpProductCollection;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory as SellerCollectionFactory;
use Comave\Sportsclub\Model\AssignedSellerFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Model\ResourceModel\Stock\ItemFactory;

/**
 * Seller Product's Collection Block.
 */
class SponserProductSlider extends \Magento\Catalog\Block\Product\ListProduct
{

    /**
     * @var \Magento\Eav\Model\ResourceModel\Entity\Attribute
     */
    protected $eavAttribute;

    /**
     * @var MpProductCollection
     */
    protected $mpProductCollection;

    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    protected $productFactory;

    protected $assignedSeller;

    /**
     * @var CollectionFactory
     */
    protected $_productCollectionFactory;

    /**
     * @var \Magento\Catalog\Model\Product
     */
    protected $_productlists;

    /**
     * @var CategoryRepositoryInterface
     */
    protected $_categoryRepository;

    /**
     * @var \Magento\Framework\Stdlib\StringUtils
     */
    protected $stringUtils;

    /**
     * @var MpHelper
     */
    protected $mpHelper;

    /**
     * @var MpProductModel
     */
    protected $mpProductModel;

    protected $productCollectionFactory;

    protected $_sellerlistCollectionFactory;

    protected $sellerCollection;

    protected $sellerList;
     protected $stockItemFactory;

    protected $_productType;

    protected $_sliderConfiguration;

    protected $_helperProducts;
    protected $productRepository;

    /**
     * @param \Magento\Catalog\Block\Product\Context    $context
     * @param \Magento\Framework\Data\Helper\PostHelper $postDataHelper
     * @param \Magento\Framework\Url\Helper\Data        $urlHelper
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param CollectionFactory                         $productCollectionFactory
     * @param \Magento\Catalog\Model\Layer\Resolver     $layerResolver
     * @param CategoryRepositoryInterface               $categoryRepository
     * @param \Magento\Framework\Stdlib\StringUtils     $stringUtils
     * @param MpHelper                                  $mpHelper
     * @param MpProductModel                            $mpProductModel
     * @param array                                     $data
     */
    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\Data\Helper\PostHelper $postDataHelper,
        \Comave\Sportsclub\Helper\Products $helperProducts,
        private readonly \Comave\Sportsclub\Helper\Data $DataProducts,
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerlistCollectionFactory,
        \Magento\Eav\Model\ResourceModel\Entity\Attribute $eavAttribute = null,
        \Magento\Catalog\Model\ProductFactory $productFactory = null,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        CollectionFactory $_productCollectionFactory,
        SellerCollectionFactory $sellerCollectionFactory = null,
        MpProductCollection $mpProductCollection = null,
        \Magento\Catalog\Model\Layer\Resolver $layerResolver,
        CategoryRepositoryInterface $categoryRepository,
        AssignedSellerFactory $assignedSeller,
        \Magento\Framework\Stdlib\StringUtils $stringUtils = null,
        MpHelper $mpHelper = null,
        MpProductModel $mpProductModel = null,
        \Magento\Framework\App\Http\Context $httpContext,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        ProductRepositoryInterface $productRepository,
         ItemFactory $stockItemFactory,
        array $data = []
    ) {
        $this->_sellerlistCollectionFactory = $sellerlistCollectionFactory;
        $this->stockItemFactory = $stockItemFactory;
        $this->_productCollectionFactory = $_productCollectionFactory;
        $this->assignedSeller = $assignedSeller;
        $this->_helperProducts = $helperProducts;
        $this->_categoryRepository = $categoryRepository;
        $this->eavAttribute = $eavAttribute ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Magento\Eav\Model\ResourceModel\Entity\Attribute::class);
        $this->mpProductCollection = $mpProductCollection ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpProductCollection::class);
        $this->productFactory = $productFactory ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Magento\Catalog\Model\ProductFactory::class);
        $this->stringUtils = $stringUtils ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Magento\Framework\Stdlib\StringUtils::class);
        $this->mpHelper = $mpHelper ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpHelper::class);
        $this->mpProductModel = $mpProductModel ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpProductModel::class);
        $this->sellerCollection = $sellerCollectionFactory ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(CollectionFactory::class);
        $this->httpContext = $httpContext;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_customerFactory = $customerFactory;
        $this->productRepository = $productRepository;

        parent::__construct(
            $context,
            $postDataHelper,
            $layerResolver,
            $categoryRepository,
            $urlHelper,
            $data
        );
    }

    public function isCustomerLoggedIn()
    {
        return (bool)$this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    public function getCustId()
    {
        return $this->httpContext->getValue('customer_id');
    }

    // Get current customer associated club identifier

    public function getCustClubIdentifier()
    {
        $clubIdentifier = false;

        if($this->isCustomerLoggedIn()){
            $customerId = $this->getCustId();
            $currentCustomer = $this->_customerRepositoryInterface->getById($customerId);

            if ($currentCustomer) {
                if($currentCustomer->getCustomAttribute('customerclub')){
                    $clubIdentifier = $currentCustomer->getCustomAttribute('customerclub')->getValue();
                }
                return $clubIdentifier;
            }
        }

        return $clubIdentifier;

    }

    // Get current customer associated club data
    public function getCustomerCollection()
    {
            $sellerid = ' ';

            $custclubIdentifier = $this->getCustClubIdentifier();
            $customerCollection = $this->_customerFactory->create()->getCollection()
                    ->addAttributeToSelect("entity_id")
                    ->addAttributeToSelect("email")
                    ->addFieldToFilter(
                        'wkv_club_unique_identfier',
                        ['eq' => $custclubIdentifier]
                    )
                    ->load();
            foreach ($customerCollection as $customer) {

                $sellerid = $customer['entity_id'];
            }

        return $sellerid;
    }

    public function getAssignedSellerIds()
    {
        $assignedSellerIDs = [];
        $clubId  = $this->getCustomerCollection();
        $assignedSellers = $this->assignedSeller->create()->getCollection()
            ->addFieldToFilter('club_id', $clubId)
            ->addFieldToFilter('sponsor_seller', '1');

        foreach ($assignedSellers as $sellerid) {
            $assignedSellerIDs[] = $sellerid['seller_id'];
        }
        return $assignedSellerIDs;
    }

    public function getClubCustomerCollection()
    {
        $customerCollection = false;
        if($this->isCustomerLoggedIn()){
            $custclubIdentifier = $this->getCustClubIdentifier();
            $customerCollection = $this->_customerFactory->create()->getCollection()
                    ->addAttributeToSelect("*")
                    ->addFieldToFilter(
                        'wkv_club_unique_identfier',
                        ['neq' => $custclubIdentifier]
                    )
                    ->load();
           return $customerCollection;
        }

    }

   /**
     * Get all products
     *
     * @return bool|\Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function _getProductCollection()
    {
       $sellerIds = $this->getAssignedSellerIds();
        $onlySelectedProducts = $this->DataProducts->sponserproductseller();
        $collection = $this->_productCollectionFactory->create();
        $collection->addFieldToSelect('*');
        $collection->addFieldToFilter('visibility', ['neq' => \Magento\Catalog\Model\Product\Visibility::VISIBILITY_NOT_VISIBLE]);
        $collection->joinField(
                    'is_in_stock',
                    'cataloginventory_stock_item',
                    'is_in_stock',
                    'product_id=entity_id',
                    '{{table}}.stock_id=1',
                    'left'
                )->addAttributeToFilter(
                    'is_in_stock',
                    ['eq' => 1]
                );
        if (!empty($sellerIds)) {
            $mageproductids = [];
            $mpcollection = $this->mpProductCollection->create();
            $mpcollection->addFieldToFilter('seller_id', ['in' => $sellerIds]);

            foreach ($mpcollection as $product) {
                $mageproductids[] = $product->getData('mageproduct_id');
            }
            if (!empty($mageproductids)) {
                $collection->addFieldToFilter('entity_id', ['in' => $mageproductids]);
               if(!$onlySelectedProducts){
                 $collection->addFieldToFilter('sponsorproduct', ['eq' => 1]);
               }
            }else {
                $collection->addFieldToFilter('entity_id', ['eq' => 0]);
            }
        }else {
           $collection->addFieldToFilter('entity_id', ['eq' => 0]);
        }

        return $collection;
    }

    public function getSellerCollection()
    {

        $sellerId = $this->getCustomerCollection();
        if (!$this->sellerList) {
            $collection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerId]
                )->setOrder(
                    'entity_id',
                    'desc'
                );
            $this->sellerList = $collection;
        }

        return $this->sellerList;
    }
}