<?php
/**
 * Coditron
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Coditron.com license that is
 * available through the world-wide-web at this URL:
 * https://www.coditron.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category   Coditron
 * @package    Comave_Club
 * @copyright  Copyright (c) 2023 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Comave\Sportsclub\Helper;

use AllowDynamicProperties;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Comave\Club\Model\Group;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\Request\Http;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;
use Webkul\MpVendorAttributeManager\Model\VendorGroupFactory;

// Assuming this is the correct namespace for the Marketplace Helper

#[AllowDynamicProperties]
class Data extends AbstractHelper
{
    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var Group
     */
    protected $groupCollection;

    /**
     * @var FilterProvider
     */
    protected $filterProvider;

    /**
     * @var Http
     */
    protected $request;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var MpHelper
     */
    protected $mpHelper;
    protected $sellerlistCollectionFactory;
    protected $customerFactory;
    protected $vendorGroupFactory;
    protected $httpContext;

    /**
     * Constructor
     *
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param Group $groupCollection
     * @param FilterProvider $filterProvider
     * @param MpHelper $mpHelper
     * @param Http $request
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        Context $context,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Webkul\MpVendorAttributeManager\Model\VendorGroupFactory $vendorGroupFactory,
        \Magento\Framework\App\Http\Context $httpContext,
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerlistCollectionFactory,
        StoreManagerInterface $storeManager,
        Group $groupCollection,
        FilterProvider $filterProvider,
        MpHelper $mpHelper,
        Http $request,
        ScopeConfigInterface $scopeConfig
    ) {
        parent::__construct($context);
        $this->sellerlistCollectionFactory = $sellerlistCollectionFactory;
        $this->httpContext = $httpContext;
        $this->customerFactory = $customerFactory;
        $this->vendorGroupFactory = $vendorGroupFactory;
        $this->storeManager = $storeManager;
        $this->groupCollection = $groupCollection;
        $this->filterProvider = $filterProvider;
        $this->mpHelper = $mpHelper; // Corrected variable name to match property name
        $this->request = $request;
        $this->scopeConfig = $scopeConfig;
    }

    public function localproductseller()
    {
        $path = "comavesportclub/general/localproductseller";
        $scope = ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue($path, $scope);
    }

    public function sponserproductseller()
    {
        $path = "comavesportclub/general/sponserproductseller";
        $scope = ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue($path, $scope);
    }

    /**
     * Get group list
     *
     * @return array
     */
    public function getGroupList()
    {
        $result = [];
        $collection = $this->groupCollection->getCollection()->addFieldToFilter('status', '1');
        foreach ($collection as $clubGroup) {
            $result[$clubGroup->getId()] = $clubGroup->getName();
        }
        return $result;
    }

    /**
     * Give the current url of recently viewed page.
     *
     * @return string
     */
    public function getCurrentUrl()
    {
        return $this->_urlBuilder->getCurrentUrl();
    }

    /**
     * Return the Customer seller status.
     *
     * @return bool|0|1
     */
    public function isSeller()
    {
        $sellerStatus = 0;
        $sellerId = $this->getCustId();
        $model = $this->getSellerCollectionObj($sellerId);
        foreach ($model as $value) {
            if ($value->getIsSeller() == 1) {
                $sellerStatus = $value->getIsSeller();
            }
        }

        return $sellerStatus;
    }

    /**
     * Return the Seller Model Collection Object.
     *
     * @param int $sellerId
     * @return \Webkul\Marketplace\Model\ResourceModel\Seller\Collection
     */
    public function getSellerCollectionObj($sellerId)
    {
        $collection = $this->getSellerCollections();
        $collection->addFieldToFilter('seller_id', $sellerId);
        // If seller data doesn't exist for current store

        if (!$collection->getSize()) {
            $collection = $this->getSellerCollections();
            $collection->addFieldToFilter('seller_id', $sellerId);
        }

        return $collection;
    }

    /**
     * Get Seller Collection
     *
     * @return \Webkul\Marketplace\Model\ResourceModel\Seller\Collection
     */
    public function getSellerCollections()
    {
        return $this->sellerlistCollectionFactory->create();
    }

    /**
     * Get Is Separate Seller Allow
     *
     * @return boolean
     */
    public function getSeparatePanelStatus()
    {
        return $this->mpHelper->getIsSeparatePanel();
    }

    /**
     * Get module status
     *
     * @return int
     */
    public function getModuleStatus()
    {
        $path = "comavesportclub/general/enable";
        $scope = ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue($path, $scope);
    }

    public function getClubsliderStatus()
    {
        $path = "comavesportclub/general/enable_club";
        $scope = ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue($path, $scope);
    }
    /**
     * Get disables the carosel from sport club page.
     *
     * @return int
     */
    public function getDisableStatus()
    {
        $path = "comavesportclub/general/disableCat";
        $scope = ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue($path, $scope);
    }

    /**
     * Get configuration value by key and store
     *
     * @param string $key
     * @param mixed $store
     * @return mixed
     */
    public function getConfig($key, $store = null)
    {
        $store = $this->storeManager->getStore($store);
        return $this->scopeConfig->getValue(
            'coditronclub/'.$key,
            ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    /**
     * Filter HTML content
     *
     * @param string $str
     * @return string
     */
    public function filter($str)
    {
        return $this->filterProvider->getPageFilter()->filter($str);
    }

    /**
     * Get search form URL
     *
     * @return string
     */
    public function getSearchFormUrl()
    {
        $url = $this->storeManager->getStore()->getBaseUrl();
        $urlPrefix = $this->getConfig('general_settings/url_prefix');
        if ($urlPrefix) {
            $url = rtrim($url, '/') . '/' . ltrim($urlPrefix, '/') . '/';
        }
        return $url . 'search';
    }

    /**
     * Get search key from request
     *
     * @return string|null
     */
    public function getSearchKey()
    {
        return $this->request->getParam('s');
    }

    /**
     * Get base URL
     *
     * @param null|string|int|\Magento\Store\Model\Store $store
     * @return string
     */
    public function getBaseUrl($store = null)
    {
        return $this->storeManager->getStore($store)->getBaseUrl();
    }

    /**
     * Get media URL
     *
     * @return string
     */
    public function getMediaUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
    }

    /**
     * Check if customer is logged in
     *
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return (bool)$this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    /**
     * Get current customer ID
     *
     * @return int|null
     */
    public function getCustId()
    {
        return $this->httpContext->getValue('customer_id');
    }
}
