<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\MarketplaceApi\Api\OrderCommentInterface" type="Comave\MarketplaceApi\Model\OrderComment" />
    <preference for="Comave\MarketplaceApi\Api\ShipmentInterface" type="Comave\MarketplaceApi\Model\Shipment" />
    <preference for="Comave\MarketplaceApi\Api\ProductAttributeManagerInterface" type="Comave\MarketplaceApi\Model\ProductAttributeManager" />
    <preference for="Comave\MarketplaceApi\Api\SourceItemsSaveWithResponseInterface" type="Comave\MarketplaceApi\Model\SourceItemsSaveWithResponse" />
    <preference for="Comave\MarketplaceApi\Api\ProductStockUpdateWithResponseInterface" type="Comave\MarketplaceApi\Model\ProductStockUpdateWithResponse" />
    <type name="Comave\MarketplaceApi\Api\OrderCommentInterface">
        <arguments>
            <argument name="class" xsi:type="string">Comave\MarketplaceApi\Model\OrderComment</argument>
        </arguments>
    </type>
    <type name="Comave\MarketplaceApi\Api\ShipmentInterface">
        <arguments>
            <argument name="class" xsi:type="string">Comave\MarketplaceApi\Model\Shipment</argument>
        </arguments>
    </type>
</config>
