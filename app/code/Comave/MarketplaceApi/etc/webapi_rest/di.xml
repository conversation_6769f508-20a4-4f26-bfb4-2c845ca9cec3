<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogInventory\Model\StockRegistry">
        <plugin name="comave_seller_stock_registry_plugin"
                type="Comave\MarketplaceApi\Plugin\StockRegistry"
                sortOrder="1"/>
    </type>
    <type name="Magento\Inventory\Model\SourceItem\Command\SourceItemsSave">
        <plugin name="comave_seller_source_item_save_plugin"
                type="Comave\MarketplaceApi\Plugin\SourceItemsSave"
                sortOrder="1"/>
    </type>
    <type name="Webkul\MpApi\Model\Seller\SellerManagement">
        <plugin name="rest-viewshipment-mask-email"
                type="Comave\MarketplaceApi\Plugin\MaskedEmailViewShipment"
                sortOrder="1"/>
        <plugin name="rest-ceditmemoPlugin-mask-email"
                type="Comave\MarketplaceApi\Plugin\MaskedEmailViewCreditmemo"
                sortOrder="1"/>
    </type>
</config>
