<?php
declare(strict_types=1);

namespace Comave\MarketplaceApi\Model;

use Comave\MarketplaceApi\Api\OrderCommentInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\Status\HistoryFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\AuthorizationException;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Webkul\Marketplace\Helper\Data as HelperMarketplace;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory as StatusCollectionFactory;
use Comave\MapOrderStatuses\Service\OrderStatuses;
use Comave\MapOrderStatuses\Model\ConfigProvider;

class OrderComment implements OrderCommentInterface
{
    private const FINAL_STATES = [
        \Magento\Sales\Model\Order::STATE_COMPLETE,
        \Magento\Sales\Model\Order::STATE_CLOSED,
        \Magento\Sales\Model\Order::STATE_CANCELED,
    ];

    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly HistoryFactory $historyFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly HelperMarketplace $helperMarketplace,
        private readonly StatusCollectionFactory $statusCollection,
        private readonly TokenManager $tokenManager,
        private readonly OrderStatuses $orderStatuses,
        private readonly ConfigProvider $orderStatusConfigProvider,
    ) {
    }

    /**
     * @param int $orderId
     * @param string $comment
     * @param string|null $status
     * @return string
     * @throws AuthorizationException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    final public function addComment(int $orderId, string $comment, ?string $status = null): string
    {
        $token = $this->tokenManager->extractTokenFromHeader();
        $sellerId = $this->tokenManager->validateAndRetrieveSellerId($token);
        if (!$sellerId) {
            throw new \Magento\Framework\Exception\InputException(__('Invalid seller ID.'));
        }

        $order = $this->retrieveOrder($orderId);

        $seller = $this->getSellerById($sellerId);
        $formattedComment = $this->formatComment($seller, $comment);

        $this->addOrderHistory($order, $formattedComment);

        if (!empty($status)) {
            $this->validateAndUpdateOrderStatus($order, $status);
        }

        return 'Order updated successfully.';
    }

    /**
     * @param int $orderId
     * @return \Magento\Sales\Api\Data\OrderInterface
     * @throws NoSuchEntityException
     */
    private function retrieveOrder(int $orderId): \Magento\Sales\Api\Data\OrderInterface
    {
        try {
            $storeId = (int) $this->storeManager->getStore()->getId();
            return $this->orderRepository->get($orderId, $storeId);
        } catch (NoSuchEntityException $e) {
            throw new NoSuchEntityException(__('Order with ID %1 does not exist.', $orderId));
        }
    }

    /**
     * @param int $sellerId
     * @return \Magento\Customer\Api\Data\CustomerInterface
     * @throws AuthorizationException
     * @throws LocalizedException
     */
    private function getSellerById(int $sellerId): \Magento\Customer\Api\Data\CustomerInterface
    {
        try {
            return $this->customerRepository->getById($sellerId);
        } catch (NoSuchEntityException $e) {
            throw new AuthorizationException(__('Seller with ID %1 does not exist.', $sellerId));
        }
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $seller
     * @param string $comment
     * @return string
     */
    private function formatComment(\Magento\Customer\Api\Data\CustomerInterface $seller, string $comment): string
    {
        return sprintf('[%s %s] %s', $seller->getFirstname(), $seller->getLastname(), $comment);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     * @throws LocalizedException
     */
    private function saveOrder(\Magento\Sales\Api\Data\OrderInterface $order): void
    {
        try {
            $this->orderRepository->save($order);
        } catch (LocalizedException $e) {
            throw new LocalizedException(__($e->getMessage()));
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param string $comment
     * @return void
     * @throws LocalizedException
     */
    private function addOrderHistory(\Magento\Sales\Api\Data\OrderInterface $order, string $comment): void
    {
        $history = $this->historyFactory->create()
            ->setParentId($order->getEntityId())
            ->setComment($comment)
            ->setIsVisibleOnFront(false);
        $order->addStatusHistory($history);

        $this->saveOrder($order);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    private function isSingleSellerOrder(\Magento\Sales\Api\Data\OrderInterface $order): bool
    {
        $sellerIds = array_map(
            fn($item) => $this->helperMarketplace->getSellerIdByProductId($item->getProductId()),
            $order->getAllItems()
        );
        return count(array_unique($sellerIds)) === 1;
    }

    /**
     * @param string $status
     * @return bool
     */
    private function isStatusValid(string $status): bool
    {
        $statuses = array_column(
            $this->statusCollection->create()->toOptionArray(),
            'value'
        );

        return in_array($status, $statuses, true);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param string $status
     * @return void
     * @throws LocalizedException
     */
    private function validateAndUpdateOrderStatus(\Magento\Sales\Api\Data\OrderInterface $order, string $status): void
    {
        if ($this->orderStatuses->isStatusActive($status) === false
            || (
                $this->orderStatusConfigProvider->isPredefinedStatusMapped($status) === false
                && $this->orderStatusConfigProvider->isCustomStatusMapped($status) === false)
        ) {
            throw new LocalizedException(
                __('Cannot update order status: The status "%1" is inactive or not allowed.', $status)
            );
        }

        if (in_array($order->getState(), self::FINAL_STATES, true)
            || !$this->isStatusValid($status)
            || !$this->orderStatuses->isStatusActive($status)
        ) {
            throw new LocalizedException(__('Order status can not be changed'));
        }

        if (!$this->isSingleSellerOrder($order)) {
            throw new LocalizedException(__('Order contains products from multiple sellers.'));
        }

        $order->setStatus($status);
        $order->addStatusToHistory($status, __('Status updated to %1 by seller.', $status));
        $this->saveOrder($order);
    }
}
