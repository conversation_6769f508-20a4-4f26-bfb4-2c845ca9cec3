<?php

declare(strict_types=1);

namespace Comave\SellerStatus\ViewModel;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Webkul\Marketplace\Helper\Data;

class SellerStatus implements ArgumentInterface
{
    /**
     * @param Data $marketplaceHelper
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     */
    public function __construct(
        private readonly Data $marketplaceHelper,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider
    ) {
    }

    /**
     * @return string|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSellerStatus(): ?string
    {
        if (!$this->marketplaceHelper->isSeller()) {
            return null;
        }

        $sellerCompany = $this->sellerCompanyProvider->get();
        $userRole = current(
            $this->companyUserRoleManagement->getRolesForCompanyUser(
                (int) $this->marketplaceHelper->getCustomerId(),
                (int) $sellerCompany->getId()
            )
        );

        return $userRole ? $userRole->getRoleName() : null;
    }
}
