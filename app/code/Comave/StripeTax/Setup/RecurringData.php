<?php

declare(strict_types=1);

namespace Comave\StripeTax\Setup;

use Comave\StripeTax\Model\Source\StripeTaxes;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Tax\Setup\Patch\Data\AddTaxAttributeAndTaxClasses;

class RecurringData implements InstallDataInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param StripeTaxes $stripeTaxes
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly StripeTaxes $stripeTaxes
    ) {
    }

    /**
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [
            AddTaxAttributeAndTaxClasses::class
        ];
    }

    /**
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     * @throws \Exception
     */
    public function apply(): self
    {
        $stripeTaxes = $this->stripeTaxes->toOptionArray();
        $taxId = false;
        $taxName = false;

        foreach ($stripeTaxes as $taxCode) {
            if ($taxCode['value'] !== StripeTaxes::STRIPE_DEFAULT_TAX_ID) {
                continue;
            }

            $taxId = $taxCode['value'];
            $taxName = $taxCode['label'];
            break;
        }

        if (!$taxId || !$taxName) {
            throw new LocalizedException(__('Unable to determine default tax rate from stripe'));
        }

        $connection = $this->moduleDataSetup->getConnection();
        $connection->update(
            $connection->getTableName('tax_class'),
            [
                'stripe_product_tax_code_name' => $taxName,
                'stripe_product_tax_code' => $taxId
            ],
            [
                'class_type = ?' => \Magento\Tax\Model\ClassModel::TAX_CLASS_TYPE_PRODUCT,
                'class_name = ?' => 'Taxable Goods'
            ]
        );

        return $this;
    }

    /**
     * Installs data for a module
     *
     * @param ModuleDataSetupInterface $setup
     * @param \Magento\Framework\Setup\ModuleContextInterface $context
     * @return void
     */
    public function install(ModuleDataSetupInterface $setup, \Magento\Framework\Setup\ModuleContextInterface $context): void
    {
        try {
            $this->apply();
        } catch (\Exception) {}
    }
}
