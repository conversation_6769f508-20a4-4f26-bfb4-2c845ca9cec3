<?php

declare(strict_types=1);

namespace Comave\StripeTax\Observer;

use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Comave\StripeTax\Model\Queue\Consumer\ProcessTaxClasses;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Event\Observer;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Model\AbstractModel;

class SaveTaxClass implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param PublisherInterface $publisher
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly PublisherInterface $publisher,
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var AbstractModel $entity */
        $entity = $observer->getEntity();

        if (empty($entity->getData('tax_class'))) {
            return;
        }

        $connection = $this->resourceConnection->getConnection('write');
        $connection->update(
            $connection->getTableName('comave_seller_onboarding_category_mapping'),
            [
                'tax_class' => $entity->getData('tax_class')
            ],
            [
                MappingInterface::MAPPING_ID . ' = ?' => $entity->getId()
            ]
        );

        $this->publisher->publish(
            ProcessTaxClasses::TOPIC_NAME,
            (string) $entity->getMappingId()
        );
    }
}
