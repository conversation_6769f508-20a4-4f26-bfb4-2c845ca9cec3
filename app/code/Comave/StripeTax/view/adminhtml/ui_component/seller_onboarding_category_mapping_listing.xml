<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="seller_category_mapping_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="seller_onboarding/category_mapping/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">mapping_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">seller_onboarding_category_mapping_listing.seller_onboarding_category_mapping_listing.seller_category_mapping_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">seller_onboarding_category_mapping_listing.seller_onboarding_category_mapping_listing.seller_category_mapping_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <column name="tax_class" component="Magento_Ui/js/grid/columns/select" sortOrder="60">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <options class="Comave\StripeTax\Model\Source\StripeTaxes"/>
                <label translate="true">Mapped Tax Class</label>
                <draggable>false</draggable>
            </settings>
        </column>
    </columns>
</listing>
