<?php

declare(strict_types=1);

namespace Comave\StripeTax\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;
use Stripe\StripeClient;
use StripeIntegration\Tax\Exceptions\Exception;
use StripeIntegration\Tax\Model\Config;

class StripeTaxes implements OptionSourceInterface
{
    public const string STRIPE_DEFAULT_TAX_ID = 'txcd_99999999';

    /**
     * @param StoreManagerInterface $storeManager
     * @param Config $config
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly Config $config,
    ) {
    }

    /**
     * @var string[]|array
     */
    private ?array $stripeTaxes = [];

    /**
     * @return array
     * @throws Exception
     * @throws LocalizedException
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function toOptionArray(): array
    {
        if (empty($this->stripeTaxes)) {
            $this->stripeTaxes[] = [
                'value' => '',
                'label' => __('Choose stripe tax class')
            ];

            try {
                $stripeClient = $this->getStripeClient();

                if (!$stripeClient instanceof StripeClient) {
                    throw new LocalizedException(__('Unable to initiate stripe client'));
                }

                $taxCodes = $stripeClient->taxCodes->all(['limit' => 100]);
                foreach ($taxCodes->autoPagingIterator() as $taxCode) {
                    $this->stripeTaxes[] = [
                        'value' => $taxCode->id,
                        'label' => $taxCode->name,
                    ];
                }
            } catch (\Exception) {//phpcs:ignore
            }
        }

        return $this->stripeTaxes;
    }

    /**
     * @return StripeClient
     * @throws Exception
     */
    private function getStripeClient(): StripeClient
    {
        $defaultStoreView = $this->storeManager->getDefaultStoreView();
        $this->config->reInitStripeFromStore($defaultStoreView);

        if ($this->config->getStripeClient()) {
            return $this->config->getStripeClient();
        }

        throw new Exception("Could not find a store connected to Stripe. Please connect a store to Stripe first.");
    }
}
