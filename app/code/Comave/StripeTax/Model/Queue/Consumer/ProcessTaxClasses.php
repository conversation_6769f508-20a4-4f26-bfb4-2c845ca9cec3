<?php

declare(strict_types=1);

namespace Comave\StripeTax\Model\Queue\Consumer;

use Comave\SellerOnboarding\Api\Category\DataSourceRepositoryInterface;
use Comave\SellerOnboarding\Api\Category\MappingRepositoryInterface;
use Comave\StripeTax\Model\Source\StripeTaxes;
use Magento\Catalog\Model\Product\Action as ProductAction;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\ResourceConnection;
use Magento\Tax\Api\TaxClassRepositoryInterface;
use Magento\Tax\Model\ClassModelFactory;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Helper\Data;

class ProcessTaxClasses
{
    public const string TOPIC_NAME = 'process.category.taxclass';

    /**
     * @param LoggerInterface $logger
     * @param MappingRepositoryInterface $mappingRepository
     * @param DataSourceRepositoryInterface $sourceRepository
     * @param TaxClassRepositoryInterface $taxClassRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param ClassModelFactory $taxClassFactory
     * @param StripeTaxes $stripeTaxes
     * @param Data $marketplaceHelper
     * @param ProductAction $productAction
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly MappingRepositoryInterface $mappingRepository,
        private readonly DataSourceRepositoryInterface $sourceRepository,
        private readonly TaxClassRepositoryInterface $taxClassRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly ClassModelFactory $taxClassFactory,
        private readonly StripeTaxes $stripeTaxes,
        private readonly Data $marketplaceHelper,
        private readonly ProductAction $productAction,
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param string $mappingId
     * @return void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(string $mappingId): void
    {
        $mapping = $this->mappingRepository->get((int) $mappingId);
        $source = $this->sourceRepository->get((int) $mapping->getMappingSourceId());
        $sellerProductIds = $this->marketplaceHelper->getSellerProducts($source->getSellerId(), false);

        if (empty($sellerProductIds)) {
            $this->logger->warning(
                '[Comave.TaxMapping] No products found for seller',
                [
                    'seller' => $source->getSellerId(),
                ]
            );

            return;
        }

        $connection = $this->resourceConnection->getConnection('read');
        $productIdsSelect = $connection->select()
            ->from(
                ['c' => $connection->getTableName('catalog_category_product')],
                [
                    'product_id'
                ]
            )->where(
                'category_id = ?',
                $mapping->getMappingCategoryId()
            )->where(
                'product_id IN (?)',
                $sellerProductIds
            );

        $productIds = $connection->fetchCol($productIdsSelect);

        if (empty($productIds)) {
            $this->logger->warning(
                '[Comave.TaxMapping] No products found in the source category for seller',
                [
                    'seller' => $source->getSellerId(),
                    'source_category' => $source->getSourceCategoryName(),
                    'mapping_category' => $mapping->getMappingCategoryId()
                ]
            );

            return;
        }

        $options = $this->stripeTaxes->toOptionArray();
        $taxKey = array_search(
            $mapping->getTaxClass(),
            array_column($options,'value')
        );

        if (!isset($options[$taxKey])) {
            $this->logger->warning(
                '[Comave.TaxMapping] Stripe tax class no longer available',
                [
                    'seller' => $source->getSellerId(),
                    'source_category' => $source->getSourceCategoryName(),
                    'mapping_category' => $mapping->getMappingCategoryId(),
                    'selectedTaxClass' => $mapping->getTaxClass()
                ]
            );

            return;
        }

        $this->logger->info(
            '[Comave.TaxMapping] Beginning tax class mapping',
            [
                'seller' => $source->getSellerId(),
                'source_category' => $source->getSourceCategoryName(),
                'mapping_category' => $mapping->getMappingCategoryId(),
                'productIds' => $productIds,
                'selectedTaxClass' => $mapping->getTaxClass(),
            ]
        );

        $selectedTaxClass = $options[$taxKey];
        $className = sprintf(
            '%s (%s)',
            $selectedTaxClass['label'],
            $source->getSellerId()
        );
        $this->searchCriteriaBuilder->addFilter('class_name', $className);
        $taxClassList = $this->taxClassRepository->getList(
            $this->searchCriteriaBuilder->create()
        );

        if (!$taxClassList->getTotalCount()) {
            $taxClass = $this->taxClassFactory->create();
            $taxClass->setClassName($className);
            $taxClass->setClassType(\Magento\Tax\Model\ClassModel::TAX_CLASS_TYPE_PRODUCT);
        } else {
            $taxClass = current($taxClassList->getItems());
        }

        $taxClass->setStripeProductTaxCode($selectedTaxClass['value']);
        $taxClass->setStripeProductTaxCodeName($selectedTaxClass['label']);
        $this->taxClassRepository->save($taxClass);

        $this->productAction->updateAttributes(
            $productIds,
            [
                'tax_class_id' => $taxClass->getId()
            ],
            0
        );

        $this->logger->info(
            '[Comave.TaxMapping] Finished tax class mapping',
            [
                'seller' => $source->getSellerId(),
                'source_category' => $source->getSourceCategoryName(),
                'mapping_category' => $mapping->getMappingCategoryId(),
                'productIds' => $productIds,
                'selectedTaxClass' => $mapping->getTaxClass(),
                'taxClassId' => $taxClass->getId()
            ]
        );
    }
}
