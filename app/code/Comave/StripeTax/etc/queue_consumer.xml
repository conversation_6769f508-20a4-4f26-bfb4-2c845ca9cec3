<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="process.category.taxclass"
              queue="process.category.taxclass"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\StripeTax\Model\Queue\Consumer\ProcessTaxClasses::execute"/>
</config>
