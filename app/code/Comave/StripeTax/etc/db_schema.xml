<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_seller_onboarding_category_mapping">
        <column xsi:type="varchar" name="tax_class" nullable="true" length="256" comment="Stripe Tax Class"/>
        <index referenceId="COMAVE_SELLER_ONBOARDING_CATEGORY_MAPPING_TAX_CLASS_ID" indexType="btree">
            <column name="tax_class"/>
        </index>
    </table>
</schema>
