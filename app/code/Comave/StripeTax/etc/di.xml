<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <virtualType name="ComaveStripeTaxMapping" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ComaveStripeTaxMapping</argument>
            <argument name="loggerPath" xsi:type="string">stripe_tax_mapping</argument>
        </arguments>
    </virtualType>

    <type name="Comave\StripeTax\Model\Queue\Consumer\ProcessTaxClasses">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveStripeTaxMapping</argument>
        </arguments>
    </type>
</config>
