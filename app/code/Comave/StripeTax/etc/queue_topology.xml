<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="process.category.taxclass" connection="amqp" type="topic">
        <binding id="sellerProcessTaxClassMappingBinding"
                 topic="process.category.taxclass"
                 destinationType="queue"
                 destination="process.category.taxclass"/>
    </exchange>
</config>
