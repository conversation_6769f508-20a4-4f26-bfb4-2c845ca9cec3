<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    <topic name="process.category.taxclass" request="string">
        <handler name="process.category.taxclass"
                 type="Comave\StripeTax\Model\Queue\Consumer\ProcessTaxClasses"
                 method="execute"/>
    </topic>
</config>
