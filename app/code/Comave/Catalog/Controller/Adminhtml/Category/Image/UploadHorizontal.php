<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Controller\Adminhtml\Category\Image;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Psr\Log\LoggerInterface;

/**
 * Category horizontal image upload controller
 */
class UploadHorizontal extends Action
{
    const ADMIN_RESOURCE = 'Magento_Catalog::categories';
    const UPLOAD_DIR = 'tmp/catalog/category/horizontal';
    const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * @var UploaderFactory
     */
    private $uploaderFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param UploaderFactory $uploaderFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        UploaderFactory $uploaderFactory,
        Filesystem $filesystem,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->uploaderFactory = $uploaderFactory;
        $this->filesystem = $filesystem;
        $this->logger = $logger;
    }

    /**
     * Upload horizontal image action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        try {
            $uploader = $this->uploaderFactory->create(['fileId' => 'horizontal_image']);
            $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(true);

            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $uploadResult = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

            unset($uploadResult['tmp_name']);
            unset($uploadResult['path']);

            $uploadResult['url'] = $this->getMediaUrl($uploadResult['file']);
            $uploadResult['name'] = $uploadResult['file'];

            $result->setData($uploadResult);
        } catch (\Exception $e) {
            $this->logger->critical($e);
            $result->setData([
                'error' => $e->getMessage(),
                'errorcode' => $e->getCode()
            ]);
        }

        return $result;
    }

    /**
     * Get media URL for uploaded file
     *
     * @param string $file
     * @return string
     */
    private function getMediaUrl($file)
    {
        return $this->_url->getBaseUrl(['_type' => \Magento\Framework\UrlInterface::URL_TYPE_MEDIA])
            . self::UPLOAD_DIR . $file;
    }
}
