<?php
declare(strict_types=1);

namespace Comave\Catalog\Controller\Adminhtml\Product\Gallery;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Serialize\Serializer\Json as JsonSerializer;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Psr\Log\LoggerInterface;
use Comave\Catalog\Service\ImageSecurityValidator;

class UploadVertical extends Action implements HttpPostActionInterface
{
    public const ADMIN_RESOURCE = 'Comave_Catalog::vertical_image_upload';

    private const UPLOAD_DIR = 'tmp/catalog/product/vertical';
    private const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];
    private const MAX_FILE_SIZE = 5242880; // 5MB

    public function __construct(
        Context $context,
        private readonly JsonFactory $resultJsonFactory,
        private readonly UploaderFactory $uploaderFactory,
        private readonly Filesystem $filesystem,
        private readonly JsonSerializer $jsonSerializer,
        private readonly LoggerInterface $logger,
        private readonly ImageSecurityValidator $imageSecurityValidator
    ) {
        parent::__construct($context);
    }

    /**
     * Upload vertical image
     *
     * @return Json
     */
    public function execute(): Json
    {
        $result = $this->resultJsonFactory->create();

        try {
            $uploader = $this->uploaderFactory->create(['fileId' => 'vertical_image']);
            $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(true);

            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $uploadResult = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

            // Comprehensive security validation
            $filePath = $mediaDirectory->getAbsolutePath(self::UPLOAD_DIR . $uploadResult['file']);
            $this->imageSecurityValidator->validateImage($filePath, $uploadResult['name'], 'vertical');

            unset($uploadResult['tmp_name'], $uploadResult['path']);

            $uploadResult['url'] = $this->getMediaUrl(self::UPLOAD_DIR . $uploadResult['file']);
            $uploadResult['file'] = $uploadResult['file'] . '.tmp';

            return $result->setData($uploadResult);

        } catch (LocalizedException $e) {
            return $result->setData([
                'error' => $e->getMessage(),
                'errorcode' => $e->getCode()
            ]);
        } catch (\Exception $e) {
            $this->logger->critical('Vertical image upload error: ' . $e->getMessage());
            return $result->setData([
                'error' => __('Something went wrong while uploading the vertical image.'),
                'errorcode' => $e->getCode()
            ]);
        }
    }



    /**
     * Get media URL
     *
     * @param string $file
     * @return string
     */
    private function getMediaUrl(string $file): string
    {
        return $this->_url->getBaseUrl(['_type' => 'media']) . $file;
    }

    /**
     * Check admin permissions
     *
     * @return bool
     */
    protected function _isAllowed(): bool
    {
        return $this->_authorization->isAllowed(self::ADMIN_RESOURCE);
    }
}
