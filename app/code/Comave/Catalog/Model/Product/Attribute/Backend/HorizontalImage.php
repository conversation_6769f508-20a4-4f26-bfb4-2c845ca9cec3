<?php
declare(strict_types=1);

namespace Comave\Catalog\Model\Product\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Psr\Log\LoggerInterface;
use Comave\Catalog\Service\ImageSecurityValidator;

class HorizontalImage extends AbstractBackend
{
    private const UPLOAD_DIR = 'catalog/product/horizontal';
    private const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    public function __construct(
        private readonly ImageSecurityValidator $imageSecurityValidator
    ) {}

    /**
     * Save uploaded file and set its name to product
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     * @throws LocalizedException
     */
    public function afterSave($object): self
    {
        $value = $object->getData($this->getAttribute()->getName());

        if (empty($value) || !is_array($value)) {
            return $this;
        }

        // Handle file deletion
        if (!empty($value['delete'])) {
            $object->setData($this->getAttribute()->getName(), '');
            $this->getAttribute()->getEntity()->saveAttribute($object, $this->getAttribute()->getName());
            return $this;
        }

        // Handle file upload
        if (!empty($value['tmp_name'])) {
            try {
                $uploader = $this->_fileUploaderFactory->create(['fileId' => $this->getAttribute()->getName()]);
                $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
                $uploader->setAllowRenameFiles(true);
                $uploader->setFilesDispersion(true);

                $mediaDirectory = $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA);
                $result = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

                $object->setData($this->getAttribute()->getName(), self::UPLOAD_DIR . $result['file']);
                $this->getAttribute()->getEntity()->saveAttribute($object, $this->getAttribute()->getName());
            } catch (\Exception $e) {
                if ($e->getCode() != \Magento\MediaStorage\Model\File\Uploader::TMP_NAME_EMPTY) {
                    $this->_logger->critical($e);
                    throw new LocalizedException(__('Something went wrong while saving the horizontal image.'));
                }
            }
        }

        return $this;
    }

    /**
     * Validate uploaded file
     *
     * @param \Magento\Framework\DataObject $object
     * @return bool
     * @throws LocalizedException
     */
    public function validate($object): bool
    {
        $attribute = $this->getAttribute();
        $attributeName = $attribute->getName();
        $value = $object->getData($attributeName);

        if ($attribute->getIsRequired() && empty($value)) {
            throw new LocalizedException(__('The horizontal image is required.'));
        }

        if (!empty($value) && is_array($value) && !empty($value['tmp_name'])) {
            $this->imageSecurityValidator->validateImage($value['tmp_name'], $value['name'] ?? '', 'horizontal');
        }

        return true;
    }


}
