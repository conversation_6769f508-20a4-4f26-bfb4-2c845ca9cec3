<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Model\Category\Attribute\Backend;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Psr\Log\LoggerInterface;

/**
 * Category horizontal image attribute backend model
 */
class HorizontalImage extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
{
    const UPLOAD_DIR = 'catalog/category/horizontal';
    const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * @var UploaderFactory
     */
    protected $_fileUploaderFactory;

    /**
     * @var Filesystem
     */
    protected $_filesystem;

    /**
     * @var LoggerInterface
     */
    protected $_logger;

    /**
     * @var string
     */
    private $additionalData = '_additional_data_';

    /**
     * @param UploaderFactory $fileUploaderFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     */
    public function __construct(
        UploaderFactory $fileUploaderFactory,
        Filesystem $filesystem,
        LoggerInterface $logger
    ) {
        $this->_fileUploaderFactory = $fileUploaderFactory;
        $this->_filesystem = $filesystem;
        $this->_logger = $logger;
    }

    /**
     * Gets image name from $value array.
     *
     * @param array $value Attribute value
     * @return string
     */
    private function getUploadedImageName($value)
    {
        if (is_array($value) && isset($value[0]['name'])) {
            return $value[0]['name'];
        }
        return '';
    }

    /**
     * Check if temporary file is available for new image upload.
     *
     * @param array $value
     * @return bool
     */
    private function isTmpFileAvailable($value)
    {
        return is_array($value) && isset($value[0]['tmp_name']);
    }

    /**
     * Avoiding saving potential upload data to DB.
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     */
    public function beforeSave($object)
    {
        $attributeName = $this->getAttribute()->getName();
        $value = $object->getData($attributeName);

        $this->_logger->info('HorizontalImage beforeSave called', [
            'attribute_name' => $attributeName,
            'value' => $value,
            'object_id' => $object->getId()
        ]);

        if ($this->isTmpFileAvailable($value) && $imageName = $this->getUploadedImageName($value)) {
            try {
                $this->_logger->info('HorizontalImage: Processing file upload', [
                    'image_name' => $imageName,
                    'tmp_name' => $value[0]['tmp_name'] ?? 'not_set'
                ]);

                // Create custom image uploader for horizontal images
                $uploader = $this->_fileUploaderFactory->create(['fileId' => $attributeName]);
                $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
                $uploader->setAllowRenameFiles(true);
                $uploader->setFilesDispersion(true);

                $mediaDirectory = $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA);
                $result = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

                $finalPath = self::UPLOAD_DIR . $result['file'];
                $this->_logger->info('HorizontalImage: File uploaded successfully', [
                    'result' => $result,
                    'final_path' => $finalPath
                ]);

                $value[0]['url'] = '/' . $finalPath;
                $value[0]['name'] = $value[0]['url'];
            } catch (\Exception $e) {
                $this->_logger->critical('HorizontalImage: Upload failed', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode()
                ]);
            }
        }

        if ($imageName = $this->getUploadedImageName($value)) {
            $object->setData($this->additionalData . $attributeName, $value);
            $object->setData($attributeName, $imageName);
            $this->_logger->info('HorizontalImage: Set image name', ['image_name' => $imageName]);
        } elseif (!is_string($value)) {
            $object->setData($attributeName, null);
            $this->_logger->info('HorizontalImage: Set attribute to null');
        }

        return parent::beforeSave($object);
    }

    /**
     * Save uploaded file and set its name to category
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     */
    public function afterSave($object)
    {
        return $this;
    }
}
