<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Model\Category\Attribute\Backend;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Psr\Log\LoggerInterface;

/**
 * Category vertical image attribute backend model
 */
class VerticalImage extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
{
    const UPLOAD_DIR = 'catalog/category/vertical';
    const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * @var UploaderFactory
     */
    protected $_fileUploaderFactory;

    /**
     * @var Filesystem
     */
    protected $_filesystem;

    /**
     * @var LoggerInterface
     */
    protected $_logger;

    /**
     * @param UploaderFactory $fileUploaderFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     */
    public function __construct(
        UploaderFactory $fileUploaderFactory,
        Filesystem $filesystem,
        LoggerInterface $logger
    ) {
        $this->_fileUploaderFactory = $fileUploaderFactory;
        $this->_filesystem = $filesystem;
        $this->_logger = $logger;
    }

    /**
     * Save uploaded file and set its name to category
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     * @throws LocalizedException
     */
    public function afterSave($object)
    {
        $value = $object->getData($this->getAttribute()->getName());

        if (empty($value) || !is_array($value)) {
            return $this;
        }

        // Handle file upload
        if (!empty($value['tmp_name'])) {
            try {
                $uploader = $this->_fileUploaderFactory->create(['fileId' => $this->getAttribute()->getName()]);
                $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
                $uploader->setAllowRenameFiles(true);
                $uploader->setFilesDispersion(true);

                $mediaDirectory = $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA);
                $result = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

                $object->setData($this->getAttribute()->getName(), self::UPLOAD_DIR . $result['file']);
                $this->getAttribute()->getEntity()->saveAttribute($object, $this->getAttribute()->getName());
            } catch (\Exception $e) {
                if ($e->getCode() != \Magento\MediaStorage\Model\File\Uploader::TMP_NAME_EMPTY) {
                    $this->_logger->critical($e);
                    throw new LocalizedException(__('Something went wrong while saving the vertical image.'));
                }
            }
        }

        return $this;
    }
}
