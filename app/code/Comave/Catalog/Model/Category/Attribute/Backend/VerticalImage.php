<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Model\Category\Attribute\Backend;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Category vertical image attribute backend model
 */
class VerticalImage extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
{
    const UPLOAD_DIR = 'catalog/category/vertical';
    const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * @var UploaderFactory
     */
    protected $_fileUploaderFactory;

    /**
     * @var Filesystem
     */
    protected $_filesystem;

    /**
     * @var LoggerInterface
     */
    protected $_logger;

    /**
     * @var StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var string
     */
    private $additionalData = '_additional_data_';

    /**
     * @param UploaderFactory $fileUploaderFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        UploaderFactory $fileUploaderFactory,
        Filesystem $filesystem,
        LoggerInterface $logger,
        StoreManagerInterface $storeManager
    ) {
        $this->_fileUploaderFactory = $fileUploaderFactory;
        $this->_filesystem = $filesystem;
        $this->_logger = $logger;
        $this->_storeManager = $storeManager;
    }

    /**
     * Gets image name from $value array.
     *
     * @param mixed $value Attribute value
     * @return string
     */
    private function getUploadedImageName($value)
    {
        // Handle different data formats from imageUploader component
        if (is_array($value)) {
            // Format 1: [0 => ['name' => 'filename.jpg', 'tmp_name' => '/tmp/...']]
            if (isset($value[0]['name'])) {
                return $value[0]['name'];
            }
            // Format 2: ['name' => 'filename.jpg', 'tmp_name' => '/tmp/...']
            if (isset($value['name'])) {
                return $value['name'];
            }
        }
        return '';
    }

    /**
     * Check if temporary file is available for new image upload.
     *
     * @param mixed $value
     * @return bool
     */
    private function isTmpFileAvailable($value)
    {
        if (is_array($value)) {
            // Format 1: [0 => ['tmp_name' => '/tmp/...']] - Direct upload
            if (isset($value[0]['tmp_name'])) {
                return true;
            }
            // Format 2: ['tmp_name' => '/tmp/...'] - Direct upload
            if (isset($value['tmp_name'])) {
                return true;
            }
            // Format 3: [0 => ['file' => '/path/to/file']] - UI component upload
            if (isset($value[0]['file'])) {
                return true;
            }
            // Format 4: ['file' => '/path/to/file'] - UI component upload
            if (isset($value['file'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Avoiding saving potential upload data to DB.
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     */
    public function beforeSave($object)
    {
        $attributeName = $this->getAttribute()->getName();
        $value = $object->getData($attributeName);

        $this->_logger->info('VerticalImage beforeSave called', [
            'attribute_name' => $attributeName,
            'value' => $value,
            'value_type' => gettype($value),
            'object_id' => $object->getId()
        ]);

        // Handle file deletion
        if (is_array($value) && !empty($value['delete'])) {
            $this->_logger->info('VerticalImage: Deleting file');
            $object->setData($attributeName, '');
            return parent::beforeSave($object);
        }

        if ($this->isTmpFileAvailable($value) && $imageName = $this->getUploadedImageName($value)) {
            try {
                // Check if this is a UI component upload (file already in tmp directory)
                $fileData = isset($value[0]) ? $value[0] : $value;

                if (isset($fileData['file']) && isset($fileData['url'])) {
                    // UI component format - file is already uploaded to tmp directory
                    $this->_logger->info('VerticalImage: Processing UI component upload', [
                        'image_name' => $imageName,
                        'file_path' => $fileData['file'],
                        'url' => $fileData['url']
                    ]);

                    // Move file from tmp to final directory
                    $mediaDirectory = $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA);
                    $tmpPath = 'tmp/catalog/category/vertical' . $fileData['file'];
                    $finalPath = self::UPLOAD_DIR . $fileData['file'];

                    if ($mediaDirectory->isExist($tmpPath)) {
                        $mediaDirectory->copyFile($tmpPath, $finalPath);
                        $mediaDirectory->delete($tmpPath);

                        $this->_logger->info('VerticalImage: File moved successfully', [
                            'from' => $tmpPath,
                            'to' => $finalPath
                        ]);

                        // Update the value array with the final URL for UI component
                        $baseUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
                        $finalUrl = $baseUrl . $finalPath;
                        $value[0]['url'] = $finalUrl;
                        $value[0]['name'] = $finalUrl;

                        $this->_logger->info('VerticalImage: Updated value array', [
                            'final_url' => $finalUrl,
                            'value' => $value
                        ]);
                    } else {
                        $this->_logger->error('VerticalImage: Tmp file not found', ['tmp_path' => $tmpPath]);
                    }
                } else {
                    // Direct upload format - use traditional uploader
                    $tmpName = isset($value[0]['tmp_name']) ? $value[0]['tmp_name'] : $value['tmp_name'];
                    $this->_logger->info('VerticalImage: Processing direct upload', [
                        'image_name' => $imageName,
                        'tmp_name' => $tmpName
                    ]);

                    $uploader = $this->_fileUploaderFactory->create(['fileId' => $attributeName]);
                    $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
                    $uploader->setAllowRenameFiles(true);
                    $uploader->setFilesDispersion(true);

                    $mediaDirectory = $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA);
                    $result = $uploader->save($mediaDirectory->getAbsolutePath(self::UPLOAD_DIR));

                    $finalPath = self::UPLOAD_DIR . '/' . $result['file'];
                    $this->_logger->info('VerticalImage: File uploaded successfully', [
                        'result' => $result,
                        'final_path' => $finalPath
                    ]);

                    $object->setData($attributeName, $finalPath);
                    $this->_logger->info('VerticalImage: Set image path', ['final_path' => $finalPath]);
                }
            } catch (\Exception $e) {
                $this->_logger->critical('VerticalImage: Upload failed', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode()
                ]);

                if ($e->getCode() != \Magento\MediaStorage\Model\File\Uploader::TMP_NAME_EMPTY) {
                    throw new LocalizedException(__('Something went wrong while saving the vertical image: %1', $e->getMessage()));
                }
            }
        }

        // Set the data following Magento's pattern
        if ($imageName = $this->getUploadedImageName($value)) {
            // Store the additional data for UI component
            $object->setData($this->additionalData . $attributeName, $value);
            // Store the image name/path for the attribute (following Magento's pattern)
            $object->setData($attributeName, $imageName);
            $this->_logger->info('VerticalImage: Set data following Magento pattern', [
                'image_name' => $imageName,
                'additional_data_key' => $this->additionalData . $attributeName,
                'additional_data_value' => $value
            ]);
        } elseif (is_string($value) && !empty($value)) {
            // Keep existing image path if it's a string
            $this->_logger->info('VerticalImage: Keeping existing image', ['value' => $value]);
        } else {
            // Clear the attribute if no valid data
            $this->_logger->info('VerticalImage: No valid image data, clearing attribute');
            $object->setData($attributeName, null);
        }

        return parent::beforeSave($object);
    }

    /**
     * Reconstruct data for UI component after loading
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     */
    public function afterLoad($object)
    {
        $attributeName = $this->getAttribute()->getName();
        $imagePath = $object->getData($attributeName);
        $additionalData = $object->getData($this->additionalData . $attributeName);

        $this->_logger->info('VerticalImage afterLoad called', [
            'attribute_name' => $attributeName,
            'image_path' => $imagePath,
            'image_path_type' => gettype($imagePath),
            'additional_data' => $additionalData,
            'additional_data_type' => gettype($additionalData),
            'object_id' => $object->getId()
        ]);

        if ($imagePath && is_string($imagePath)) {
            // Check if additional data already exists (from previous save)
            if (!$additionalData) {
                // Reconstruct the value array for UI component
                $baseUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);

                // Handle both relative paths and full URLs
                if (strpos($imagePath, 'http') === 0) {
                    // Already a full URL
                    $imageUrl = $imagePath;
                } else {
                    // Relative path, add base URL
                    $imageUrl = $baseUrl . $imagePath;
                }

                $value = [
                    [
                        'name' => $imageUrl,
                        'url' => $imageUrl,
                        'size' => 0, // We don't have size info for existing images
                        'type' => 'image'
                    ]
                ];

                // Set the additional data for UI component
                $object->setData($this->additionalData . $attributeName, $value);

                $this->_logger->info('VerticalImage: Reconstructed data for UI component', [
                    'image_path' => $imagePath,
                    'image_url' => $imageUrl,
                    'value' => $value,
                    'additional_data_key' => $this->additionalData . $attributeName
                ]);
            } else {
                $this->_logger->info('VerticalImage: Additional data already exists', [
                    'additional_data' => $additionalData
                ]);
            }
        } else {
            $this->_logger->info('VerticalImage: No image path or invalid type', [
                'image_path' => $imagePath,
                'type' => gettype($imagePath)
            ]);
        }

        return parent::afterLoad($object);
    }

    /**
     * Save uploaded file and set its name to category
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     */
    public function afterSave($object)
    {
        return $this;
    }
}
