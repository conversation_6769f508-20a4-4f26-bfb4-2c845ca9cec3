<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Setup\Patch\Data;

use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

/**
 * Create category horizontal and vertical image attributes
 */
class CreateCategoryOrientationImageFields implements DataPatchInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        // Add horizontal_image attribute
        $eavSetup->addAttribute(
            Category::ENTITY,
            'horizontal_image',
            [
                'type' => 'varchar',
                'label' => 'Horizontal Image',
                'input' => 'image',
                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\HorizontalImage::class,
                'required' => false,
                'sort_order' => 41,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'Content',
                'used_in_product_listing' => false,
                'visible_on_front' => false,
                'user_defined' => false,
                'default' => '',
            ]
        );

        // Add vertical_image attribute
        $eavSetup->addAttribute(
            Category::ENTITY,
            'vertical_image',
            [
                'type' => 'varchar',
                'label' => 'Vertical Image',
                'input' => 'image',
                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\VerticalImage::class,
                'required' => false,
                'sort_order' => 42,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'Content',
                'used_in_product_listing' => false,
                'visible_on_front' => false,
                'user_defined' => false,
                'default' => '',
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases()
    {
        return [];
    }
}
