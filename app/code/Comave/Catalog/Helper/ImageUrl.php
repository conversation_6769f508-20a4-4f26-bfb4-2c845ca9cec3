<?php
declare(strict_types=1);

namespace Comave\Catalog\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\UrlInterface;
use Psr\Log\LoggerInterface;

class ImageUrl extends AbstractHelper
{
    public function __construct(
        Context $context,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Get horizontal image URL
     *
     * @param string $imagePath
     * @return string
     */
    public function getHorizontalImageUrl(string $imagePath): string
    {
        if (empty($imagePath)) {
            return '';
        }

        return $this->getImageUrl($imagePath);
    }

    /**
     * Get vertical image URL
     *
     * @param string $imagePath
     * @return string
     */
    public function getVerticalImageUrl(string $imagePath): string
    {
        if (empty($imagePath)) {
            return '';
        }

        return $this->getImageUrl($imagePath);
    }

    /**
     * Get image URL with thumbnail size
     *
     * @param string $imagePath
     * @param string $size
     * @return string
     */
    public function getImageUrlWithSize(string $imagePath, string $size = 'medium'): string
    {
        if (empty($imagePath)) {
            return '';
        }

        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $size . '_' . $pathInfo['basename'];
        
        return $this->getImageUrl($thumbnailPath);
    }

    /**
     * Get base image URL
     *
     * @param string $imagePath
     * @return string
     */
    private function getImageUrl(string $imagePath): string
    {
        try {
            $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $baseUrl . ltrim($imagePath, '/');
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Failed to get store base URL: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Check if image exists
     *
     * @param string $imagePath
     * @return bool
     */
    public function imageExists(string $imagePath): bool
    {
        if (empty($imagePath)) {
            return false;
        }

        $fullPath = BP . '/pub/media/' . ltrim($imagePath, '/');
        return file_exists($fullPath);
    }

    /**
     * Get image dimensions
     *
     * @param string $imagePath
     * @return array|null
     */
    public function getImageDimensions(string $imagePath): ?array
    {
        if (!$this->imageExists($imagePath)) {
            return null;
        }

        $fullPath = BP . '/pub/media/' . ltrim($imagePath, '/');
        $imageInfo = getimagesize($fullPath);
        
        if ($imageInfo === false) {
            return null;
        }

        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'type' => $imageInfo[2],
            'mime' => $imageInfo['mime']
        ];
    }

    /**
     * Get placeholder image URL
     *
     * @param string $type
     * @return string
     */
    public function getPlaceholderUrl(string $type = 'horizontal'): string
    {
        try {
            $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $baseUrl . 'catalog/product/placeholder/' . $type . '_placeholder.jpg';
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Failed to get placeholder URL: ' . $e->getMessage());
            return '';
        }
    }
}
