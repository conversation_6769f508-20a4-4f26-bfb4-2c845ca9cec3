define([
    'Magento_Ui/js/form/element/file-uploader',
    'Magento_Ui/js/modal/alert',
    'mage/translate'
], function (FileUploader, alert, $t) {
    'use strict';

    return FileUploader.extend({
        defaults: {
            orientation: 'horizontal', // 'horizontal' or 'vertical'
            maxDimension: 3000,
            validationMessages: {
                horizontal: $t('Horizontal image should have width greater than height. Current dimensions: %1x%2'),
                vertical: $t('Vertical image should have height greater than width. Current dimensions: %1x%2'),
                maxDimension: $t('Image dimensions are too large. Maximum allowed: %1x%1 pixels.')
            }
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            this.initOrientationValidation();
            return this;
        },

        /**
         * Initialize orientation validation
         */
        initOrientationValidation: function () {
            // Override the processFile method to add orientation validation
            this.processFile = this.processFile.bind(this);
        },

        /**
         * Process uploaded file with orientation validation
         *
         * @param {Object} file
         * @returns {Object}
         */
        processFile: function (file) {
            var self = this,
                processedFile = this._super(file);

            if (file.type && file.type.indexOf('image/') === 0) {
                this.validateImageOrientation(file).then(function (isValid) {
                    if (!isValid) {
                        self.removeFile(file);
                    }
                }).catch(function (error) {
                    console.error('Image validation error:', error);
                    self.removeFile(file);
                });
            }

            return processedFile;
        },

        /**
         * Validate image orientation
         *
         * @param {Object} file
         * @returns {Promise}
         */
        validateImageOrientation: function (file) {
            var self = this;

            return new Promise(function (resolve, reject) {
                var img = new Image(),
                    reader = new FileReader();

                reader.onload = function (e) {
                    img.onload = function () {
                        var width = this.naturalWidth,
                            height = this.naturalHeight,
                            isValid = self.checkOrientation(width, height);

                        if (!isValid) {
                            self.showOrientationError(width, height);
                        }

                        resolve(isValid);
                    };

                    img.onerror = function () {
                        reject(new Error('Failed to load image'));
                    };

                    img.src = e.target.result;
                };

                reader.onerror = function () {
                    reject(new Error('Failed to read file'));
                };

                reader.readAsDataURL(file);
            });
        },

        /**
         * Check if image orientation is correct
         *
         * @param {Number} width
         * @param {Number} height
         * @returns {Boolean}
         */
        checkOrientation: function (width, height) {
            // Check maximum dimensions
            if (width > this.maxDimension || height > this.maxDimension) {
                this.showDimensionError();
                return false;
            }

            // Check orientation
            if (this.orientation === 'horizontal') {
                return width > height;
            } else if (this.orientation === 'vertical') {
                return height > width;
            }

            return true;
        },

        /**
         * Show orientation error message
         *
         * @param {Number} width
         * @param {Number} height
         */
        showOrientationError: function (width, height) {
            var message = this.validationMessages[this.orientation];
            
            alert({
                title: $t('Invalid Image Orientation'),
                content: message.replace('%1', width).replace('%2', height),
                actions: {
                    always: function () {}
                }
            });
        },

        /**
         * Show dimension error message
         */
        showDimensionError: function () {
            alert({
                title: $t('Image Too Large'),
                content: this.validationMessages.maxDimension.replace('%1', this.maxDimension),
                actions: {
                    always: function () {}
                }
            });
        },

        /**
         * Remove file from uploader
         *
         * @param {Object} file
         */
        removeFile: function (file) {
            var fileId = file.uid || file.id;
            
            if (fileId && this.value()) {
                var files = this.value().filter(function (item) {
                    return item.uid !== fileId && item.id !== fileId;
                });
                
                this.value(files);
            }
        },

        /**
         * Handle upload success
         *
         * @param {Object} file
         * @param {Object} response
         */
        onUploadSuccess: function (file, response) {
            if (response && response.error) {
                alert({
                    title: $t('Upload Error'),
                    content: response.error,
                    actions: {
                        always: function () {}
                    }
                });
                this.removeFile(file);
                return;
            }

            this._super(file, response);
        },

        /**
         * Handle upload error
         *
         * @param {Object} file
         * @param {Object} response
         */
        onUploadError: function (file, response) {
            var errorMessage = $t('An error occurred while uploading the image.');
            
            if (response && response.error) {
                errorMessage = response.error;
            }

            alert({
                title: $t('Upload Error'),
                content: errorMessage,
                actions: {
                    always: function () {}
                }
            });

            this.removeFile(file);
        }
    });
});
