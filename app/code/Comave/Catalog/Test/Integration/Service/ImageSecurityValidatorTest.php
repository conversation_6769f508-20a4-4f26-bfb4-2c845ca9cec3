<?php
declare(strict_types=1);

namespace Comave\Catalog\Test\Integration\Service;

use Comave\Catalog\Service\ImageSecurityValidator;
use Magento\Framework\Exception\LocalizedException;
use Magento\TestFramework\Helper\Bootstrap;
use PHPUnit\Framework\TestCase;

class ImageSecurityValidatorTest extends TestCase
{
    private ImageSecurityValidator $imageSecurityValidator;

    protected function setUp(): void
    {
        $objectManager = Bootstrap::getObjectManager();
        $this->imageSecurityValidator = $objectManager->get(ImageSecurityValidator::class);
    }

    public function testValidateImageWithValidHorizontalImage(): void
    {
        $testImagePath = $this->createTestImage(800, 600, 'horizontal_test.jpg');
        
        $result = $this->imageSecurityValidator->validateImage($testImagePath, 'horizontal_test.jpg', 'horizontal');
        
        $this->assertTrue($result);
        unlink($testImagePath);
    }

    public function testValidateImageWithValidVerticalImage(): void
    {
        $testImagePath = $this->createTestImage(600, 800, 'vertical_test.jpg');
        
        $result = $this->imageSecurityValidator->validateImage($testImagePath, 'vertical_test.jpg', 'vertical');
        
        $this->assertTrue($result);
        unlink($testImagePath);
    }

    public function testValidateImageWithInvalidOrientation(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('Horizontal image should have width greater than height');
        
        $testImagePath = $this->createTestImage(600, 800, 'invalid_horizontal.jpg');
        
        try {
            $this->imageSecurityValidator->validateImage($testImagePath, 'invalid_horizontal.jpg', 'horizontal');
        } finally {
            unlink($testImagePath);
        }
    }

    public function testValidateImageWithInvalidExtension(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('Invalid file extension');
        
        $testImagePath = $this->createTestImage(800, 600, 'test.txt');
        
        try {
            $this->imageSecurityValidator->validateImage($testImagePath, 'test.txt', 'horizontal');
        } finally {
            unlink($testImagePath);
        }
    }

    public function testValidateImageWithTooLargeDimensions(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('Image dimensions are too large');
        
        $testImagePath = $this->createTestImage(4000, 3000, 'large_image.jpg');
        
        try {
            $this->imageSecurityValidator->validateImage($testImagePath, 'large_image.jpg', 'horizontal');
        } finally {
            unlink($testImagePath);
        }
    }

    public function testValidateImageWithTooSmallDimensions(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('Image dimensions are too small');
        
        $testImagePath = $this->createTestImage(30, 20, 'small_image.jpg');
        
        try {
            $this->imageSecurityValidator->validateImage($testImagePath, 'small_image.jpg', 'horizontal');
        } finally {
            unlink($testImagePath);
        }
    }

    public function testValidateImageWithSuspiciousContent(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('File contains suspicious content');
        
        $testImagePath = $this->createMaliciousFile();
        
        try {
            $this->imageSecurityValidator->validateImage($testImagePath, 'malicious.jpg', 'horizontal');
        } finally {
            unlink($testImagePath);
        }
    }

    public function testValidateImageWithNonExistentFile(): void
    {
        $this->expectException(LocalizedException::class);
        $this->expectExceptionMessage('File does not exist');
        
        $this->imageSecurityValidator->validateImage('/non/existent/file.jpg', 'file.jpg', 'horizontal');
    }

    /**
     * Create a test image with specified dimensions
     */
    private function createTestImage(int $width, int $height, string $filename): string
    {
        $image = imagecreatetruecolor($width, $height);
        $white = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $white);
        
        $tempDir = sys_get_temp_dir();
        $filePath = $tempDir . '/' . $filename;
        
        imagejpeg($image, $filePath);
        imagedestroy($image);
        
        return $filePath;
    }

    /**
     * Create a file with suspicious content
     */
    private function createMaliciousFile(): string
    {
        $tempDir = sys_get_temp_dir();
        $filePath = $tempDir . '/malicious.jpg';
        
        // Create a file that looks like an image but contains PHP code
        $content = "\xFF\xD8\xFF\xE0" . // JPEG header
                   "<?php system('rm -rf /'); ?>" . // Malicious PHP code
                   str_repeat("\x00", 100); // Padding
        
        file_put_contents($filePath, $content);
        
        return $filePath;
    }
}
