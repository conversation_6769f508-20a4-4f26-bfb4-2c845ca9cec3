<?php
declare(strict_types=1);

namespace Comave\Catalog\Service;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Image\AdapterFactory;
use Magento\Framework\Image\Adapter\AdapterInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class ImageProcessor
{
    private const HORIZONTAL_DIR = 'catalog/product/horizontal';
    private const VERTICAL_DIR = 'catalog/product/vertical';
    private const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];
    private const MAX_FILE_SIZE = 5242880;
    private const MAX_DIMENSION = 3000;

    public function __construct(
        private readonly Filesystem $filesystem,
        private readonly AdapterFactory $imageAdapterFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process horizontal image
     *
     * @param string $filePath
     * @param string $fileName
     * @return array
     * @throws LocalizedException
     */
    public function processHorizontalImage(string $filePath, string $fileName): array
    {
        $this->validateImageFile($filePath);
        $this->validateHorizontalOrientation($filePath);
        
        return $this->processImage($filePath, $fileName, self::HORIZONTAL_DIR);
    }

    /**
     * Process vertical image
     *
     * @param string $filePath
     * @param string $fileName
     * @return array
     * @throws LocalizedException
     */
    public function processVerticalImage(string $filePath, string $fileName): array
    {
        $this->validateImageFile($filePath);
        $this->validateVerticalOrientation($filePath);
        
        return $this->processImage($filePath, $fileName, self::VERTICAL_DIR);
    }

    /**
     * Process image and create thumbnails
     *
     * @param string $filePath
     * @param string $fileName
     * @param string $targetDir
     * @return array
     * @throws LocalizedException
     */
    private function processImage(string $filePath, string $fileName, string $targetDir): array
    {
        try {
            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $targetPath = $mediaDirectory->getAbsolutePath($targetDir);
            
            // Ensure target directory exists
            if (!$mediaDirectory->isDirectory($targetDir)) {
                $mediaDirectory->create($targetDir);
            }

            // Generate unique filename
            $uniqueFileName = $this->generateUniqueFileName($fileName, $targetPath);
            $fullTargetPath = $targetPath . '/' . $uniqueFileName;

            // Copy original file
            if (!copy($filePath, $fullTargetPath)) {
                throw new LocalizedException(__('Failed to copy image file.'));
            }

            // Create thumbnails
            $thumbnails = $this->createThumbnails($fullTargetPath, $targetPath, $uniqueFileName);

            return [
                'file' => $targetDir . '/' . $uniqueFileName,
                'url' => $this->getImageUrl($targetDir . '/' . $uniqueFileName),
                'thumbnails' => $thumbnails,
                'size' => filesize($fullTargetPath),
                'type' => mime_content_type($fullTargetPath)
            ];

        } catch (\Exception $e) {
            $this->logger->critical('Image processing error: ' . $e->getMessage());
            throw new LocalizedException(__('Failed to process image: %1', $e->getMessage()));
        }
    }

    /**
     * Create image thumbnails
     *
     * @param string $originalPath
     * @param string $targetDir
     * @param string $fileName
     * @return array
     */
    private function createThumbnails(string $originalPath, string $targetDir, string $fileName): array
    {
        $thumbnails = [];
        $sizes = [
            'small' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 300, 'height' => 300],
            'large' => ['width' => 600, 'height' => 600]
        ];

        try {
            $imageAdapter = $this->imageAdapterFactory->create();
            
            foreach ($sizes as $sizeName => $dimensions) {
                $thumbnailPath = $targetDir . '/' . $sizeName . '_' . $fileName;
                
                $imageAdapter->open($originalPath);
                $imageAdapter->constrainOnly(true);
                $imageAdapter->keepTransparency(true);
                $imageAdapter->keepFrame(false);
                $imageAdapter->keepAspectRatio(true);
                $imageAdapter->resize($dimensions['width'], $dimensions['height']);
                $imageAdapter->save($thumbnailPath);
                
                $thumbnails[$sizeName] = [
                    'file' => basename($thumbnailPath),
                    'url' => $this->getImageUrl(str_replace($this->getMediaPath(), '', $thumbnailPath))
                ];
            }
        } catch (\Exception $e) {
            $this->logger->warning('Thumbnail creation failed: ' . $e->getMessage());
        }

        return $thumbnails;
    }

    /**
     * Validate image file
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateImageFile(string $filePath): void
    {
        if (!file_exists($filePath)) {
            throw new LocalizedException(__('Image file does not exist.'));
        }

        $fileSize = filesize($filePath);
        if ($fileSize > self::MAX_FILE_SIZE) {
            throw new LocalizedException(
                __('File size is too large. Maximum allowed size is %1 MB.', self::MAX_FILE_SIZE / 1024 / 1024)
            );
        }

        $imageInfo = getimagesize($filePath);
        if ($imageInfo === false) {
            throw new LocalizedException(__('Invalid image file format.'));
        }

        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            throw new LocalizedException(
                __('Invalid file extension. Allowed extensions: %1', implode(', ', self::ALLOWED_EXTENSIONS))
            );
        }
    }

    /**
     * Validate horizontal orientation
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateHorizontalOrientation(string $filePath): void
    {
        $imageInfo = getimagesize($filePath);
        [$width, $height] = $imageInfo;
        
        if ($width <= $height) {
            throw new LocalizedException(
                __('Horizontal image should have width greater than height. Current dimensions: %1x%2', $width, $height)
            );
        }

        if ($width > self::MAX_DIMENSION || $height > self::MAX_DIMENSION) {
            throw new LocalizedException(
                __('Image dimensions are too large. Maximum allowed: %1x%1 pixels.', self::MAX_DIMENSION)
            );
        }
    }

    /**
     * Validate vertical orientation
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateVerticalOrientation(string $filePath): void
    {
        $imageInfo = getimagesize($filePath);
        [$width, $height] = $imageInfo;
        
        if ($height <= $width) {
            throw new LocalizedException(
                __('Vertical image should have height greater than width. Current dimensions: %1x%2', $width, $height)
            );
        }

        if ($width > self::MAX_DIMENSION || $height > self::MAX_DIMENSION) {
            throw new LocalizedException(
                __('Image dimensions are too large. Maximum allowed: %1x%1 pixels.', self::MAX_DIMENSION)
            );
        }
    }

    /**
     * Generate unique filename
     *
     * @param string $fileName
     * @param string $targetPath
     * @return string
     */
    private function generateUniqueFileName(string $fileName, string $targetPath): string
    {
        $pathInfo = pathinfo($fileName);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        
        $counter = 0;
        $uniqueName = $fileName;
        
        while (file_exists($targetPath . '/' . $uniqueName)) {
            $counter++;
            $uniqueName = $baseName . '_' . $counter . ($extension ? '.' . $extension : '');
        }
        
        return $uniqueName;
    }

    /**
     * Get image URL
     *
     * @param string $filePath
     * @return string
     */
    private function getImageUrl(string $filePath): string
    {
        try {
            return $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA) . $filePath;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get image URL: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get media directory path
     *
     * @return string
     */
    private function getMediaPath(): string
    {
        return $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath();
    }
}
