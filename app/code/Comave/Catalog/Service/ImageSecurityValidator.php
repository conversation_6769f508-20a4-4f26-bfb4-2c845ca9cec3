<?php
declare(strict_types=1);

namespace Comave\Catalog\Service;

use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class ImageSecurityValidator
{
    private const ALLOWED_MIME_TYPES = [
        'image/gif',
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/avif',
        'image/svg+xml'
    ];

    private const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'jpeg', 'png', 'webp', 'avif', 'jfif', 'svg'];
    private const MAX_FILE_SIZE = 5242880; // 5MB
    private const MAX_DIMENSION = 3000;
    private const MIN_DIMENSION = 50;

    // Suspicious file signatures that might indicate malicious content
    private const SUSPICIOUS_SIGNATURES = [
        '<?php',
        '<?=',
        '<script',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        'eval(',
        'base64_decode',
        'exec(',
        'system(',
        'shell_exec',
        'passthru',
        'file_get_contents',
        'file_put_contents',
        'fopen',
        'fwrite',
        'include',
        'require'
    ];

    public function __construct(
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Comprehensive image security validation
     *
     * @param string $filePath
     * @param string $originalName
     * @param string $orientation
     * @return bool
     * @throws LocalizedException
     */
    public function validateImage(string $filePath, string $originalName, string $orientation = 'horizontal'): bool
    {
        // Basic file existence check
        if (!file_exists($filePath)) {
            throw new LocalizedException(__('File does not exist.'));
        }

        // File size validation
        $this->validateFileSize($filePath);

        // Extension validation
        $this->validateFileExtension($originalName);

        // MIME type validation
        $this->validateMimeType($filePath);

        // Image format validation
        $imageInfo = $this->validateImageFormat($filePath);

        // Dimension validation
        $this->validateDimensions($imageInfo, $orientation);

        // Content security validation
        $this->validateFileContent($filePath);

        // Additional security checks
        $this->performSecurityScans($filePath);

        return true;
    }

    /**
     * Validate file size
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateFileSize(string $filePath): void
    {
        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize > self::MAX_FILE_SIZE) {
            throw new LocalizedException(
                __('File size is too large. Maximum allowed size is %1 MB.', self::MAX_FILE_SIZE / 1024 / 1024)
            );
        }

        if ($fileSize === 0) {
            throw new LocalizedException(__('File is empty.'));
        }
    }

    /**
     * Validate file extension
     *
     * @param string $fileName
     * @throws LocalizedException
     */
    private function validateFileExtension(string $fileName): void
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        if (!in_array($extension, self::ALLOWED_EXTENSIONS, true)) {
            throw new LocalizedException(
                __('Invalid file extension. Allowed extensions: %1', implode(', ', self::ALLOWED_EXTENSIONS))
            );
        }
    }

    /**
     * Validate MIME type
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateMimeType(string $filePath): void
    {
        $mimeType = mime_content_type($filePath);
        
        if ($mimeType === false || !in_array($mimeType, self::ALLOWED_MIME_TYPES, true)) {
            throw new LocalizedException(
                __('Invalid file type. File MIME type: %1', $mimeType ?: 'unknown')
            );
        }
    }

    /**
     * Validate image format using getimagesize
     *
     * @param string $filePath
     * @return array
     * @throws LocalizedException
     */
    private function validateImageFormat(string $filePath): array
    {
        $imageInfo = getimagesize($filePath);
        
        if ($imageInfo === false) {
            throw new LocalizedException(__('Invalid image file format.'));
        }

        // Verify MIME type matches getimagesize result
        if (!in_array($imageInfo['mime'], self::ALLOWED_MIME_TYPES, true)) {
            throw new LocalizedException(__('Image MIME type validation failed.'));
        }

        return $imageInfo;
    }

    /**
     * Validate image dimensions
     *
     * @param array $imageInfo
     * @param string $orientation
     * @throws LocalizedException
     */
    private function validateDimensions(array $imageInfo, string $orientation): void
    {
        [$width, $height] = $imageInfo;

        // Check minimum dimensions
        if ($width < self::MIN_DIMENSION || $height < self::MIN_DIMENSION) {
            throw new LocalizedException(
                __('Image dimensions are too small. Minimum size: %1x%1 pixels.', self::MIN_DIMENSION)
            );
        }

        // Check maximum dimensions
        if ($width > self::MAX_DIMENSION || $height > self::MAX_DIMENSION) {
            throw new LocalizedException(
                __('Image dimensions are too large. Maximum size: %1x%1 pixels.', self::MAX_DIMENSION)
            );
        }

        // Validate orientation
        if ($orientation === 'horizontal' && $width <= $height) {
            throw new LocalizedException(
                __('Horizontal image should have width greater than height. Current dimensions: %1x%2', $width, $height)
            );
        }

        if ($orientation === 'vertical' && $height <= $width) {
            throw new LocalizedException(
                __('Vertical image should have height greater than width. Current dimensions: %1x%2', $width, $height)
            );
        }
    }

    /**
     * Validate file content for suspicious patterns
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateFileContent(string $filePath): void
    {
        $content = file_get_contents($filePath, false, null, 0, 8192); // Read first 8KB
        
        if ($content === false) {
            throw new LocalizedException(__('Unable to read file content.'));
        }

        $contentLower = strtolower($content);

        foreach (self::SUSPICIOUS_SIGNATURES as $signature) {
            if (strpos($contentLower, strtolower($signature)) !== false) {
                $this->logger->warning('Suspicious content detected in uploaded image', [
                    'file_path' => $filePath,
                    'signature' => $signature
                ]);
                throw new LocalizedException(__('File contains suspicious content and cannot be uploaded.'));
            }
        }
    }

    /**
     * Perform additional security scans
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function performSecurityScans(string $filePath): void
    {
        // Check for double extensions (e.g., image.php.jpg)
        $fileName = basename($filePath);
        if (substr_count($fileName, '.') > 1) {
            $this->logger->warning('File with multiple extensions detected', ['file_name' => $fileName]);
            throw new LocalizedException(__('Files with multiple extensions are not allowed.'));
        }

        // Check for null bytes in filename
        if (strpos($fileName, "\0") !== false) {
            throw new LocalizedException(__('Invalid characters in filename.'));
        }

        // Validate that the file is actually an image by trying to create an image resource
        $this->validateImageResource($filePath);
    }

    /**
     * Validate image by creating image resource
     *
     * @param string $filePath
     * @throws LocalizedException
     */
    private function validateImageResource(string $filePath): void
    {
        $imageInfo = getimagesize($filePath);
        if ($imageInfo === false) {
            return; // Already validated in validateImageFormat
        }

        $imageType = $imageInfo[2];
        $imageResource = null;

        try {
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $imageResource = imagecreatefromjpeg($filePath);
                    break;
                case IMAGETYPE_PNG:
                    $imageResource = imagecreatefrompng($filePath);
                    break;
                case IMAGETYPE_GIF:
                    $imageResource = imagecreatefromgif($filePath);
                    break;
                case IMAGETYPE_WEBP:
                    if (function_exists('imagecreatefromwebp')) {
                        $imageResource = imagecreatefromwebp($filePath);
                    }
                    break;
                case IMAGETYPE_AVIF:
                    if (function_exists('imagecreatefromavif')) {
                        $imageResource = imagecreatefromavif($filePath);
                    }
                    break;
            }

            if ($imageResource === false) {
                throw new LocalizedException(__('Unable to process image file. File may be corrupted.'));
            }

            if ($imageResource !== null) {
                imagedestroy($imageResource);
            }
        } catch (\Exception $e) {
            $this->logger->error('Image resource validation failed', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
            throw new LocalizedException(__('Image validation failed. File may be corrupted or invalid.'));
        }
    }
}
