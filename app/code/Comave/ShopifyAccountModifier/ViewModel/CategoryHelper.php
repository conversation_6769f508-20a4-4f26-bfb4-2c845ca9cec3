<?php

declare(strict_types=1);

namespace Comave\ShopifyAccountModifier\ViewModel;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProTypeModel;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable\Attribute as ConfigurableAttributeModel;
use Magento\Eav\Api\AttributeManagementInterface;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Group\CollectionFactory as AttrGroupCollection;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory as AttrOptionCollectionFactory;
use Magento\Framework\Data\Form\FormKey;
use Magento\Framework\Filter\FilterManager;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ImportedtmpproductRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifyaccountsRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifycategorymapRepositoryInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\SaveProduct;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ListingTemplateFactory;

class CategoryHelper extends Data
{
    public function __construct(
        FilterManager $filterManager,
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifycategorymap\Source\RootCatFactory $rootCat,
        ShopifycategorymapRepositoryInterface $shopifyCategoryMapRepository,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyaccountsFactory,
        ShopifyaccountsRepositoryInterface $shopifyAccountsRepository,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\Storage\DbStorage $dbStorage,
        ImportedtmpproductRepositoryInterface $importedTmpProductRepository,
        \Magento\Catalog\Model\ProductFactory $product,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        FormKey $formkey,
        SaveProduct $saveProduct,
        AttributeFactory $attributeFactory,
        AttrGroupCollection $attrGroupCollection,
        AttrOptionCollectionFactory $attrOptionCollectionFactory,
        AttributeManagementInterface $attributeManagement,
        ProductAttributeRepositoryInterface $productAttribute,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Filesystem $filesystem,
        ConfigurableAttributeModel $configurableAttributeModel,
        ConfigurableProTypeModel $configurableProTypeModel,
        \Magento\Widget\Model\Template\Filter $templateProcessor,
        \Magento\Framework\Indexer\IndexerInterfaceFactory $indexerFactory,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Quote\Api\CartRepositoryInterface $cartRepositoryInterface,
        \Magento\Quote\Api\CartManagementInterface $cartManagementInterface,
        \Magento\Quote\Model\Quote\Address\Rate $shippingRate,
        \Magento\Sales\Model\Order $order,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Backend\Model\Session $backendSession,
        \Magento\Directory\Model\Config\Source\Country $countryHelper,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\HTTP\Client\Curl $curl,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\PriceRuleFactory $priceRule,
        ListingTemplateFactory $listingTemplate,
        \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger $logger,
        \Magento\Framework\Filesystem\Driver\File $file, \Webkul\Marketplace\Helper\Data $mphelper,
        \Webkul\Marketplace\Model\ProductFactory $mpProductFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Laminas\Uri\Uri $laminasUri,
        \Magento\Catalog\Api\AttributeSetRepositoryInterface $attributeSetRepository,
        private readonly \Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Categories $categoryTree,
        \Comave\MapOrderStatuses\Service\OrderStatuses $orderStatuses,
    ) {
        parent::__construct(
            $filterManager,
            $context,
            $storeManager,
            $transportBuilder,
            $rootCat,
            $shopifyCategoryMapRepository,
            $shopifyaccountsFactory,
            $shopifyAccountsRepository,
            $dbStorage,
            $importedTmpProductRepository,
            $product,
            $productRepository,
            $formkey,
            $saveProduct,
            $attributeFactory,
            $attrGroupCollection,
            $attrOptionCollectionFactory,
            $attributeManagement,
            $productAttribute,
            $registry,
            $filesystem,
            $configurableAttributeModel,
            $configurableProTypeModel,
            $templateProcessor,
            $indexerFactory,
            $encryptor,
            $cartRepositoryInterface,
            $cartManagementInterface,
            $shippingRate,
            $order,
            $customerRepository,
            $customerFactory,
            $backendSession,
            $countryHelper,
            $countryFactory,
            $curl,
            $jsonHelper,
            $priceRule,
            $listingTemplate,
            $logger,
            $file,
            $mphelper,
            $mpProductFactory,
            $date,
            $laminasUri,
            $attributeSetRepository,
            $orderStatuses
        );
    }

    /**
     * @return array|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getTopLevelMageCategory(): ?array
    {
        return $this->categoryTree->getCategoriesTree();
    }
}
