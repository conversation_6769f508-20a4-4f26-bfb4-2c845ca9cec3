<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tab\ShopifyAccount">
        <plugin name="generalAccountModifier" type="Comave\ShopifyAccountModifier\Plugin\ChangeAccount"/>
    </type>
    <type name="Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tab\GeneralConfiguration">
        <plugin name="generalConfigurationModifier" type="Comave\ShopifyAccountModifier\Plugin\ChangeGeneralConfiguration"/>
    </type>
    <type name="Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tab\ListingConfiguration">
        <plugin name="listingModifier" type="Comave\ShopifyAccountModifier\Plugin\ChangeListing"/>
    </type>
    <type name="Webkul\MpMultiShopifyStoreMageConnect\ViewModel\Category">
        <arguments>
            <argument xsi:type="object" name="helper">Comave\ShopifyAccountModifier\ViewModel\CategoryHelper</argument>
        </arguments>
    </type>
    <type name="Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tabs">
        <plugin name="tabsModifier" type="Comave\ShopifyAccountModifier\Plugin\ChangeTabs"/>
    </type>
    <type name="Webkul\MpMultiShopifyStoreMageConnect\Controller\Adminhtml\Categories\SaveMapping">
        <plugin name="saveDefaultCategory" type="Comave\ShopifyAccountModifier\Plugin\CheckDefaultCategory"/>
    </type>
</config>
