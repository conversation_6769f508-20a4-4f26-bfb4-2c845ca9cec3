<?php

declare(strict_types=1);

namespace Comave\ShopifyAccountModifier\Plugin;

use Magento\Framework\Data\Form;
use Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tab\GeneralConfiguration;

class ChangeGeneralConfiguration
{
    /**
     * @param GeneralConfiguration $generalTab
     * @param Form $form
     * @return array|null
     */
    public function beforeSetForm(
        GeneralConfiguration $generalTab,
        Form $form
    ): ?array {
        if (!$form->getElement('generalConfiguration_fieldset')) {
            return null;
        }

        $fieldSet = $form->getElement('generalConfiguration_fieldset');
        $defaultCategory = $fieldSet->getElements()->searchById('default_cate');
        $importSelector = $fieldSet->getElements()->searchById('import_product');
        $productWithHtml = $fieldSet->getElements()->searchById('item_with_html');
        $appliesForRule = $fieldSet->getElements()->searchById('price_rule_on');
        $orderWebhook = $fieldSet->getElements()->searchById('order_update_webhook');
        $productWebhook  = $fieldSet->getElements()->searchById('product_update_webhook');
        $conversionRate  = $fieldSet->getElements()->searchById('currency_conv_rate');
        $verifKey  = $fieldSet->getElements()->searchById('webhook_verify_key');

        if ($conversionRate !== false) {
            $fieldSet->removeField('currency_conv_rate');
            $fieldSet->addField(
                'currency_conv_rate',
                'hidden',
                [
                    'name' => 'currency_conv_rate',
                    'id' => 'currency_conv_rate',
                    'title' => __('Conversion Rate'),
                    'value' => 0,
                    'default' => 0,
                    'class' => 'required-entry validate-number validate-zero-or-greater',
                    'required' => true,
                ]
            );
        }

        if ($verifKey !== false) {
            $verifKey->setData(
                'note',
                __(
                    'The webhook verification key can be found in your Shopify store under Settings > Notifications (tab) > Webhooks',
                )
            );
        }

        if ($orderWebhook !== false) {
            $orderWebhook->setData(
                'note',
                __(
                    'This enables order updates incoming from shopify based on webhook configuration in Shopify, for more details see <a href="%1" target="_blank">Shopify Documentation</a>',
                    'https://help.shopify.com/en/manual/fulfillment/setup/notifications/webhooks'
                )
            );
        }

        if ($productWebhook !== false) {
            $productWebhook->setData(
                'note',
                __(
                    'This enables product updates incoming from shopify based on webhook configuration in Shopify, for more details see <a href="%1" target="_blank">Shopify Documentation</a>',
                    'https://help.shopify.com/en/manual/fulfillment/setup/notifications/webhooks'
                )
            );
        }

        if ($defaultCategory !== false) {
            $fieldSet->removeField('default_cate');
        }

        if ($importSelector !== false) {
            $fieldSet->removeField('import_product');
        }

        if ($productWithHtml !== false) {
            $fieldSet->removeField('item_with_html');
            $fieldSet->addField(
                'item_with_html',
                'hidden',
                [
                    'label' => __('Product Description With HTML'),
                    'title' => __('Product Description With HTML'),
                    'required' => true,
                    'index' => 'item_with_html',
                    'name' => 'item_with_html',
                    'default' => 1,
                    'value' => 1
                ]
            );
        }

        if ($appliesForRule !== false) {
            $appliesForRule->setData(
                'note',
                __('Commission is added ultimately in profile settings')
            );
        }

        return [$form];
    }
}
