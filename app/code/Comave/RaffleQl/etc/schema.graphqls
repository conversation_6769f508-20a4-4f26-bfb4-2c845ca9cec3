type Query {
    raffleEvents(
        pageSize: Int = 10
        currentPage: Int = 1
        filter: RaffleEventFilterInput
    ): RaffleEventOutput
    @doc(description: "Get list of raffle events")
    @resolver(class: "Comave\\RaffleQl\\Model\\Resolver\\RaffleEvents")

    customerTickets(
        event_id: ID @doc(description: "uid of the event")
        filter: CustomerTicketsFilterInput
        pageSize: Int = 10
        currentPage: Int = 1
    ): CustomerTicketsOutput
    @doc(description: "Get customer raffle tickets")
    @resolver(class: "Comave\\RaffleQl\\Model\\Resolver\\CustomerTickets")
}

type Mutation {
    purchaseRaffleTickets(
        event_id: ID @doc(description: "Raffle Event ID")
        quantity: Int = 1 @doc(description: "Number of tickets to purchase")
    ): PurchaseRaffleTicketsOutput @resolver(class: "Comave\\RaffleQl\\Model\\Resolver\\PurchaseRaffleTickets")

    claimRafflePrize(
        ticket_id: ID @doc(description: "Winning ticket ID")
        prize_id: ID @doc(description: "Prize ID to claim")
    ): ClaimPrizeOutput @resolver(class: "Comave\\RaffleQl\\Model\\Resolver\\ClaimRafflePrize")
}

type PurchaseRaffleTicketsOutput {
    tickets: [RaffleTicket]
    event: RaffleEvent
    total_cost: Float
    transaction_id: ID
}

type RaffleEventOutput {
    items: [RaffleEvent]
    total_count: Int
    page_info: SearchResultPageInfo
}

type RaffleEvent {
    uid: ID @doc(description: "unique id of the event")
    title: String @doc(description: "Raffle event title")
    description: String @doc(description: "Raffle event description. Can contain HTML tags.")
    start_date: String @doc(description: "Raffle event start date")
    end_date: String @doc(description: "Raffle event end date")
    status: Int @doc(description: "Raffle event status")
    status_label: String @doc(description: "Raffle event status label")
    total_tickets: Int @doc(description: "Total tickets")
    available_tickets: Int @doc(description: "Available tickets")
    image: String @doc(description: "URL image of the event if found")
    ticket_price: Float @doc(description: "Ticket price")
    created_at: String @doc(description: "Raffle event created date")
    updated_at: String @doc(description: "Raffle event updated date")
    prizes: [RafflePrize] @doc(description: "Raffle event prizes")
}

type RafflePrize {
    uid: ID @doc(description: "Prize ID")
    title: String @doc(description: "Prize title")
    description: String @doc(description: "Prize description")
    value: Float @doc(description: "Prize value")
    rank: Int @doc(description: "Prize rank")
}

input RaffleEventFilterInput {
    entity_id: FilterTypeInput @doc(description: "Raffle event ID")
    title: FilterTypeInput @doc(description: "Raffle event title")
    status: FilterTypeInput @doc(description: "Raffle event status")
    start_date: FilterTypeInput @doc(description: "Raffle event start date")
    end_date: FilterTypeInput @doc(description: "Raffle event end date")
}

type CustomerTicketsOutput {
    items: [RaffleTicket]
    total_count: Int
    page_info: SearchResultPageInfo
}

type RaffleTicket {
    uid: ID
    ticket_number: String
    status: Int
    status_label: String
    event: RaffleEvent
    purchased_at: String
    is_winner: Boolean
    prize: RafflePrize @doc(description: "Prize information if ticket is a winner")
}

input CustomerTicketsFilterInput {
    ticket_number: FilterTypeInput
    status: FilterTypeInput
    purchased_at: FilterTypeInput
    is_winner: Boolean
}

type ClaimPrizeOutput {
    success: Boolean
    message: String
    claim_details: PrizeClaim
}

type PrizeClaim {
    uid: ID
    status: String
    claim_date: String
    estimated_delivery: String
    tracking_number: String
}
