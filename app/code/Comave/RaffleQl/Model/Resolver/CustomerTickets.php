<?php

declare(strict_types=1);

namespace Comave\RaffleQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class CustomerTickets implements ResolverInterface
{
    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var \Magento\GraphQl\Model\Query\ContextInterface $context */
        // Check customer authentication
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized to view tickets.'));
        }

        return [
            'total_count' => 0,
            'items' => [],
            'page_info' => [
                'page_size' => $args['pageSize'] ?? 1,
                'current_page' => $args['currentPage'] ?? 1,
                'total_pages' => 0
            ]
        ];
    }
}
