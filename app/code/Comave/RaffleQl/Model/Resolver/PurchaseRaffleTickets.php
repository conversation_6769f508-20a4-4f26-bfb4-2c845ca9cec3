<?php

declare(strict_types=1);

namespace Comave\RaffleQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlAuthenticationException;

class PurchaseRaffleTickets implements ResolverInterface
{

    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlAuthenticationException
     * @throws GraphQlInputException
     * @throws LocalizedException
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array {
        /** @var \Magento\GraphQl\Model\Query\ContextInterface $context */
        if (!$context->getUserId()) {
            throw new GraphQlAuthenticationException(
                __('Customer must be authenticated to purchase tickets.')
            );
        }

        return [
            'tickets' => [],
            'event' => [],
            'total_cost' => 0,
            'transaction_id' => 'N/A'
        ];
    }
}
