<?php

declare(strict_types=1);

namespace Comave\RaffleQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Exception\GraphQlAuthenticationException;

class ClaimRafflePrize implements ResolverInterface
{
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array {
        /** @var \Magento\GraphQl\Model\Query\ContextInterface $context */
        if (!$context->getUserId()) {
            throw new GraphQlAuthenticationException(
                __('Customer must be authenticated to claim prizes.')
            );
        }

        return [
            'success' => true,
            'message' => '',
            'claim_details' => null
        ];
    }
}
