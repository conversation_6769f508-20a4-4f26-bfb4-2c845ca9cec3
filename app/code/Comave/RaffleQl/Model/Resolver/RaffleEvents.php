<?php
declare(strict_types=1);

namespace Comave\RaffleQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class RaffleEvents implements ResolverInterface
{
    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array {
        $totalCount = 0;

        return [
            'total_count' => $totalCount,
            'items' => [],
            'page_info' => [
                'page_size' => $args['pageSize'] ?? 1,
                'current_page' => $args['currentPage'] ?? 1,
                'total_pages' => 0
            ]
        ];
    }
}
