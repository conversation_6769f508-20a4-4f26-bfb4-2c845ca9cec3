<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">reasonmanagement_reason_listing.reasonmanagement_reason_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/add"/>
                <class>primary</class>
                <label translate="true">Create New Reason</label>
            </button>
        </buttons>
        <spinner>reasonmanagement_reason_listing_columns</spinner>
        <deps>
            <dep>reasonmanagement_reason_listing.reasonmanagement_reason_listing_data_source</dep>
        </deps>
    </settings>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <dataSource name="reasonmanagement_reason_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Comave_ReasonManagement::reasons</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="reasonmanagement_reason_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <columns name="reasonmanagement_reason_listing_columns">
        <column name="id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="code">
            <settings>
                <filter>text</filter>
                <label translate="true">Reason Code</label>
            </settings>
        </column>
        <column name="applies_to" component="Magento_Ui/js/grid/columns/select" sortOrder="50">
            <settings>
                <options class="Comave\ReasonManagement\Model\Option\EntityProvider"/>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Applies To</label>
            </settings>
        </column>
        <actionsColumn name="actions" class="Comave\ReasonManagement\Ui\Component\Listing\Column\ActionsColumn">
            <settings>
                <indexField>id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
