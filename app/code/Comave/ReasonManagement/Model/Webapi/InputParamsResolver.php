<?php

declare(strict_types=1);

namespace Comave\ReasonManagement\Model\Webapi;

use Comave\ReasonManagement\Model\Config\Converter;
use Comave\ReasonManagement\Model\ConfigProvider;
use Magento\Framework\Api\SimpleDataObjectConverter;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\AuthorizationException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Reflection\MethodsMap;
use Magento\Framework\Webapi\Exception;
use Magento\Framework\Webapi\ServiceInputProcessor;
use Magento\Framework\Webapi\Rest\Request as RestRequest;
use Magento\Framework\Webapi\Validator\EntityArrayValidator\InputArraySizeLimitValue;
use Magento\Webapi\Controller\Rest\ParamsOverrider;
use Magento\Webapi\Controller\Rest\RequestValidator;
use Magento\Webapi\Controller\Rest\Router;
use Magento\Webapi\Controller\Rest\Router\Route;

/**
 * This class is responsible for retrieving resolved input data
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class InputParamsResolver extends \Magento\Webapi\Controller\Rest\InputParamsResolver
{
    private ?Route $route = null;

    /**
     * @param ConfigProvider $configProvider
     * @param ReasonResolver $reasonResolver
     * @param RestRequest $request
     * @param ParamsOverrider $paramsOverrider
     * @param ServiceInputProcessor $serviceInputProcessor
     * @param Router $router
     * @param RequestValidator $requestValidator
     * @param MethodsMap|null $methodsMap
     * @param InputArraySizeLimitValue|null $inputArraySizeLimitValue
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly ReasonResolver $reasonResolver,
        private readonly RestRequest $request,
        private readonly ParamsOverrider $paramsOverrider,
        private readonly ServiceInputProcessor $serviceInputProcessor,
        private readonly Router $router,
        private readonly RequestValidator $requestValidator,
        private ?MethodsMap $methodsMap = null,
        private ?InputArraySizeLimitValue $inputArraySizeLimitValue = null
    ) {
        parent::__construct($request, $paramsOverrider, $serviceInputProcessor, $router, $requestValidator, $methodsMap, $inputArraySizeLimitValue);
        $this->methodsMap = $methodsMap ?: ObjectManager::getInstance()
            ->get(MethodsMap::class);
        $this->inputArraySizeLimitValue = $inputArraySizeLimitValue ?? ObjectManager::getInstance()
            ->get(InputArraySizeLimitValue::class);
    }

    /**
     * Process and resolve input parameters
     *
     * @return array
     * @throws Exception|AuthorizationException|LocalizedException
     */
    public function resolve()
    {
        $this->requestValidator->validate();
        $route = $this->getRoute();
        $this->inputArraySizeLimitValue->set($route->getInputArraySizeLimit());

        return $this->serviceInputProcessor->process(
            $route->getServiceClass(),
            $route->getServiceMethod(),
            $this->getInputData(),
        );
    }

    /**
     * Get API input data
     *
     * @return array
     * @throws InputException|Exception
     */
    public function getInputData()
    {
        $route = $this->getRoute();
        $serviceMethodName = $route->getServiceMethod();
        $serviceClassName = $route->getServiceClass();
        /*
         * Valid only for updates using PUT when passing id value both in URL and body
         */
        if ($this->request->getHttpMethod() == RestRequest::HTTP_METHOD_PUT) {
            $inputData = $this->paramsOverrider->overrideRequestBodyIdWithPathParam(
                $this->request->getParams(),
                $this->request->getBodyParams(),
                $serviceClassName,
                $serviceMethodName
            );
            $inputData = array_merge($inputData, $this->request->getParams());
            $inputData = $this->filterInputData($inputData);
        } else {
            $inputData = $this->request->getRequestData();
        }

        $this->validateParameters($serviceClassName, $serviceMethodName, $route->getParameters());

        $overriders = $route->getParameters();
        $this->unsetReasonParameters($overriders, true);

        return $this->paramsOverrider->override($inputData, $overriders);
    }

    /**
     * Validates InputData
     *
     * @param array $inputData
     * @return array
     */
    private function filterInputData(array $inputData): array
    {
        $result = [];

        $data = array_filter($inputData, function ($k) use (&$result) {
            $key = is_string($k) ? strtolower(str_replace('_', "", $k)) : $k;
            return !isset($result[$key]) && ($result[$key] = true);
        }, ARRAY_FILTER_USE_KEY);

        return array_map(function ($value) {
            return is_array($value) ? $this->filterInputData($value) : $value;
        }, $data);
    }

    /**
     * Retrieve current route.
     *
     * @return Route
     * @throws Exception
     */
    public function getRoute()
    {
        if (!$this->route) {
            $this->route = $this->router->match($this->request);
        }

        return $this->route;
    }

    /**
     * Validate that parameters are really used in the current request.
     *
     * @param string $serviceClassName
     * @param string $serviceMethodName
     * @param array $paramOverriders
     */
    private function validateParameters(
        string $serviceClassName,
        string $serviceMethodName,
        array $paramOverriders
    ): void {
        $originalParamOverriders = array_keys($paramOverriders);
        $methodParams = $this->methodsMap->getMethodParams($serviceClassName, $serviceMethodName);
        foreach ($originalParamOverriders as $key => $param) {
            $arrayKeys = explode('.', $param ?? '');
            $value = array_shift($arrayKeys);

            if (in_array($value, [Converter::KEY_WITH_REASON_PARAM, Converter::KEY_REASON_ENTITY])) {
                if (!$this->configProvider->isEnabled()) {
                    $this->unsetReasonParameters($originalParamOverriders);

                    continue;
                }

                if (
                    $paramOverriders[Converter::KEY_WITH_REASON_PARAM] === 'false' ||
                    empty($paramOverriders[Converter::KEY_WITH_REASON_PARAM])
                ) {
                    $this->unsetReasonParameters($originalParamOverriders);
                    continue;
                }

                if (
                    empty($paramOverriders[Converter::KEY_REASON_ENTITY]) ||
                    !interface_exists($paramOverriders[Converter::KEY_REASON_ENTITY])
                ) {
                    throw new \UnexpectedValueException(__('Invalid endpoint configuration, missing/invalid reason referenced entity')->__toString());
                }

                $reasonHeader = $this->request->getHeader(ReasonResolver::REQUEST_REASON_HEADER);

                if (empty($reasonHeader)) {
                    throw new Exception(
                        __('Invalid request, reason header is missing'),
                        Exception::HTTP_BAD_REQUEST
                    );
                }

                $this->reasonResolver->execute(
                    $paramOverriders[Converter::KEY_REASON_ENTITY],
                    $this->request
                );
                $this->unsetReasonParameters($originalParamOverriders);

                continue;
            }

            foreach ($methodParams as $serviceMethodParam) {
                $serviceMethodParamName = $serviceMethodParam[MethodsMap::METHOD_META_NAME];
                $serviceMethodType = $serviceMethodParam[MethodsMap::METHOD_META_TYPE];

                $camelCaseValue = SimpleDataObjectConverter::snakeCaseToCamelCase($value);
                if ($serviceMethodParamName === $value || $serviceMethodParamName === $camelCaseValue) {
                    if (count($arrayKeys) > 0) {
                        $camelCaseKey = SimpleDataObjectConverter::snakeCaseToCamelCase('set_' . $arrayKeys[0]);
                        $this->validateParameters($serviceMethodType, $camelCaseKey, [implode('.', $arrayKeys)]);
                    }
                    unset($originalParamOverriders[$key]);
                    break;
                }
            }
        }

        if (!empty($originalParamOverriders)) {
            $message = 'The current request does not expect the next parameters: '
                . implode(', ', $originalParamOverriders);
            throw new \UnexpectedValueException(__($message)->__toString());
        }
    }

    /**
     * @param array $parameters
     * @param bool $byKeys
     * @return void
     */
    private function unsetReasonParameters(array &$parameters, bool $byKeys = false): void
    {
        if ($byKeys === true) {
            if (isset($parameters[Converter::KEY_WITH_REASON_PARAM])) {
                unset($parameters[Converter::KEY_WITH_REASON_PARAM]);
            }

            if (isset($parameters[Converter::KEY_REASON_ENTITY])) {
                unset($parameters[Converter::KEY_REASON_ENTITY]);
            }

            return;
        }

        $withParamIndex = array_search(
            Converter::KEY_WITH_REASON_PARAM,
            $parameters
        );

        if ($withParamIndex >= 0) {
            unset($parameters[$withParamIndex]);
        }

        $reasonEntityParam = array_search(
            Converter::KEY_REASON_ENTITY,
            $parameters
        );

        if ($reasonEntityParam >= 0) {
            unset($parameters[$reasonEntityParam]);
        }
    }
}
