<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model;

use Comave\Sales\Api\PostProcessorInterface;

class ProcessorTypeManager
{
    public const string REWARD_TYPE_PROCESSOR = 'reward_type_processor';
    public const string SELLER_EMAIL_TYPE_PROCESSOR = 'seller_email_type_processor';

    /**
     * @param \Comave\Sales\Model\ProcessorTypeFactory $processorTypeFactory
     * @param array $processorTypes
     */
    public function __construct(
        private readonly ProcessorTypeFactory $processorTypeFactory,
        private readonly array $processorTypes = []
    ) {
    }

    /**
     * @param string $processingType
     * @return \Comave\Sales\Api\PostProcessorInterface|null
     */
    public function get(string $processingType): ?PostProcessorInterface
    {
        $processor = null;
        if (!empty($this->processorTypes[$processingType])) {
            $processor = $this->processorTypeFactory->create(
                $this->processorTypes[$processingType]
            );
        }

        return $processor;
    }
}