<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Service\Reward\Points;

use Comave\Sales\Api\RewardTypeInterface;
use Comave\Sales\Model\RewardTypeFactory;
use Exception;
use Psr\Log\LoggerInterface;

class Calculator
{
    public function __construct(
        private readonly RewardTypeFactory $rewardTypeFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param float $amount
     * @param string $rewardType
     * @return int
     */
    public function calculate(float $amount, string $rewardType = RewardTypeInterface::TYPE_LIX): int
    {
        try {
            return $this->rewardTypeFactory->create($rewardType)->getPoints($amount);
        } catch (Exception $exception) {
            $this->logger->warning(sprintf('Cannot calculate %s Reward Type points', $rewardType), [
                'exception' => $exception->getMessage(),
                'rewardType' => $rewardType,
                'amount' => $amount,
            ]);

            return 0;
        }
    }
}