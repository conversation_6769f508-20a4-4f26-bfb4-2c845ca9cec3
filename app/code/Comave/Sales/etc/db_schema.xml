<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="sales_order_notification_history" resource="default" engine="innodb"
           comment="Sales Order Notification History">
        <column xsi:type="int" name="notification_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Notification ID"/>
        <column xsi:type="int" name="order_id" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Order ID"/>
        <column xsi:type="text" name="message" nullable="true" comment="Notification Message"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created at"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="notification_id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="SALES_ORDER_NOTIFICATION_ID_SALES_ORDER_ENTITY_ID"
                    table="sales_order_notification_history" column="order_id"
                    referenceTable="sales_order" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
        <index referenceId="SALES_ORDER_NOTIFICATION_HISTORY_ORDER_ID_INDEX" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>
    <table name="comave_seller_order_tracking" resource="default" engine="innodb"
           comment="Comave Seller Order Tracking">
        <column xsi:type="int" name="tracking_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Tracking ID"/>
        <column xsi:type="int" name="order_id" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Order ID"/>
        <column xsi:type="int" name="seller_id" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Seller ID"/>
        <column xsi:type="varchar" name="tracking_number" nullable="false" length="255" comment="Tracking Number"/>
        <column xsi:type="varchar" name="tracking_status" nullable="false" length="255" comment="Tracking Status"/>
        <column xsi:type="timestamp" name="date" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="json" name="payload" nullable="false" comment="Payload"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created at"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="tracking_id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_SELLER_ORDER_TRACKING_ORDER_ID_SALES_ORDER_ENTITY_ID"
                    table="comave_seller_order_tracking" column="order_id"
                    referenceTable="sales_order" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
        <index referenceId="COMAVE_SELLER_ORDER_TRACKING_SELLER_ID" indexType="btree">
            <column name="seller_id"/>
        </index>
        <index referenceId="COMAVE_SELLER_ORDER_TRACKING_ORDER_ID" indexType="btree">
            <column name="order_id"/>
        </index>
        <constraint xsi:type="unique" referenceId="COMAVE_SELLER_ORDER_TRACKING_SELLER_ORDER_TRACKING_CONSTRAINT">
            <column name="seller_id"/>
            <column name="order_id"/>
            <column name="tracking_number"/>
        </constraint>
    </table>
</schema>
