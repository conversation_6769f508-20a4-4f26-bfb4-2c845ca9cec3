<?php

declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Magento\Framework\App\RequestInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Controller\Adminhtml\Order\AddComment;
use Magento\Framework\Message\ManagerInterface;

class AllowDeliveredStatus
{
    public const string STATUS_DELIVERED = 'delivered';

    public function __construct(
        private readonly RequestInterface $request,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly ManagerInterface $messageManager,
    ) {}

    public function aroundExecute(AddComment $subject, \Closure $proceed)
    {
        $orderId = (int) $this->request->getParam('order_id');
        $postData = $this->request->getPostValue();

        if (!$orderId
            || !isset($postData['history']['status'])
            || $postData['history']['status'] !== self::STATUS_DELIVERED
        ) {
            return $proceed();
        }

        try {
            $order = $this->orderRepository->get($orderId);
            $requestedStatus = $postData['history']['status'];
            $comment = $postData['history']['comment'] ?? '';

            $order->setStatus($requestedStatus);
            $order->setState('complete');

            $this->orderRepository->save($order);
            $this->messageManager->addSuccessMessage('Order status changed to Delivered.');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage('Failed to change order status to Delivered.');
        }

        return $proceed();
    }
}
