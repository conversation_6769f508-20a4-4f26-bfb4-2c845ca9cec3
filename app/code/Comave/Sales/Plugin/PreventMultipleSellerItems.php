<?php

declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Comave\SellerApi\Service\GetSellerProductMapping;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Model\Quote;

class PreventMultipleSellerItems
{
    /**
     * @param GetSellerProductMapping $getSellerProductMapping
     */
    public function __construct(
        private readonly GetSellerProductMapping $getSellerProductMapping
    ) {
    }

    /**
     * @param Quote $quote
     * @param \Magento\Catalog\Model\Product $product
     * @param mixed|null $request
     * @param string $processMode
     * @return array|null
     * @throws LocalizedException
     */
    public function beforeAddProduct(
        Quote $quote,
        \Magento\Catalog\Model\Product $product,
        mixed $request = null,
        ?string $processMode = \Magento\Catalog\Model\Product\Type\AbstractType::PROCESS_MODE_FULL
    ): ?array {
        if (empty($quote->getAllVisibleItems())) {
            return null;
        }

        $productIds = [$product->getId()];

        foreach ($quote->getAllVisibleItems() as $item) {
            $productIds[] = $item->getProduct()->getId();
        }

        $sellerMapping = $this->getSellerProductMapping->get(
            array_unique($productIds)
        );

        if (count($sellerMapping) <= 1) {
            return null;
        }

        throw new LocalizedException(
            __('You cannot add products from multiple sellers to the cart. Please remove the first product(s) first before continuing.')
        );
    }
}
