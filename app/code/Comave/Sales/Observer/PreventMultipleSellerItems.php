<?php

declare(strict_types=1);

namespace Comave\Sales\Observer;

use Comave\SellerApi\Service\GetSellerProductMapping;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Model\Quote;

class PreventMultipleSellerItems implements ObserverInterface
{
    /**
     * @param GetSellerProductMapping $getSellerProductMapping
     */
    public function __construct(private readonly GetSellerProductMapping $getSellerProductMapping)
    {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws LocalizedException
     */
    public function execute(Observer $observer): void
    {
        /** @var Quote $quote */
        $quote = $observer->getQuote();
        $productIds = [];

        foreach ($quote->getAllVisibleItems() as $item) {
            $productIds[] = $item->getProduct()->getId();
        }

        $sellerMapping = $this->getSellerProductMapping->get(
            array_unique($productIds)
        );

        if (count($sellerMapping) <= 1) {
            return;
        }

        throw new LocalizedException(
            __('You cannot add products from multiple sellers to the cart. Please remove the first item(s) before continuing.')
        );
    }
}
