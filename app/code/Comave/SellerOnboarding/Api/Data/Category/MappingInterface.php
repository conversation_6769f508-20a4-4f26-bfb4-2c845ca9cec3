<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Api\Data\Category;

use Magento\Framework\Api\ExtensibleDataInterface;

interface MappingInterface extends ExtensibleDataInterface
{
    public const int MAPPING_TYPE_AUTO = 0;
    public const int MAPPING_TYPE_MANUAL = 1;

    public const string MAPPING_ID = 'mapping_id';
    public const string MAPPING_SOURCE_ID = 'mapping_source_id';
    public const string MAPPING_CATEGORY_ID = 'mapping_category_id';
    public const string MAPPING_ACCURACY = 'mapping_accuracy';
    public const string MAPPING_TYPE = 'mapping_type';
    public const string TAX_CLASS = 'tax_class';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingId(int $id): MappingInterface;

    /**
     * Get Mapping ID
     *
     * @return int
     */
    public function getMappingId(): int;

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingSourceId(int $id): MappingInterface;

    /**
     * Get Mapping Source ID
     *
     * @return int
     */
    public function getMappingSourceId(): int;

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingCategoryId(int $id): MappingInterface;

    /**
     * Get Mapping Category ID
     *
     * @return int
     */
    public function getMappingCategoryId(): int;

    /**
     * @param float $accuracy
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingAccuracy(float $accuracy): MappingInterface;

    /**
     * Get Mapping Accuracy
     *
     * @return float
     */
    public function getMappingAccuracy(): float;

    /**
     * @param int $type
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingType(int $type): MappingInterface;

    /**
     * Get Mapping Type
     *
     * @return int
     */
    public function getMappingType(): int;

    /**
     * @return string|null
     */
    public function getTaxClass(): ?string;

    /**
     * @param string|null $taxClass
     * @return MappingInterface
     */
    public function setTaxClass(?string $taxClass): MappingInterface;
}
