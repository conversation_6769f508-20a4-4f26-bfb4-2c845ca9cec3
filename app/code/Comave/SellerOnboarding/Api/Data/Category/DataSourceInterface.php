<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Api\Data\Category;

use Magento\Framework\Api\ExtensibleDataInterface;

interface DataSourceInterface extends ExtensibleDataInterface
{
    public const string SOURCE_ID = 'source_id';
    public const string SELLER_ID = 'seller_id';
    public const string SOURCE_CATEGORY_ID = 'source_category_id';
    public const string SOURCE_CATEGORY_NAME = 'source_category_name';
    public const string SOURCE_METADATA = 'source_metadata';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setSourceId($id);

    /**
     * Get Source ID
     *
     * @return int
     */
    public function getSourceId();

    /**
     * Set Seller
     *
     * @param int $sellerId
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setSellerId($sellerId);

    /**
     * Get Seller ID
     *
     * @return int
     */
    public function getSellerId();

    /**
     * @param string $sourceCategoryId
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setSourceCategoryId($sourceCategoryId);

    /**
     * Get Source Category ID
     *
     * @return string
     */
    public function getSourceCategoryId();

    /**
     * @param string $sourceCategoryName
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setSourceCategoryName($sourceCategoryName);

    /**
     * Get Source Category Name
     *
     * @return string
     */
    public function getSourceCategoryName();

    /**
     * @param string $sourceMetadata
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface
     */
    public function setSourceMetadata($sourceMetadata);

    /**
     * Get Source Metadata
     *
     * @return string
     */
    public function getSourceMetadata();
}
