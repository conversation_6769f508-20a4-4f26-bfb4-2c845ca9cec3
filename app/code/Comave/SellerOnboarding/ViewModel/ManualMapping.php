<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\ViewModel;

use <PERSON>ma<PERSON>\SellerOnboarding\Api\Data\Category\DataSourceInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Comave\SellerOnboarding\Model\Command\BuildCategoryTree;
use Magento\Catalog\Ui\Component\Product\Form\Categories\Options;
use Magento\Framework\DB\Select;
use Magento\Framework\Stdlib\ArrayUtils;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Catalog\Model\ResourceModel\Category\Collection as MappingCollection;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as MappingCollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller\Collection;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;

class ManualMapping implements ArgumentInterface
{
    /**
     * @param BuildCategoryTree $buildCategoryTree
     * @param CollectionFactory $sellerCollectionFactory
     * @param MappingCollectionFactory $mappingCollectionFactory
     */
    public function __construct(
        private readonly BuildCategoryTree $buildCategoryTree,
        private readonly CollectionFactory $sellerCollectionFactory,
        private readonly MappingCollectionFactory $mappingCollectionFactory,
    ) {
    }

    /**
     * @return array
     */
    public function getSellerList(): array
    {
        /** @var Collection $collection */
        $collection = $this->sellerCollectionFactory->create();
        $collection->getSelect()->reset(Select::COLUMNS);
        $collection->getSelect()
            ->join(
                ['c' => $collection->getTable('customer_entity')],
                'c.entity_id = main_table.seller_id',
                [
                    'entity_id',
                    'firstname',
                    'lastname'
                ]
            )->group('seller_id');

        return $collection->getItems();
    }

    /**
     * @param string $sellerId
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCategoryMapping(string $sellerId): array
    {
        /** @var MappingCollection $collection */
        $collection = $this->mappingCollectionFactory->create();
        $collection->getSelect()->reset(Select::COLUMNS);
        $collection->getSelect()
            ->join(
                ['sm' => $collection->getTable('comave_seller_onboarding_category_mapping')],
                'sm.mapping_category_id = e.entity_id',
                []
            )->join(
                ['s' => $collection->getTable('comave_seller_onboarding_category_data_source')],
                's.source_id = sm.mapping_source_id',
                []
            )->where(
                'seller_id = ?',
                $sellerId
            );

        $collection->getSelect()
            ->columns([
                's.' . DataSourceInterface::SOURCE_ID,
                's.' . DataSourceInterface::SOURCE_CATEGORY_NAME,
                'sm.' . MappingInterface::MAPPING_ACCURACY,
                'sm.' . MappingInterface::MAPPING_ID,
                'sm.' . MappingInterface::MAPPING_TYPE,
                'sm.' . MappingInterface::MAPPING_CATEGORY_ID,
                'path' => new \Zend_Db_Expr('REPLACE(path, \'1/2/\', \'\')')
            ]);
        $collection->addAttributeToSelect(['name'], 'inner');

        if (!$collection->getSize()) {
            return [];
        }

        $result = [];

        foreach ($collection->getItems() as $mapping) {
            $mappingSourceId = $mapping->getData(DataSourceInterface::SOURCE_ID);

            if (!isset($result[$mappingSourceId])) {
                $result[$mappingSourceId] = [];
            }

            $result[$mappingSourceId][] = $mapping;
        }

        return $result;
    }

    /**
     * @param string $path
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCategoryTreeBreadcrumb(string $path): string
    {
        return $this->buildCategoryTree->execute($path);
    }
}
