<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Model\Category;

use Comave\SellerOnboarding\Api\Category\DataSourceListRepositoryInterface;
use Comave\SellerOnboarding\Api\Category\DataSourceRepositoryInterface;
use Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class DataSourceUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\SellerOnboarding\Api\Category\DataSourceListRepositoryInterface $listRepository
     * @param \Comave\SellerOnboarding\Api\Category\DataSourceRepositoryInterface $repository
     * @param \Comave\SellerOnboarding\Model\Category\DataSourceFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly DataSourceListRepositoryInterface $listRepository,
        private readonly DataSourceRepositoryInterface $repository,
        private readonly DataSourceFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $dataSource
     * @return void
     */
    public function save(AbstractModel $dataSource)
    {
        $this->repository->save($dataSource);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|string $sourceCategoryId
     * @param int $sellerId
     * @return \Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getBySellerId(int|string $sourceCategoryId, int $sellerId): ?DataSourceInterface
    {
        if (!$sellerId) {
            return $this->get(null);
        }

        $sourceId = null;
        $sources = $this->listRepository->getList(
            $this->searchCriteriaBuilder
                ->addFilter(DataSourceInterface::SELLER_ID, $sellerId)
                ->addFilter(DataSourceInterface::SOURCE_CATEGORY_ID, $sourceCategoryId)
                ->create()
        )->getItems();
        foreach ($sources as $dataSource) {
            $sourceId = (int)$dataSource->getSourceId();
            break;
        }

        return $this->get($sourceId);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | DataSource | DataSourceInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }
}
