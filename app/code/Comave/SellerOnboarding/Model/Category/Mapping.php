<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Model\Category;

use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Comave\SellerOnboarding\Model\ResourceModel\Category\Mapping as CategoryMappingResourceModel;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Mapping extends AbstractExtensibleModel implements MappingInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'seller_category_mapping';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'seller_category_mapping';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'seller_category_mapping';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\SellerOnboarding\Model\Category\Mapping
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Mapping
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingId(int $id): MappingInterface
    {
        return $this->setData(self::MAPPING_ID, $id);
    }

    /**
     * @return int
     */
    public function getMappingId(): int
    {
        return (int)$this->getData(self::MAPPING_ID);
    }

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingSourceId(int $id): MappingInterface
    {
        return $this->setData(self::MAPPING_SOURCE_ID, $id);
    }

    /**
     * Get Mapping Source ID
     *
     * @return int
     */
    public function getMappingSourceId(): int
    {
        return (int)$this->getData(self::MAPPING_SOURCE_ID);
    }

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingCategoryId(int $id): MappingInterface
    {
        return $this->setData(self::MAPPING_CATEGORY_ID, $id);
    }

    /**
     * Get Mapping Category ID
     *
     * @return int
     */
    public function getMappingCategoryId(): int
    {
        return (int)$this->getData(self::MAPPING_CATEGORY_ID);
    }

    /**
     * @param float $accuracy
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingAccuracy(float $accuracy): MappingInterface
    {
        return $this->setData(self::MAPPING_ACCURACY, $accuracy);
    }

    /**
     * Get Mapping Accuracy
     *
     * @return float
     */
    public function getMappingAccuracy(): float
    {
        return (float)$this->getData(self::MAPPING_ACCURACY);
    }

    /**
     * @param int $type
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setMappingType(int $type): MappingInterface
    {
        return $this->setData(self::MAPPING_TYPE, $type);
    }

    /**
     * Get Mapping Type
     *
     * @return int
     */
    public function getMappingType(): int
    {
        return (int)$this->getData(self::MAPPING_TYPE);
    }

    /**
     * @return string|null
     */
    public function getTaxClass(): ?string
    {
        return $this->getData(self::TAX_CLASS);
    }

    /**
     * @param string|null $taxClass
     * @return MappingInterface
     */
    public function setTaxClass(?string $taxClass): MappingInterface
    {
        return $this->setData(self::TAX_CLASS, $taxClass);
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(CategoryMappingResourceModel::class);
    }
}
