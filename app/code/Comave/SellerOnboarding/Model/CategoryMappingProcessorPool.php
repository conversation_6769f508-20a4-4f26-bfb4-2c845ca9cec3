<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Model;

use Comave\SellerOnboarding\Api\CategoryProcessorInterface;
use Magento\Framework\Exception\LocalizedException;

class CategoryMappingProcessorPool
{
    /**
     * @param CategoryProcessorInterface[] $processors
     */
    public function __construct(private readonly array $processors = [])
    {
    }

    /**
     * @param string $integrationType
     * @param string $sellerId
     * @return void
     * @throws \Comave\SellerApi\Exception\UnknownIntegrationException
     */
    public function processCategories(string $integrationType, string $sellerId): void
    {
        if (!isset($this->processors[$integrationType]) || !$this->processors[$integrationType] instanceof CategoryProcessorInterface) {
            throw new \Comave\SellerApi\Exception\UnknownIntegrationException(
                __('Unable to process category mapping, integration process not found')
            );
        }

        $this->processors[$integrationType]->processCategories($sellerId);
    }
}
