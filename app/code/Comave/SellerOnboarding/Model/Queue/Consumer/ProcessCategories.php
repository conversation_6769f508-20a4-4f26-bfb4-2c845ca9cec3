<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Model\Queue\Consumer;

use Comave\SellerApi\Model\IntegrationTypePool;
use Comave\SellerOnboarding\Model\CategoryMappingProcessorPool;

class ProcessCategories
{
    public const string TOPIC_NAME = 'seller.process.category.mapping';

    /**
     * @param IntegrationTypePool $integrationTypePool
     * @param CategoryMappingProcessorPool $categoryMappingProcessorPool
     */
    public function __construct(
        private readonly IntegrationTypePool $integrationTypePool,
        private readonly CategoryMappingProcessorPool $categoryMappingProcessorPool
    ) {
    }

    /**
     * @param string $sellerId
     * @return void
     * @throws \Comave\SellerApi\Exception\UnknownIntegrationException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(string $sellerId): void
    {
        $integration = $this->integrationTypePool->identifyBySeller($sellerId);
        $this->categoryMappingProcessorPool->processCategories(
            $integration->getIntegrationType(),
            $sellerId
        );
    }
}
