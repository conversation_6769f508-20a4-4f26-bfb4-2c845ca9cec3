<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Ui\Component\Listing\Column\Category\Mapping;

use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Actions extends Column
{
    /**
     * Url path to edit
     * @var string
     */
    const string URL_PATH_EDIT = 'seller_onboarding/category_mapping/edit';

    /**
     * Url path to delete
     * @var string
     */
    const string URL_PATH_DELETE = 'seller_onboarding/category_mapping/delete';

    /**
     * URL Path to properly assign categories
     * @var string
     */
    const string URL_PATH_ASSIGN = 'seller_onboarding/category_mapping/assign';

    /**
     * Url builder
     * @var UrlInterface
     */
    protected UrlInterface $urlBuilder;

    /**
     * @param \Magento\Framework\View\Element\UiComponent\ContextInterface $context
     * @param \Magento\Framework\View\Element\UiComponentFactory $uiComponentFactory
     * @param \Magento\Framework\UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (empty($dataSource['data']['items'])) {
            return $dataSource;
        }

        foreach ($dataSource['data']['items'] as & $item) {
            if (isset($item['mapping_id'])) {
                $item[$this->getData('name')] = [
                    'edit' => [
                        'href' => $this->urlBuilder->getUrl(
                            self::URL_PATH_EDIT,
                            [
                                'mapping_id' => $item['mapping_id'],
                            ]
                        ),
                        'label' => __('Edit')->render(),
                    ],
                    'delete' => [
                        'href' => $this->urlBuilder->getUrl(
                            static::URL_PATH_DELETE,
                            [
                                'mapping_id' => $item['mapping_id'],
                            ]
                        ),
                        'label' => __('Delete'),
                        'confirm' => [
                            'title' => __('Delete'),
                            'message' => __(
                                'Are you sure you want to delete this category mapping?'
                            ),
                        ],
                    ],
                ];

                if ((int) $item[MappingInterface::MAPPING_TYPE] !== MappingInterface::MAPPING_TYPE_AUTO) {
                    continue;
                }

                $item[$this->getData('name')]['assign_category'] = [
                    'href' => $this->urlBuilder->getUrl(
                        static::URL_PATH_ASSIGN,
                        [
                            'mapping_id' => $item['mapping_id'],
                        ]
                    ),
                    'label' => __('Assign Category'),
                    'confirm' => [
                        'title' => __('Assign'),
                        'message' => __(
                            'Are you sure you want to overwrite and assign this category mapping?'
                        ),
                    ],
                ];
            }
        }

        return $dataSource;
    }
}
