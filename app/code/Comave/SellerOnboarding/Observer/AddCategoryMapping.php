<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Observer;

use Comave\SellerOnboarding\Service\Category\Mapping;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class AddCategoryMapping implements ObserverInterface
{
    /**
     * @param Mapping $mappingService
     */
    public function __construct(private readonly Mapping $mappingService)
    {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        if (empty($observer->getMappingList())) {
            return;
        }


        $this->mappingService->insertMultiple($observer->getMappingList());
    }
}
