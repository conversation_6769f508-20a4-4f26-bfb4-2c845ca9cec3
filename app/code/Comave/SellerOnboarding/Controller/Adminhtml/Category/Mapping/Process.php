<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Controller\Adminhtml\Category\Mapping;

use Comave\SellerOnboarding\Model\Queue\Consumer\ProcessCategories;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\MessageQueue\PublisherInterface;

class Process extends Action implements HttpGetActionInterface
{
    /**
     * @param Context $context
     * @param PublisherInterface $publisher
     */
    public function __construct(
        Context $context,
        private readonly PublisherInterface $publisher
    ) {
        parent::__construct($context);
    }

    /**
     * @return Redirect
     */
    public function execute(): Redirect
    {
        $redirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        $this->publisher->publish(
            ProcessCategories::TOPIC_NAME,
            $this->getRequest()->getParam('seller_id')
        );

        $this->getMessageManager()->addSuccessMessage(
            __('Seller product categories will be processed shortly')
        );

        return $redirect->setRefererUrl();
    }
}
