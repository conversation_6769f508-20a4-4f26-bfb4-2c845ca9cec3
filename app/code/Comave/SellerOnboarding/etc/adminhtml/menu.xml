<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Comave_SellerOnboarding::menu"
             title="Comave Seller Onboarding"
             module="Comave_SellerOnboarding"
             sortOrder="140"
             parent="Webkul_Marketplace::marketplace"
             resource="Comave_SellerOnboarding::menu"
        />
        <add id="Comave_SellerOnboarding::category_source_list"
             title="Seller Category List"
             module="Comave_SellerOnboarding"
             sortOrder="10"
             action="seller_onboarding/category_source/"
             resource="Comave_SellerOnboarding::category_source_list"
             parent="Comave_SellerOnboarding::menu"
        />
        <add id="Comave_SellerOnboarding::category_mapping_list"
             title="Seller Category Mapping List"
             module="Comave_SellerOnboarding"
             sortOrder="20"
             action="seller_onboarding/category_mapping/"
             resource="Comave_SellerOnboarding::category_mapping_list"
             parent="Comave_SellerOnboarding::menu"
        />
        <add id="Comave_SellerOnboarding::category_mapping_list_manual"
             title="Seller Category Manual Mapping"
             module="Comave_SellerOnboarding"
             sortOrder="30"
             action="seller_onboarding/category_mapping/manual"
             resource="Comave_SellerOnboarding::category_mapping_list"
             parent="Comave_SellerOnboarding::menu"
        />
    </menu>
</config>
