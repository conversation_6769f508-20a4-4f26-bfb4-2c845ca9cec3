<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="seller.process.category.mapping" connection="amqp" type="topic">
        <binding id="sellerProcessCategoryMapping"
                 topic="seller.process.category.mapping"
                 destinationType="queue"
                 destination="seller.process.category.mapping"/>
    </exchange>
</config>
