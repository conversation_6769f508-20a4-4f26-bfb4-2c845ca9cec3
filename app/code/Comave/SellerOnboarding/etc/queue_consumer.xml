<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="sellerProcessCategoryMapping"
              queue="seller.process.category.mapping"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\SellerOnboarding\Model\Queue\Consumer\ProcessCategories::execute"/>
</config>
