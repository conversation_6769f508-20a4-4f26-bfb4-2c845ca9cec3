<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" src_type="url"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block name="comave.form.container" template="Comave_SellerOnboarding::main_form.phtml">
                <arguments>
                    <argument xsi:type="object" name="manualViewModel">Comave\SellerOnboarding\ViewModel\ManualMapping</argument>
                </arguments>
                <block name="comave.mapping.manual.seller" template="Comave_SellerOnboarding::seller_list.phtml"/>
                <block name="comave.mapping.manual.seller.categories" template="Comave_SellerOnboarding::category_list.phtml"/>
            </block>
        </referenceContainer>
    </body>
</page>
