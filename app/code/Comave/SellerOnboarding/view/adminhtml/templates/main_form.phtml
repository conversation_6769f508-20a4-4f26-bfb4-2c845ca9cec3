<?php
/** @var \Magento\Framework\View\Element\Template $block */
?>
<?php if ($block->getRequest()->getParam('seller')): ?>
    <div class="page-main-actions">
        <div class="page-actions-placeholder"></div>
        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
            <div class="page-actions-inner" data-title="Process Products">
                <div class="page-actions-buttons">
                    <button onclick="window.location.href = '<?= $block->getUrl('*/*/process', ['seller_id' => $block->getRequest()->getParam('seller')]); ?>';"
                            id="add"
                            title="Process Products"
                            type="button"
                            class="action- scalable primary">
                        <span><?= /** @noEscape */ __('Process products'); ?></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="entry-edit form-inline">
    <?= $block->getChildHtml('comave.mapping.manual.seller'); ?>
    <?php if ($block->getRequest()->getParam('seller')): ?>
        <?= /** @noEscape  */ $block->getChildHtml('comave.mapping.manual.seller.categories'); ?>
    <?php endif; ?>
</div>
