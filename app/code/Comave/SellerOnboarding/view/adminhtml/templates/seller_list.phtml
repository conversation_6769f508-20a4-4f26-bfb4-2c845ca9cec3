<?php
/** @var \Magento\Framework\View\Element\Template $block */
$selectedSeller = $block->getRequest()->getParam('seller');
/** @var \Comave\SellerOnboarding\ViewModel\ManualMapping $viewModel */
$viewModel = $block->getParentBlock()->getData('manualViewModel');
$sellerList = $viewModel->getSellerList();
?>
<div class="fieldset-wrapper _show">
    <div class="admin__fieldset-wrapper-content _show">
        <fieldset class="admin__fieldset">
            <div class="admin__field">
                <div class="admin__field-label">
                    <label for="seller_list"><?= __('Choose a seller'); ?></label>
                </div>
                <div class="admin__field-control control">
                    <select name="seller" id="seller_list" class="required-entry _required select admin__control-select">
                        <option value="">Select a seller</option>
                        <?php foreach ($sellerList as $seller): ?>
                            <option <?= $selectedSeller === $seller->getId() ? 'selected="selected"' : ''; ?> value="<?= /** @noEscape  */ $seller->getId(); ?>">
                                <?= /** @noEscape  */$seller->getFirstname() . ' ' . $seller->getLastname(); ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
        </fieldset>
    </div>
</div>
<script>
    require([
        'jquery',
        'domReady!'
    ], function ($) {
        'use strict';

        $(document).on('change', '#seller_list', function () {
            const baseUrl = window.location.href.replace(/\/seller\/\d+/g, "");
            window.location.href = baseUrl.replace(/\/$/, '') + '/seller/' + $(this).val();
        });
    });
</script>
