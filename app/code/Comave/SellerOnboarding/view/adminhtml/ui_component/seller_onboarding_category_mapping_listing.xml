<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">seller_onboarding_category_mapping_listing.seller_category_mapping_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">New Category Mapping</label>
            </button>
        </buttons>
        <spinner>seller_category_mapping_columns</spinner>
        <deps>
            <dep>seller_onboarding_category_mapping_listing.seller_category_mapping_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="seller_category_mapping_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">mapping_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="seller_category_mapping_listing_data_source">
            <settings>
                <requestFieldName>mapping_id</requestFieldName>
                <primaryFieldName>mapping_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <exportButton name="export_button" />
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect
                    name="seller_id"
                    provider="${ $.parentName }"
                    sortOrder="10"
                    component="Magento_Ui/js/grid/filters/elements/ui-select"
                    template="ui/grid/filters/elements/ui-select">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="options" xsi:type="array"></item>
                        <item name="filterOptions" xsi:type="boolean">true</item>
                        <item name="searchOptions" xsi:type="boolean">true</item>
                        <item name="filterPlaceholder" xsi:type="string" translate="true">Seller</item>
                        <item name="emptyOptionsHtml" xsi:type="string" translate="true">Start typing to find sellers</item>
                        <item name="missingValuePlaceholder" xsi:type="string" translate="true">Seller with name: %s doesn\'t exist</item>
                        <item name="isDisplayMissingValuePlaceholder" xsi:type="boolean">true</item>
                        <item name="isDisplayEmptyPlaceholder" xsi:type="boolean">true</item>
                        <item name="isRemoveSelectedIcon" xsi:type="boolean">true</item>
                        <item name="filterRateLimit" xsi:type="string" translate="true">1000</item>
                        <item name="filterRateLimitMethod" xsi:type="string">notifyWhenChangesStop</item>
                        <item name="levelsVisibility" xsi:type="number">1</item>
                        <item name="searchUrl" xsi:type="url" path="seller_onboarding/seller/search"/>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Seller</label>
                    <dataScope>seller_id</dataScope>
                </settings>
            </filterSelect>
            <filterSelect
                    name="mapping_source_id"
                    provider="${ $.parentName }"
                    sortOrder="20"
                    component="Magento_Ui/js/grid/filters/elements/ui-select"
                    template="ui/grid/filters/elements/ui-select">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="options" xsi:type="array"></item>
                        <item name="filterOptions" xsi:type="boolean">true</item>
                        <item name="searchOptions" xsi:type="boolean">true</item>
                        <item name="filterPlaceholder" xsi:type="string" translate="true">Seller Category</item>
                        <item name="emptyOptionsHtml" xsi:type="string" translate="true">Start typing to find seller categories</item>
                        <item name="missingValuePlaceholder" xsi:type="string" translate="true">Seller Category with name: %s doesn\'t exist</item>
                        <item name="isDisplayMissingValuePlaceholder" xsi:type="boolean">true</item>
                        <item name="isDisplayEmptyPlaceholder" xsi:type="boolean">true</item>
                        <item name="isRemoveSelectedIcon" xsi:type="boolean">true</item>
                        <item name="filterRateLimit" xsi:type="string" translate="true">1000</item>
                        <item name="filterRateLimitMethod" xsi:type="string">notifyWhenChangesStop</item>
                        <item name="levelsVisibility" xsi:type="number">1</item>
                        <item name="searchUrl" xsi:type="url" path="seller_onboarding/seller_category/search"/>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Seller Category</label>
                    <dataScope>mapping_source_id</dataScope>
                </settings>
            </filterSelect>
            <filterSelect
                    name="mapping_category_id"
                    provider="${ $.parentName }"
                    sortOrder="30"
                    component="Magento_Catalog/js/components/new-category"
                    template="ui/grid/filters/elements/ui-select">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="filterOptions" xsi:type="boolean">true</item>
                        <item name="levelsVisibility" xsi:type="number">1</item>
                        <item name="filterPlaceholder" xsi:type="string" translate="true">Store Category</item>
                        <item name="emptyOptionsHtml" xsi:type="string" translate="true">Start typing to find categories</item>
                    </item>
                </argument>
                <settings>
                    <options class="Magento\Catalog\Ui\Component\Product\Form\Categories\Options"/>
                    <label translate="true">Store Category</label>
                    <dataScope>mapping_category_id</dataScope>
                    <listens>
                        <link name="${ $.namespace }.${ $.namespace }:responseData">setParsed</link>
                    </listens>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete selected category mappings?</message>
                        <title translate="true">Delete</title>
                    </confirm>
                    <url path="seller_onboarding/category_mapping/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="seller_category_mapping_columns" component="Comave_SellerOnboarding/js/grid/listing">
        <selectionsColumn name="ids">
            <settings>
                <indexField>mapping_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>55</resizeDefaultWidth>
            </settings>
        </selectionsColumn>
        <column name="mapping_id" sortOrder="10">
            <settings>
                <label translate="true">Mapping ID</label>
                <draggable>false</draggable>
                <sorting>asc</sorting>
                <visible>false</visible>
            </settings>
        </column>
        <column name="seller_id" sortOrder="20" component="Webkul_Marketplace/js/grid/columns/link"
                class="Webkul\MpSellerBuyerCommunication\Ui\Component\Listing\Columns\SellerName">
            <settings>
                <draggable>false</draggable>
                <visible>true</visible>
                <label translate="true">Seller</label>
            </settings>
        </column>
        <column name="mapping_source_id" sortOrder="30" class="Comave\SellerOnboarding\Ui\Component\Listing\Column\Seller\Category\Name">
            <settings>
                <draggable>false</draggable>
                <label translate="true">Seller Category</label>
            </settings>
        </column>
        <column name="mapping_category_id"
                component="Comave_SellerOnboarding/js/grid/columns/categories"
                sortOrder="40">
            <settings>
                <options class="Magento\Catalog\Ui\Component\Product\Form\Categories\Options"/>
                <draggable>false</draggable>
                <dataType>select</dataType>
                <label translate="true">Store Category</label>
            </settings>
        </column>
        <column name="mapping_accuracy" sortOrder="50">
            <settings>
                <filter>textRange</filter>
                <dataType>number</dataType>
                <draggable>false</draggable>
                <sortable>false</sortable>
                <label translate="true">Mapping Accuracy</label>
            </settings>
        </column>
        <column name="mapping_type" component="Magento_Ui/js/grid/columns/select" sortOrder="60">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Comave\SellerOnboarding\Model\Config\Category\Mapping\Type"/>
                <label translate="true">Mapping Type</label>
                <draggable>false</draggable>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="70">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <timezone>false</timezone>
                <dateFormat>MMM d, y</dateFormat>
                <label translate="true">Created At</label>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>55</resizeDefaultWidth>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="80">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <timezone>false</timezone>
                <dateFormat>MMM d, y</dateFormat>
                <label translate="true">Updated At</label>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>55</resizeDefaultWidth>
            </settings>
        </column>
        <actionsColumn name="actions" class="SellerCategoryMappingGridActions" sortOrder="100">
            <settings>
                <indexField>mapping_id</indexField>
                <resizeEnabled>false</resizeEnabled>
            </settings>
        </actionsColumn>
    </columns>
</listing>
