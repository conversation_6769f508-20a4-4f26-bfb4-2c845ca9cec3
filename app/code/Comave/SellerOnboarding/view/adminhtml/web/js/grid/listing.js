define([
    'Magento_Ui/js/grid/listing'
], function (Collection) {
    'use strict';

    return Collection.extend({
        defaults: {
            template: "Comave_SellerOnboarding/grid/listing",
        },

        initialize: function () {
            this._super();

            return this;
        },

        isRowManual: function (index) {
            return this.hasData() && this.rows[index].mapping_type === '1';
        },
    });
});
