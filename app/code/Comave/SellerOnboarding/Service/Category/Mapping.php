<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerOnboarding\Service\Category;

use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Magento\Framework\App\ResourceConnection;

class Mapping
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(private readonly ResourceConnection $resourceConnection)
    {
    }

    public function getSellers(): array
    {
        return [];
    }

    /**
     * @param MappingInterface[] $mappingList
     * @return void
     */
    public function insertMultiple(array $mappingList): void
    {
        $connection = $this->resourceConnection->getConnection('write');
        $insertArr = [];

        foreach ($mappingList as $mapping) {
            if (!$mapping instanceof MappingInterface) {
                continue;
            }

            $insertArr[] = $mapping->getData();
        }

        $connection->delete(
            $connection->getTableName('comave_seller_onboarding_category_mapping'),
            [
                'mapping_source_id IN (?)' => array_column($insertArr, 'mapping_source_id'),
                'mapping_type = ?' => MappingInterface::MAPPING_TYPE_AUTO
            ]
        );
        $connection->insertMultiple(
            $connection->getTableName('comave_seller_onboarding_category_mapping'),
            $insertArr
        );
    }
}
