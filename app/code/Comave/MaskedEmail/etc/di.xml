<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\MaskedEmail\Api\MaskedEmailInterface" type="Comave\MaskedEmail\Model\MaskedEmail" />
    <preference for="Comave\MaskedEmail\Api\SellerContractInterface" type="Comave\MaskedEmail\Service\SellerContract" />
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin name="set_masked_email"
                type="Comave\MaskedEmail\Plugin\SetMaskedEmail"
                sortOrder="50"/>
    </type>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="maskedEmailUpdater" xsi:type="object">Comave\MaskedEmail\Console\Command\SetMaskedEmails</item>
                <item name="setBasicContractType" xsi:type="object">Comave\MaskedEmail\Console\Command\SetBasicContractType</item>
                <item name="resetContractType" xsi:type="object">Comave\MaskedEmail\Console\Command\ResetContractType</item>
                <item name="resetMaskedEmails" xsi:type="object">Comave\MaskedEmail\Console\Command\ResetMaskedEmails</item>
            </argument>
        </arguments>
    </type>
</config>
