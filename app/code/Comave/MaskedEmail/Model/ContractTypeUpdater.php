<?php

declare(strict_types=1);

namespace Comave\MaskedEmail\Model;

use Comave\MaskedEmail\Api\SellerContractInterface;
use Comave\MaskedEmail\Model\Config\Source\ContractType;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class ContractTypeUpdater
{
    /**
     * @param CustomerCollectionFactory $customerCollectionFactory
     * @param CustomerResource $customerResource
     * @param LoggerInterface $logger
     * @param EavConfig $eavConfig
     */
    public function __construct(
        private readonly CustomerCollectionFactory $customerCollectionFactory,
        private readonly CustomerResource $customerResource,
        private readonly LoggerInterface $logger,
        private readonly EavConfig $eavConfig,
    ) {}

    /**
     * @return int
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function setBasicContractType(): int
    {
        $updatedContracts = 0;
        $collection = $this->customerCollectionFactory->create();
        $contractTypeAttributeId = $this->getAttributeId(SellerContractInterface::CONTRACT_TYPE_ATTRIBUTE_CODE);
        $collection->getSelect()->joinLeft(
            ['cev' => 'customer_entity_varchar'],
            sprintf(
                'cev.entity_id = e.entity_id AND cev.attribute_id = %d',
                $contractTypeAttributeId
            ),
            ['contract_type' => 'value']
        );
        $where = new \Zend_Db_Expr('cev.value IS NULL');
        $collection->getSelect()->where($where);

        foreach ($collection as $customer) {
            try {
                $customer->setData(
                    SellerContractInterface::CONTRACT_TYPE_ATTRIBUTE_CODE,
                    ContractType::CONTRACT_TYPE_BASIC
                );
                $this->customerResource->saveAttribute(
                    $customer,
                    SellerContractInterface::CONTRACT_TYPE_ATTRIBUTE_CODE
                );
                $updatedContracts++;
            } catch (LocalizedException $e) {
                $this->logger->error(sprintf(
                    'Failed to set the contract type for customer ID %d: %s',
                    $customer->getId(),
                    $e->getMessage()
                ));
            }
        }

        $this->logger->info(sprintf(
            'Contract has been set as basic for %d customer(s).',
            $updatedContracts
        ));

        return $updatedContracts;
    }

    /**
     * @param string $attributeCode
     * @return int
     */
    private function getAttributeId(string $attributeCode): int
    {
        return (int) $this->eavConfig->getAttribute(
            \Magento\Customer\Model\Customer::ENTITY,
            SellerContractInterface::CONTRACT_TYPE_ATTRIBUTE_CODE
        )->getId();
    }
}
