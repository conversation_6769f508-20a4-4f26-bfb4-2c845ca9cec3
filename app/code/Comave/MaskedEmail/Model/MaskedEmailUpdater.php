<?php

declare(strict_types=1);

namespace Comave\MaskedEmail\Model;

use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Comave\MaskedEmail\Service\MaskedEmailGenerator;
use Comave\MaskedEmail\Api\MaskedEmailInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;
use Psr\Log\LoggerInterface;
use Comave\Marketplace\Model\Service\Seller;

class MaskedEmailUpdater
{
    /**
     * @param CustomerCollectionFactory $customerCollectionFactory
     * @param CustomerResource $customerResource
     * @param MaskedEmailGenerator $maskedEmailGenerator
     * @param CollectionFactory $sellerCollectionFactory
     * @param LoggerInterface $logger
     * @param Seller $sellerService
     */
    public function __construct(
        private readonly CustomerCollectionFactory $customerCollectionFactory,
        private readonly CustomerResource $customerResource,
        private readonly MaskedEmailGenerator $maskedEmailGenerator,
        private readonly CollectionFactory $sellerCollectionFactory,
        private readonly LoggerInterface $logger,
        private readonly Seller $sellerService,
    ) {}

    /**
     * Update all customers that don't have a masked_email set
     *
     * @return int
     */
    public function updateMissingMaskedEmails(): int
    {
        $updatedCustomers = 0;
        $sellerIds = $this->sellerService->getSellerIds();
        $collection = $this->customerCollectionFactory->create();
        $collection->addAttributeToFilter(MaskedEmailInterface::MASKED_EMAIL_ATTRIBUTE_CODE, ['null' => true])
                    ->addAttributeToFilter('entity_id', ['nin' => $sellerIds]);

        foreach ($collection as $customer) {
            try {
                $maskedEmail = $this->maskedEmailGenerator->generateMaskedHash($customer->getEmail());
                $customer->setData(MaskedEmailInterface::MASKED_EMAIL_ATTRIBUTE_CODE, $maskedEmail);
                $this->customerResource->saveAttribute($customer, MaskedEmailInterface::MASKED_EMAIL_ATTRIBUTE_CODE);
                $updatedCustomers++;
            } catch (\Throwable $e) {
                $this->logger->error(sprintf(
                    'Failed to update masked_email for customer ID %d: %s',
                    $customer->getId(),
                    $e->getMessage()
                ));
            }
        }

        $this->logger->info(sprintf(
            'Masked email updated for %d customer(s).',
            $updatedCustomers
        ));

        return $updatedCustomers;
    }
}
