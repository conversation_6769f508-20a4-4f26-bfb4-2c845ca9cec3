<?php

declare(strict_types=1);

namespace Comave\MaskedEmail\Console\Command;

use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\MaskedEmail\Model\ContractTypeUpdater;

class SetBasicContractType extends Command
{
    public function __construct(
        private readonly ContractTypeUpdater $contractTypeUpdater,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setName('comave:seller:set-basic-contract-type')
            ->setDescription('Sets the BASIC contract type for all customers who do not have it.');

        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $updatedCustomers = $this->contractTypeUpdater->setBasicContractType();
        $output->writeln('<info>Contract type assignment completed.</info>');
        $output->writeln("<info>BASIC contract type set for {$updatedCustomers} customer(s).</info>");

        return Cli::RETURN_SUCCESS;
    }
}
