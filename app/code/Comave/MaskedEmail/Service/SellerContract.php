<?php

declare(strict_types=1);

namespace Comave\MaskedEmail\Service;

use Comave\MaskedEmail\Api\SellerContractInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Comave\MaskedEmail\Model\Config\Source\ContractType;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Customer\Model\Session as CustomerSession;
use Comave\MaskedEmail\Service\MaskedEmailGenerator;
use Comave\MaskedEmail\Api\MaskedEmailInterface;
use Comave\MarketplaceApi\Model\TokenManager;
use Psr\Log\LoggerInterface;

class SellerContract implements SellerContractInterface
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly CustomerSession $customerSession,
        private readonly TokenManager $tokenManager,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @param int $sellerId
     * @return bool
     */
    public function isPremiumSeller(int $sellerId): bool
    {
        try {
            $seller = $this->customerRepository->getById($sellerId);
            $contractType = $seller->getCustomAttribute(SellerContractInterface::CONTRACT_TYPE_ATTRIBUTE_CODE)?->getValue() ?? null;

            return $contractType === ContractType::CONTRACT_TYPE_PREMIUM;
        } catch (NoSuchEntityException $e) {
            $this->logger->info(sprintf("Seller with ID %d not found: %s", $sellerId, $e->getMessage()));
        } catch (\Exception $e) {
            $this->logger->error(sprintf("Error retrieving seller contract type for ID %d: %s", $sellerId, $e->getMessage()));
        }

        return false;
    }

    /**

     * @return bool
     */
    public function isCurrentSellerPremium(): bool
    {
        $sellerId = null;
        try {
            $sellerId = $this->getCurrentSellerId();
            if (!$sellerId) {
                return false;
            }

            return $this->isPremiumSeller($sellerId);
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('Error checking premium status for seller ID %d: %s', $sellerId, $e->getMessage())
            );

            return false;
        }
    }

    /**
     * Get the customer email based on the seller's contract type
     *
     * @param int $customerId
     * @param string $customerEmail
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCustomerEmail(int $customerId, string $customerEmail = ''): string
    {
        //guest user
        if ($customerId === 0) {
            return MaskedEmailGenerator::generateMaskedHash($customerEmail);
        }

        if ($this->isCurrentSellerPremium() && !empty($customerEmail)) {
            return $customerEmail;
        }

        $sellerId = $this->getCurrentSellerId();
        if ($this->hasAssignedClub($sellerId)) {
            return $customerEmail;
        }

        try {
            $customer = $this->customerRepository->getById($customerId);
            $email = $customer->getEmail();

            return $customer->getCustomAttribute(MaskedEmailInterface::MASKED_EMAIL_ATTRIBUTE_CODE)?->getValue()
                ?? MaskedEmailGenerator::generateMaskedHash($email);
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                sprintf(
                    'Error retrieving customer email for %s: %s',
                    $customerId > 0 ? "ID $customerId" : "Email $customerEmail",
                    $e->getMessage()
                )
            );
            return MaskedEmailGenerator::generateMaskedHash($customerEmail);
        }

        return '';
    }

    /**
     * @return int
     * @throws \Magento\Framework\Exception\AuthorizationException
     */
    private function getCurrentSellerId(): int
    {
        if ($this->customerSession->isLoggedIn()) {
            return (int) $this->customerSession->getCustomerId();
        }

        $token = $this->tokenManager->extractTokenFromHeader();
        $sellerId = $this->tokenManager->validateAndRetrieveSellerId($token);

        if (!$sellerId) {
            throw new \Magento\Framework\Exception\AuthorizationException(__('Unauthorized, sellerId not found.'));
        }

        return (int) $sellerId;
    }

    /**
     * @param int $sellerId
     * @return bool
     */
    public function hasAssignedClub(int $sellerId): bool
    {
        $seller = $this->customerRepository->getById($sellerId);
        $sellerClub = $seller->getCustomAttribute('customerclub');
        if (empty($sellerClub) || $sellerClub->getValue() == null ) {
            return false;
        }

        return true;
    }
}
