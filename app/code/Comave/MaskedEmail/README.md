# Comave_MaskedEmail module
This feature introduces masked emails for customers. 
Sellers will now see a masked version of the customer's email address — unless they have a PREMIUM contract, which grants full visibility.
Sellers will see the masked email in:
- Seller Dashboard
- Invoices
- Orders
- "View Customers" section
- REST API responses
- Shopify order sync
- Console command "comave:customer:set-masked-emails" to set masked emails for customers who do not have a masked email set
- Console command "comave:customer:set-basic-contract-type" to set basic contract type for customers who do not have a contract type set
- Console command "comave:customer:reset-contract-type" to reset the contract type for customers who already have a contract type set
- Console command "comave:customer:reset-masked-emails" to reset the masked emails for customers who already have a masked email set
- Rest calls:
  -  /sellers/me/order/order_id/invoice/invoice_id

Exceptions:
- Sellers with special contracts (e.g., Seller Club types) may be excluded from this logic.

<PERSON><PERSON> can assign a contract type (BASIC or PREMIUM) to each seller using the contract_type customer attribute.
