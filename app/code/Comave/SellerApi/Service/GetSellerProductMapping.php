<?php

declare(strict_types=1);

namespace Comave\SellerApi\Service;

use Comave\SellerApi\Api\IntegrationInterface;
use Magento\Framework\App\ResourceConnection;

class GetSellerProductMapping
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param array $productIds
     * @return array
     */
    public function get(array $productIds): array
    {
        $connection = $this->resourceConnection->getConnection('read');
        $productsSelect = $connection->select()
            ->from(
                ['flat' => $connection->getTableName(IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT)],
                [
                    'seller_id',
                    'productIds' => new \Zend_Db_Expr('GROUP_CONCAT(product_id)')
                ]
            )->where(
                'product_id IN (?)',
                $productIds
            )->group('seller_id');

        return $connection->fetchAssoc($productsSelect) ?: [];
    }
}
