<?php

declare(strict_types=1);

namespace Comave\SellerApi\Model;

use Comave\SellerApi\Api\IntegrationInterface;
use Magento\Framework\App\ResourceConnection;

class Integrations
{
    public function __construct(
        private readonly ResourceConnection $resourceConnection
    ) {}

    /**
     * @return array
     */
    public function getIntegrations(): array
    {
        $connection = $this->resourceConnection->getConnection();

        $integrationsTypeSelect = $connection->select()
            ->distinct()
            ->from(
                $connection->getTableName(IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT),
                ['integration_type']
            );

        return $connection->fetchAssoc($integrationsTypeSelect) ?: [];
    }
}
