<?php

declare(strict_types=1);

namespace Comave\SellerApi\Model;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SellerApi\Api\IntegrationExtensionInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\UrlInterface;

class BaseIntegration implements IntegrationInterface
{
    private ?IntegrationExtensionInterface $extensionAttributes = null;
    private ?string $sellerId = null;

    /**
     * @param ResourceConnection $resourceConnection
     * @param string|null $sellerColumnIdentifier
     * @param string|null $productColumnIdentifier
     * @param string|null $mainProductLinkTable
     * @param string|null $orderTableLink
     * @param string|null $orderTableLinkId
     * @param string|null $orderTableLinkField
     * @param string|null $externalLink
     * @param string|null $integrationType
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly ?string $sellerColumnIdentifier = '',
        private readonly ?string $productColumnIdentifier = '',
        private readonly ?string $mainProductLinkTable = '',
        private readonly ?string $orderTableLink = '',
        private readonly ?string $orderTableLinkId = '',
        private readonly ?string $orderTableLinkField = '',
        private readonly ?string $externalLink = '',
        private ?string $integrationType = ''
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getIntegrationType(): string
    {
        return $this->integrationType;
    }

    /**
     * @inheritDoc
     */
    public function getTableLink(): string
    {
        return $this->mainProductLinkTable;
    }

    /**
     * @inheritDoc
     */
    public function getSellerColumnIdentifier(): string
    {
        return $this->sellerColumnIdentifier;
    }

    /**
     * @inheritDoc
     */
    public function getSellerColumnProduct(): string
    {
        return $this->productColumnIdentifier;
    }

    /**
     * @return \Comave\SellerApi\Api\IntegrationExtensionInterface|null
     */
    public function getExtensionAttributes(): ?IntegrationExtensionInterface
    {
        return $this->extensionAttributes;
    }

    /**
     * @param \Comave\SellerApi\Api\IntegrationExtensionInterface $extension
     * @return $this
     */
    public function setExtensionAttributes(IntegrationExtensionInterface $extension): self
    {
        $this->extensionAttributes = $extension;

        return $this;
    }

    /**
     * @param string $sellerId
     * @return self
     */
    public function setSellerId(string $sellerId): IntegrationInterface
    {
        $this->sellerId = $sellerId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getSellerId(): ?string
    {
        return $this->sellerId;
    }

    /**
     * @param string $integrationType
     * @return IntegrationInterface
     */
    public function setIntegrationType(string $integrationType): IntegrationInterface
    {
        $this->integrationType = $integrationType;

        return $this;
    }

    /**
     * @param string $magentoOrderId
     * @return string|null
     */
    public function getExternalOrderLink(string $magentoOrderId): ?string
    {
        $connection = $this->resourceConnection->getConnection('read');

        if (
            empty($this->orderTableLink) ||
            empty($this->externalLink) ||
            empty($this->orderTableLinkId) ||
            empty($this->orderTableLinkField) ||
            !$connection->isTableExists($this->orderTableLink) ||
            !$connection->tableColumnExists(
                $connection->getTableName($this->orderTableLink),
                $this->orderTableLinkField
            ) ||
            !$connection->tableColumnExists(
                $connection->getTableName($this->orderTableLink),
                $this->orderTableLinkId
            )
        ) {
            return null;
        }

        $externalIdSelect = $connection->select()
            ->from(
                $this->resourceConnection->getTableName($this->orderTableLink),
                [
                    $this->orderTableLinkField
                ]
            )->where(
                $this->orderTableLinkId . ' = ?',
                $magentoOrderId
            );

        if (!$externalId = $connection->fetchOne($externalIdSelect)) {
            return null;
        }

        return sprintf(
            $this->externalLink,
            $externalId
        );
    }
}
