<?php

declare(strict_types=1);

namespace Comave\SellerApi\Plugin\Adminhtml;

use GhoSter\ChangeCustomerPassword\Block\Adminhtml\Customer\PasswordChange;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;

class RestrictOnlySellers
{
    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(private readonly CollectionFactory $collectionFactory)
    {
    }

    /**
     * @param PasswordChange $passwordChangeBlock
     * @param callable $proceed
     * @return string
     */
    public function aroundToHtml(
        PasswordChange $passwordChangeBlock,
        callable $proceed,
    ): string {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter(
            'seller_id',
            $passwordChangeBlock->getCustomerId()
        )->addFieldToFilter('is_seller', 1);

        return $collection->getSize() > 0 ? $proceed() : '';
    }
}
