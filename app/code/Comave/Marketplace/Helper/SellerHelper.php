<?php
declare(strict_types=1);

namespace Comave\Marketplace\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\RequestInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class Seller<PERSON>elper extends AbstractHelper
{
    public const string FRONTEND_LOGIN_PATH = 'login';

    private const string IS_SELLER_VAR_NAME = 'is_seller';

    /**
     * Construct function
     *
     * @param \Webkul\Marketplace\Helper\Data $mpHelperData
     * @param \Magento\Framework\App\RequestInterface $request
     */
    public function __construct(
        private readonly MarketplaceHelper $mpHelperData,
        private readonly RequestInterface  $request
    )
    {
    }

    /**
     * Check if the customer is a seller
     *
     * @param int $customerId
     * @return bool
     */
    public function isCustomerSeller(int $customerId): bool
    {
        $params = $this->request->getParams();

        if (
            !empty($params[self::IS_SELLER_VAR_NAME]) &&
            !empty($params['profileurl']) &&
            (int)$params[self::IS_SELLER_VAR_NAME] === 1
        ) {
            return true;
        }

        $collection = $this->mpHelperData->getSellerCollectionObj($customerId);
        $collection->addFieldToFilter(self::IS_SELLER_VAR_NAME, true);

        return $collection->getSize() > 0;
    }
}
