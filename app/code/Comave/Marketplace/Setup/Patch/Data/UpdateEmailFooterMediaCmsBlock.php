<?php

declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Api\GetBlockByIdentifierInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Psr\Log\LoggerInterface;

class UpdateEmailFooterMediaCmsBlock implements DataPatchInterface
{
    private const CMS_BLOCK_EMAIL_FOOTER_MEDIA_BLOCK = 'email_footer_media_block';
    private const CMS_BLOCK_EMAIL_FOOTER_MEDIA_BLOCK_HTML = 'email_footer_media_block.html';
    private const DEFAULT_SOURCE_DIR = 'install-data';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly BlockRepositoryInterface $blockRepository,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $htmlContent = $this->getFileContent(self::CMS_BLOCK_EMAIL_FOOTER_MEDIA_BLOCK_HTML);
        if (!$htmlContent) {
            return;
        }

        try {
            $cmsBlock = $this->getCmsBlockByIdentifier(self::CMS_BLOCK_EMAIL_FOOTER_MEDIA_BLOCK);
            $cmsBlock->setContent($htmlContent);
            $this->blockRepository->save($cmsBlock);
            $this->logger->info(
                sprintf(
                    'CMS block with identifier "%s" has been updated',
                    self::CMS_BLOCK_EMAIL_FOOTER_MEDIA_BLOCK
                )
            );
        } catch (NoSuchEntityException $e) {
            $this->logger->error("CMS block with identifier does not exist." . $e->getMessage());
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to save CMS block ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('Generic fail to save CMS block ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @param $blockId
     * @return \Magento\Cms\Api\Data\BlockInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getCmsBlockByIdentifier($blockId)
    {
        try {
            return $this->blockRepository->getById($blockId);
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                sprintf(
                    'CMS block with identifier "%s" does not exist. Error: %s',
                    $blockId,
                    $e->getMessage()
                )
            );
            throw new LocalizedException(
                __('CMS block with identifier "%1" does not exist. . Error: %s', $blockId, $e->getMessage())
            );
        }
    }

    /**
     * @return string
     */
    private function getSourceDir(): string
    {
        return $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_Marketplace')
            . '/' . self::DEFAULT_SOURCE_DIR . '/';
    }

    /**
     * @param $file
     * @return string
     */
    private function getFileContent($file): string
    {
        try {
            return $this->ioFile->read($this->getSourceDir() . $file);
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                sprintf('CMS block file "%s" does not exist. Error: %s', $file, $e->getMessage())
            );
            return '';
        }
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
