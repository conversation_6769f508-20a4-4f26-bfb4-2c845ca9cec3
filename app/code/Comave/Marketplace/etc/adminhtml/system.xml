<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="marketplace" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <tab>webkul</tab>
            <group id="product_settings" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                <field id="disable_seller_create_attribute" translate="label comment" sortOrder="2" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disable Create Attribute for Configurable Products</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

            </group>
            <group id="inventory_settings" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Inventory Settings</label>
                <field id="admin_low_stock_threshold" translate="label comment" sortOrder="2" type="text" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Low Stock Quantity Admin/Seller Grid</label>
                    <validate>validate-number</validate>
                </field>
            </group>
        </section>
    </system>
</config>
