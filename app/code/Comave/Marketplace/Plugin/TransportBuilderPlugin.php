<?php

declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Comave\Marketplace\Helper\SellerHelper;
use Magento\Email\Model\Template as EmailTemplate;
use Magento\Email\Model\TemplateFactory;
use Magento\Framework\Mail\Template\TransportBuilder;
use Comave\Marketplace\Service\BaseUrlResolver;

class TransportBuilderPlugin
{
    private const string IS_SELLER_VAR_NAME = 'is_seller';

    /**
     * @param SellerHelper $sellerHelper
     * @param TemplateFactory $templateFactory
     * @param BaseUrlResolver $baseUrlResolver
     */
    public function __construct(
        private readonly SellerHelper $sellerHelper,
        private readonly TemplateFactory $templateFactory,
        private readonly BaseUrlResolver $baseUrlResolver,
    ) {
    }

    /**
     * Before plugin for the TransportBuilder
     *
     * @param \Magento\Framework\Mail\Template\TransportBuilder $subject
     * @param mixed[] $templateVars
     * @return mixed[]|null
     */
    public function beforeSetTemplateVars(TransportBuilder $subject, array $templateVars): ?array
    {
        $templateId = $this->getTemplateId($subject);

        if ($templateId === null) {
            return null;
        }

        return $this->processTemplateVars($templateVars);
    }

    /**
     * Get template code function
     *
     * @param \Magento\Framework\Mail\Template\TransportBuilder $subject
     * @return string|null
     */
    private function getTemplateId(TransportBuilder $subject): ?string
    {
        $templateId = $subject->getTemplateId();

        if (is_numeric($templateId)) {
            $template = $this->getTemplateById($templateId);

            return $template->getId() ? $template->getOrigTemplateCode() : null;
        }

        return $templateId;
    }

    /**
     * Process template variables to add the customer seller information function
     *
     * @param mixed[] $templateVars
     * @return mixed[]|null
     */
    private function processTemplateVars(array $templateVars): ?array
    {
        if (empty($templateVars['customer'])) {
            return null;
        }

        $customer = $templateVars['customer'];

        if ($customer instanceof \Magento\Framework\DataObject && $customer->getId()) {
            $isSeller = $this->sellerHelper->isCustomerSeller((int) $customer->getId());
            $templateVars[self::IS_SELLER_VAR_NAME] = $isSeller;

            $templateVars['baseUrl'] = $isSeller
                ? $this->baseUrlResolver->getStoreBaseLinkUrl()
                : $this->baseUrlResolver->getFrontendUrl();

            return [$templateVars];
        }

        return null;
    }

    /**
     * Get template code by template ID
     *
     * @param string $templateId
     * @return \Magento\Email\Model\Template as EmailTemplate
     */
    private function getTemplateById(string $templateId): EmailTemplate
    {
        return $this->templateFactory->create()->load($templateId);
    }
}
