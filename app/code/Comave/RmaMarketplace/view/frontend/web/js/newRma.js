define([
    "jquery",
    'Magento_Ui/js/modal/confirm',
    'Magento_Ui/js/modal/alert',
    "jquery/ui"
], function ($, confirmation, alertBox) {
    'use strict';
    $.widget('mprma.newRma', {
        options: {},
        _create: function () {
            var self = this;
            var reasons = self.options.reasons;
            var blockHtml = self.options.blockHtml;
            var imgErrorMsg = self.options.imgErrorMsg;
            var consignmentLabel = self.options.consignmentLabel;
            var sellerLabel = self.options.sellerLabel;
            var selectItemLabel = self.options.selectItemLabel;
            var imgSelectLabel = self.options.imgSelectLabel;
            var orderSelectLabel = self.options.orderSelectLabel;
            var sellerSelectLabel = self.options.sellerSelectLabel;
            var resolutionSelectLabel = self.options.resolutionSelectLabel;
            var itemsErrorLabel = self.options.itemsErrorLabel;
            var refundLabel = self.options.refundLabel;
            var replaceLabel = self.options.replaceLabel;
            var cancelLabel = self.options.cancelLabel;
            var deliveredLabel = self.options.deliveredLabel;
            var notDeliveredLabel = self.options.notDeliveredLabel;
            var orderStatusLabelText = self.options.orderStatusLabel;
            var selectQtyLabel = self.options.selectQtyLabel;
            var selectSellerLabel = self.options.selectSellerLabel;
            var warningLabel = self.options.warningLabel;
            var qtyMsg = self.options.qtyMsg;
            var orderData = {};
            var result = [];
            var imgCount = 0;
            var selectedCount = 0;
            var img = "";
            var error = false;
            var isVirtual = 0;
            var sellerImageData = {}; 
            var removedImages = [];// Stores image data for each seller
            var product_not_availbe = "No products available for returns";

            $(document).ready(function () {
                $("#orders").val("");
                $("#orders").on('change', function () {
                    removeConsignmentBlock();
                    $(".wk-seller-field").remove();
                    $(".wk-resolution-field").remove();
                    $(".wk-order-status-field").remove();
                    $(".wk-mass-select").prop("checked", false);
                    var orderId = $(this).val();
                    if (orderId == "") {
                        resetOrderArea();
                    } else {
                        showLoadingMask();
                        $.ajax({
                            type: 'post',
                            url: self.options.orderUrl,
                            async: true,
                            dataType: 'json',
                            data: { order_id: orderId, is_guest: self.options.isGuest },
                            success: function (data) {
                                if (data.isLoggedIn == 1) {
                                    orderData = data;
                                    $("#order_items").empty();
                                    $(".wk-seller-field").remove();
                                    var orderStatus = data.order_status;
                                    var shipmentStatus = data.shipment_status;
                                    var items = orderData.items;
                                    var orderStatus = data.items;
                                    displayItems(items, orderStatus, data, product_not_availbe)
                                    var shipmentStatus = orderData.order_get_status.order_status;
                                    setProductStatus(shipmentStatus);
                                } else {
                                    location.reload();
                                }
                                hideLoadingMask();
                            }
                        });
                    }
                });
                
                $('body').on('change', '.wk-mass-select', function () {
                    if ($(this).prop("checked") == true) {
                        $(".order_item").prop("checked", true);
                        $(".order_qty").addClass("required-entry");
                        $(".wk-reason").addClass("required-entry");
                        $(".wk-resolution").addClass("required-entry");
                    } else {
                        $(".order_item").prop("checked", false);
                        $(".order_qty").removeClass("required-entry");
                        $(".order_qty").removeClass("mage-error");
                        $(".wk-reason").removeClass("required-entry");
                        $(".wk-reason").removeClass("mage-error");
                        $(".wk-resolution").removeClass("required-entry");
                        $(".wk-resolution").removeClass("mage-error");
                    }
                    manageOrderStatus();
                });


                $("body").on('click', '.order_item', function () {
                    var allChecked = true;
                    if ($(this).prop("checked") == true) {
                        $(this).parent().parent().find(".order_qty, .wk-reason").addClass("required-entry");
                    } else {
                        $(this).parent().parent().find(".order_qty, .wk-reason").removeClass("required-entry");
                    }
                });


                $("body").on('click', '.order_item', function () {
                    var allChecked = true;
                    if ($(this).prop("checked") == true) {
                        $(this).parent().parent().find(".order_qty").addClass("required-entry");
                        $(this).parent().parent().find(".wk-reason").addClass("required-entry");
                        $(this).parent().parent().find(".wk-resolution").addClass("required-entry");
                    } else {
                        $(this).parent().parent().find(".order_qty").removeClass("required-entry");
                        $(this).parent().parent().find(".order_qty").removeClass("mage-error");
                        $(this).parent().parent().find(".wk-reason").removeClass("required-entry");
                        $(this).parent().parent().find(".wk-reason").removeClass("mage-error");
                         $(this).parent().parent().find(".wk-resolution").removeClass("required-entry");
                          $(this).parent().parent().find(".wk-resolution").removeClass("mage-error");
 
                    }
                    $(".order_item").each(function () {
                        if ($(this).prop("checked") == false) {
                            allChecked = false;
                        }
                    });
                    if (allChecked) {
                        $(".wk-mass-select").prop("checked", true);
                    } else {
                        $(".wk-mass-select").prop("checked", false);
                    }
                    manageOrderStatus();
                });

                $('body').on('change', '.image-upload', function () {
                    var sellerId = $(this).closest('tr').data('seller-id');
                    var files = $(this).prop('files');
                    if (files.length > 0) {
                        var formData = new FormData();
                        $.each(files, function(index, file) {
                            formData.append('file[]', file);
                        });

                        $.ajax({
                            url: '/uploade/upload/index', // corrected URL
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                if (response.success) {
                                    if (!sellerImageData.hasOwnProperty(sellerId)) {
                                        sellerImageData[sellerId] = [];
                                    }
                                    $.each(response.filePaths, function(index, path) {
                                        var fileName = path.split('/').pop();
                                        sellerImageData[sellerId].push({ name: fileName, path: path });
                                    });
                                } else {
                                    console.error('Error:', response.error);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('AJAX Error:', error);
                            }
                        });
                    }
                });
               // Event listener for removing images

                $('body').on('click', '.remove-image', function () {
                    var fileToRemove = $(this).data('file');
                    var imagePreview = $(this).closest('tr').find('.image-preview');
                    var sellerId = $(this).closest('tr').data('seller-id');
                    
                    var imageData = sellerImageData[sellerId];
                    imageData.forEach(function(item, index) {
                        if (item.name === fileToRemove) {
                            imageData.splice(index, 1);
                        }
                    });
                    $(this).closest('.image-preview-container').remove();
                });

                $(".wk-save").on('click', async function (event) {
                    event.preventDefault();
                    if ($('#wk_new_rma_form').valid()) {
                        var itemSelected = $(".order_item:checked").length > 0;
                        if (!itemSelected) {
                            alertBox({
                                title: warningLabel,
                                content: "<div class='wk-mprma-warning-content'>" + selectItemLabel + "</div>",
                                actions: {
                                    always: function () { }
                                }
                            });
                            return false;
                        }

                        for (var sellerId in sellerImageData) {
                            if (sellerImageData.hasOwnProperty(sellerId)) {
                                var imageData = sellerImageData[sellerId];
                                $("#image_data_" + sellerId).val(JSON.stringify(imageData));
                            }
                        }

                        // // Add the removed image data to the form
                        // $("#removed_images").val(JSON.stringify(removedImages));

                        var resolutionType = $(".wk-resolution").val();
                        if (resolutionType == "3") {
                            var totalOrderQty = 0;
                            let selectedOrderId = $("#orders").val();
                            await $.ajax({
                                type: 'post',
                                url: self.options.totalOrderQtyUrl,
                                data: { order_id: selectedOrderId },
                                success: function (data) {
                                    if (data.status === true) {
                                        totalOrderQty = data.totalOrderQty;
                                    }
                                }
                            });
                            let selectedQty = 0;
                            $(".order_qty").each(function () {
                                if ($(this).val() != "") {
                                    selectedQty += parseInt($(this).val());
                                }
                            });

                            if (selectedQty < totalOrderQty) {
                                isPartialCancel = true;
                                $("#isPartialCancel").val(1);
                                confirmation({
                                    title: 'Cancel Items',
                                    content: 'Are you sure? This order will be canceled and a new one will be created instead.',
                                    actions: {
                                        confirm: function () {
                                            var field = "<input type='hidden' id='is_virtual' name='is_virtual' value='" + isVirtual + "'>";
                                            $("#is_virtual").remove();
                                            $("#wk_new_rma_form").append(field);
                                            $("#wk_new_rma_form").submit();
                                        },
                                        cancel: function () {
                                            return false;
                                        }
                                    }
                                });
                            }
                            hideLoadingMask();
                        }
                        var field = "<input type='hidden' id='is_virtual' name='is_virtual' value='" + removedImages + "'>";
                        $("#is_virtual").remove();
                        $("#wk_new_rma_form").append(field);

                        // Submit the form
                        $("#wk_new_rma_form").submit();
                    }
                });
            });

            function resetOrderArea()
            {
                var errorHtml = '<div class="message info"><span>'+self.options.orderSelectLabel+'</span></div>';
                var html = '<tr><td colspan="7">'+errorHtml+'</td></tr>';
                $("#order_items").empty();
                $("#order_items").append(html);
            }

            function displayItems(items, orderStatus, data) {
                if (items.length === 0) {
                    var html = $('<tr>');
                    html.append($('<tr>').append($('<td>', { colspan: '7', text: 'No products available for returns' })));
                    $("#order_items").append(html);
                }
                
                var displayedProducts = {}; // Used to track displayed products by SKU

                // Loop through each item in the items array
                $.each(items, function(index, item) {
                    $.each(item, function(index, itemobj) {
                        $.each(itemobj, function(index, itemvalues) {
                            var key = itemvalues.sku;

                            // Check if the product with this SKU has already been displayed
                            if (!displayedProducts[key]) {
                                // If not displayed yet, generate HTML for the item
                                var html = getHtml(itemvalues, orderStatus, data);
                                html.data('seller-id', itemvalues.seller_id);
                                var imageInput = $('<input type="file" class="image-upload" multiple>').on('change', function() {
                                    previewImages(this, $(this).closest('tr').find('.image-preview'));
                                });
                                var imagePreview = $('<div class="image-preview"></div>');
                                html.append($('<td></td>').append(imageInput).append(imagePreview));

                                // Append a hidden input field to store image data for each seller
                                var imageDataInput = $('<input type="hidden" id="image_data_' + itemvalues.seller_id + '" name="image_data[' + itemvalues.seller_id + ']">');
                                $("#wk_new_rma_form").append(imageDataInput);

                                // Append the HTML to the table
                                $("#order_items").append(html);

                                // Mark the product as displayed
                                displayedProducts[key] = true;
                            }
                        });
                    });
                });
            }

            
            function previewImages(input, imagePreview) {
                var files = input.files;
                if (files && files.length > 0) {
                    // Only clear the container if there are files selected
                    imagePreview.empty();

                    // Loop through each file
                    $.each(files, function(index, file) {
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            var imgElement = $('<img>').attr('src', e.target.result);
                            var deleteButton = $('<button>').text('Remove').addClass('remove-image').data('file', file.name);
                            var container = $('<div class="image-preview-container"></div>').append(imgElement).append(deleteButton);
                            imagePreview.append(container);
                        };
                        reader.readAsDataURL(file); // Read file as data URL
                    });
                } 
            }



             $('body').on('change', '#order_status', function () {
                    var type = $(this).val();
                    if (type == 1) {
                        addConsignmentBlock();
                    } else {
                        removeConsignmentBlock();
                    }
            });


            function setProductStatus(type) {
                $(".wk-order-status-field").remove();
                var orderStatusLabel = orderStatusLabelText;
                var orderStatusHtml = $("<input type='hidden' class='wk-order-status required-entry' name='order_status' value='1'>");

                var fieldsetHtml = $("<div>", { class: 'field wk-order-status-field' });
               // fieldsetHtml.append($("<label>", { class: 'label' }).append($("<span>", { text: orderStatusLabel })));
                fieldsetHtml.append($("<div>", { class: 'control' }).append(orderStatusHtml));
                $(".wk-actions-toolbar").before(fieldsetHtml);
            }


            $('body').on('change', '.wk-order-status', function () {
                $(".wk-order-cons-field").remove();
                var orderStatus = $('.wk-order-status').val();
                var consignmentLabel = $.mage.__("Consignment Number");
                var orderConsHtml = $("<input class='wk-order-con-no required-entry validate-no-html-tags' name='number'>");
                 
                if (orderStatus==0) {
                    $(".wk-order-cons-field").remove();
                } else {
                var fieldsetHtml = $("<div>", { class : 'required field wk-order-cons-field'});
                fieldsetHtml.append($("<label>", { class : 'label'}).append($("<span>", { text : consignmentLabel})));
                fieldsetHtml.append($("<div>", { class : 'control'}).append(orderConsHtml));
                $(".wk-actions-toolbar").before(fieldsetHtml);
                }
                
            });


            function getHtml(obj, orderStatus, data) {
                var qty = obj.qty;                
                if (orderStatus == 2) {
                    qty = 0; 
                }
                var html = $('<tr>');
                if (qty > 0) {
                    html.append($('<td>', { class: 'col' })
                        .append($('<input>', { type: 'checkbox', name: 'item_ids[]', class: 'order_item', value: obj.item_id, 'data-id': obj.id, 'data-virtual': obj.is_virtual })));
                } else {
                    html.append($('<td>', { class: 'col' }));
                }
                html.append($('<td>', { class: 'col' }).append($('<div>', { class: 'wk-mp-rma-img' }).append($('<img/>', { src: obj.product_image }))).append($('<div>', { class: 'wk-mp-rma-name' }).append($('<a/>', { href: obj.product_url, text: obj.name })).append(obj.optionHtml)));
                html.append($('<td>', { class: 'col', html: obj.sku }));
                html.append($('<td>', { class: 'col', html: obj.price }));
                if (qty > 0) {
                    html.append($('<td>', { class: 'col' }).append(getDropDownList(obj.qty, obj.item_id)));
                } else {
                    html.append($('<td>', { class: 'col', text: qtyMsg }));
                }
                html.append($('<td>', { class: 'col' }).append(getResolutionDropDown(data, obj.item_id))); 
                html.append($('<td>', { class: 'col' }).append(getReasonDropDown(obj.item_id)));
                return html;
            }

            function getResolutionDropDown(data, itemId) {
                var resolutions = data.resolutions; 
                var resolutionHtml = $("<select class='wk-resolution' name='resolution_type[" + itemId + "]'></select>");
                resolutionHtml.append("<option value=''>" + $.mage.__("Select Resolution") + "</option>");

                $.each(resolutions, function(index, resolution) {
                    resolutionHtml.append("<option value='" + resolution + "'>" + resolution + "</option>");
                });

                return resolutionHtml;
            }

            function getReasonDropDown(itemId)
            {
                var combo = $("<select class='wk-reason' name='reason_ids["+itemId+"]'></select>");
                for (var key in reasons) {
                    if (reasons.hasOwnProperty(key)) {
                        var val = key;
                        if (val == 0) {
                            val = "";
                        }
                        combo.append("<option value='"+val+"'>"+reasons[key]+"</option>");
                    }
                }
                return combo;
            }

            function getDropDownList(qty, itemId)
            {
                var combo = $("<select name='total_qty["+itemId+"]' class='order_qty'></select>");
                combo.append("<option value=''>"+self.options.selectQtyLabel+"</option>");
                var count = 1;
                while (count <= qty) {
                    combo.append("<option value='"+count+"'>" + count + "</option>");
                    count++;
                };
                return combo;
            }

            function hideLoadingMask()
            {
                $(".wk-loading-mask").addClass("wk-display-none");
                $(".loading-mask").addClass("wk-display-none");
                
            }

            function showLoadingMask()
            {
                $(".wk-loading-mask").removeClass("wk-display-none");
            }

            function addConsignmentBlock()
            {
                var input = $('<input>', { class : 'input-text required-entry', id : 'consignment_number', type : 'text', 'data-validate' :'{required:true}', name:'number' });
                var html = $('<div>', { class : 'field required' });
                html.append(
                    $('<label class="label" for="consignment_number"></label>')
                    .append($('<span>', { text : consignmentLabel }))
                );
                html.append($('<div>', { class : 'control' }).append(input));
                $("#consignment_number").remove();
                $("#order_status").parent().parent().after(html);
            }

            function removeConsignmentBlock()
            {
                $("#consignment_number").parent().parent().remove();
            }


            function manageOrderStatus()
            {
                var virtualOrder = true;
                $(".order_item").each(function () {
                    if ($(this).attr("data-virtual") == 0) {
                        virtualOrder = false;
                    }
                });
                if (virtualOrder) {
                    isVirtual = 1;
                    $(".wk-order-status-field").addClass("wk-display-none");
                } else {
                    $(".wk-order-status-field").removeClass("wk-display-none");
                    var count = 0;
                    var removeStatusField = true;
                    $(".order_item").each(function () {
                        if ($(this).prop("checked") == true) {
                            count++;
                            if ($(this).attr("data-virtual") == 0) {
                                removeStatusField = false;
                            }
                        }
                    });

                    if (removeStatusField && count > 0) {
                        isVirtual = 1;
                        $(".wk-order-status-field").addClass("wk-display-none");
                    } else {
                        isVirtual = 0;
                        $(".wk-order-status-field").removeClass("wk-display-none");
                    }
                }
            }
        }
    });

    return $.mprma.newRma;
});
