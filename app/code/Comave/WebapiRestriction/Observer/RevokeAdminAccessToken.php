<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\WebapiRestriction\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Integration\Api\AdminTokenServiceInterface;
use Psr\Log\LoggerInterface;

class RevokeAdminAccessToken implements ObserverInterface
{
    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Integration\Api\AdminTokenServiceInterface $tokenService
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly AdminTokenServiceInterface $tokenService
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $user = $observer->getEvent()->getObject();
        try {
            if (!empty($user->getPassword())) {
                $this->tokenService->revokeAdminAccessToken($user->getId());
            }
        } catch (LocalizedException $e) {
            $this->logger->warning($e->getMessage());
        }
    }
}
