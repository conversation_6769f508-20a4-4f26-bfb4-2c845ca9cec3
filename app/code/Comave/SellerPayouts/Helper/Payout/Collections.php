<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Helper\Payout;

use Comave\SellerPayouts\Helper\Data as DataHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;
use Webkul\Marketplace\Model\ResourceModel\Saleslist\Collection;
use Webkul\Marketplace\Model\ResourceModel\Saleslist\CollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller\Collection as SellerCollection;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory as SellerCollectionFactory;

class Collections extends \Magento\Framework\App\Helper\AbstractHelper
{
    private readonly string $marketplaceOrdersTableName;
    private readonly string $salesOrdersTableName;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Comave\SellerPayouts\Helper\Data $dataHelper
     * @param \Webkul\Marketplace\Model\ResourceModel\Saleslist\CollectionFactory $collectionFactory
     * @param \Magento\Framework\App\ResourceConnection $resourceModel
     * @param \Webkul\Marketplace\Model\ResourceModel\Seller\Collection $sellerCollectionFactory
     */
    public function __construct(
        Context $context,
        protected DataHelper $dataHelper,
        protected CollectionFactory $collectionFactory,
        protected ResourceConnection $resourceModel,
        protected SellerCollectionFactory $sellerCollectionFactory
    ) {
        $this->marketplaceOrdersTableName = $this->resourceModel->getTableName('marketplace_orders');
        $this->salesOrdersTableName = $this->resourceModel->getTableName('sales_order');

        parent::__construct($context);
    }

    /**
     * Get seller collection
     *
     * @return \Webkul\Marketplace\Model\ResourceModel\Seller\Collection
     */
    public function getSellerCollection(): SellerCollection
    {
        $collection = $this->sellerCollectionFactory->create()
            ->addFieldToSelect('*')
            ->addFieldToFilter('is_seller', ['eq' => 1])
            ->setOrder('entity_id', 'desc');

        $collection->getSelect()
            ->join(
                ['ea' => 'eav_attribute'],
                $this->resourceModel->getConnection()->quoteInto("ea.attribute_code = ?", 'stripe_client_id'),
                []
            )->join(
                ['cev' => 'customer_entity_varchar'],
                'main_table.seller_id = cev.entity_id and ea.attribute_id = cev.attribute_id',
                ['stripe_client_id' => 'value']
            );

        return $collection;
    }

    /**
     * Get Seller Collection for delivered items function
     *
     * @param int $id
     * @return \Webkul\Marketplace\Model\ResourceModel\Saleslist\Collection
     */
    public function getSellerCollectionForDeliveredItems(int $id): Collection
    {
        // Get the seller only unpaid orders
        $collection = $this->collectionFactory->create()
            ->addFieldToFilter('paid_status', 0)
            ->addFieldToFilter('main_table.order_id', ['neq' => 0])
            ->addFieldToFilter('cpprostatus', ['neq' => 0])
            ->addFieldToFilter('main_table.seller_id', $id);

        // Join with the sales_order table to filter by delivered status and updated_at
        $collection->getSelect()
            ->join(['sales_order' => $this->salesOrdersTableName], 'main_table.order_id = sales_order.entity_id', [])
            ->where('sales_order.status = ?', 'delivered');
        if(!$this->dataHelper->isTestEnabled()){
            $collection->getSelect()->where('sales_order.updated_at <= DATE_SUB(CURDATE(), INTERVAL ? DAY)', $this->dataHelper->getDate());
        }

        $collection->getSelect()->joinLeft(
                ['wk_orders' => $this->marketplaceOrdersTableName],
                'main_table.order_id = wk_orders.order_id AND wk_orders.seller_id = main_table.seller_id',
                ['refunded_shipping_charges', 'shipping_charges', 'tax_to_seller']
            );

        return $collection;
    }
}
