<?php
    $marketplaceHelper = $block->getMpHelper();
    $isenable = false;
    $isPartner= $marketplaceHelper->isSeller();
    $magentoCurrentUrl = $block->getCurrentUrl();
    $isSellerGroup = $marketplaceHelper->isSellerGroupModuleInstalled();
?>
<?php if($isenable):?>
    <?php if ($isPartner): ?>
        <?php if (($isSellerGroup &&
        $marketplaceHelper->isAllowedAction('seller_payouts/seller/')) || !$isSellerGroup): ?>
            <li id="wk-mp-menu-seller_payouts" class="wk-mp-menu-seller_payouts level-0 
            <?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'seller_payouts/seller/') ? "current active":"");?>">
                <a 
                    href=
                    "<?=  $escaper->escapeUrl($block->getUrl('seller_payouts/seller/payouts', ['_secure' => 1])); ?>"
                    class=
                    "<?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'seller_payouts/seller/')? "active":"");?>">
                    <?=  $escaper->escapeHtml(__('Stripe Connects')) ?>
                </a>
            </li>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>