<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<?php
$helper = $this->helper(Comave\SellerPayouts\Helper\Data::class);
$isenable = $helper->isEnabled();
?>
<div id="seller-pay-block">
    <fieldset class="fieldset admin__fieldset-wrapper">
        <legend class="legend admin__legend">
            <span><?= /* @escapeNotVerified */ $block->escapeHtml(__('Seller Payment Information')) ?></span>
        </legend>
        <div class="form-inline">
        <form action="<?= /* @escapeNotVerified */ $block->escapeUrl($block->getUrl('marketplace/order/massPayseller', ['seller_id'=>$block->getRequest()->getParam('seller_id')] )) ?>" enctype="multipart/form-data" method="post" id="form-seller-order-pay-pay">
            <?= $block->getBlockHtml('formkey'); ?>
            <input type="hidden" name="wksellerorderids" id="wksellerorderids" value="" />
            <input type="hidden" name="seller_id" value="<?= /* @escapeNotVerified */ $block->escapeHtml($block->getRequest()->getParam('seller_id'))?>" />
            <div class="box-left" style="width: 100%;">
                <div class="entry-edit">
                    <div class="fieldset">
                        <table cellspacing="0" class="form-list" style="width: 100%;">
                            <tbody>
                                <tr>
                                    <td class="label"><label><?= /* @escapeNotVerified */ $block->escapeHtml(__('Seller Name ')) ?></label></td>
                                    <td class="value">
                                        <strong>
                                            <?= /* @escapeNotVerified */ $block->escapeHtml($block->getCustomer()->getName())?>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"><label><?= /* @escapeNotVerified */ $block->escapeHtml(__('Seller Email ')) ?></label></td>
                                    <td class="value">
                                        <strong>
                                            <?= /* @escapeNotVerified */ $block->escapeHtml($block->getCustomer()->getEmail())?>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"><label><?= /* @escapeNotVerified */ $block->escapeHtml(__('Total Amount To Be Pay '))?></label></td>
                                    <td class="value">
                                        <strong>
                                            <span id="wk-total-sellerprice"></span>
                                            <div data-bind="scope: 'sellerpay'">
                                                <!-- ko template: getTemplate() --><!-- /ko -->
                                            </div>
                                        </strong>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </form>
        </div>
    </fieldset>
</div>
<?php
$viewModel = $block->getViewModel();
$helper = $viewModel->getHelper();
$getPriceFormat = $viewModel->getJsonHelper()->jsonEncode(
    $helper->getPriceFormat()
);
?>
<script>
      require.config({
        map: {
            '*': {
                'Magento_Ui/js/grid/columns/multiselect':  'Webkul_Marketplace/js/grid/columns/multiselect'
            }
        }
      });
</script>
<script type="text/x-magento-init">
    {
        "*": {
            "Magento_Ui/js/core/app": {
                "components": {
                    "sellerpay": {
                        "component": "Webkul_Marketplace/js/grid/columns/multiselect",
                        "template" : "Webkul_Marketplace/pay",
                        "priceFormat" : <?= /* @noEscape */ $getPriceFormat?>
                    }
                }
            }
        }
    }
</script>
