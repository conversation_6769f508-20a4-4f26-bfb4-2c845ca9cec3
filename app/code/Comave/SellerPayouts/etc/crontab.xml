<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
	<group id="default">
		<job name="comave_sellerpayouts_payouts" instance="Comave\SellerPayouts\Cron\PayOuts" method="execute">
			<schedule>0 0 14,28 * *</schedule>
		</job>
        <job name="comave_check_connected_accounts" instance="Comave\SellerPayouts\Cron\CheckAccounts" method="execute">
            <schedule>0 8 * * *</schedule>
        </job>
	</group>
</config>
