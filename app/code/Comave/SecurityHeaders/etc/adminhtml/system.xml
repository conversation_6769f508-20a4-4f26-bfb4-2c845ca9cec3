<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="comave_securityheaders" translate="label"
                 showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Security Headers</label>
            <tab>advanced</tab>
            <resource>Comave_SecurityHeaders::config</resource>

            <group id="headers" translate="label"
                   showInDefault="1" showInWebsite="1" showInStore="0"
                   sortOrder="10">
                <label>Custom Security Headers</label>

                <field id="custom"
                       sortOrder="10"
                       showInDefault="1"
                       showInWebsite="1"
                       type="text">
                    <label>Header list</label>
                    <comment>Example: X-Frame-Options = DENY</comment>
                    <backend_model>Comave\SecurityHeaders\Model\System\Config\Backend\Headers</backend_model>
                    <frontend_model>Comave\SecurityHeaders\Block\Adminhtml\System\Config\Form\Field\Headers</frontend_model>
                </field>
            </group>
        </section>
    </system>
</config>
