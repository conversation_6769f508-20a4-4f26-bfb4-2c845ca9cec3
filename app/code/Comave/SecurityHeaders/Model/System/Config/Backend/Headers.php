<?php
declare(strict_types=1);

namespace Comave\SecurityHeaders\Model\System\Config\Backend;

use Magento\Config\Model\Config\Backend\Serialized\ArraySerialized;
use Magento\Framework\Exception\LocalizedException;

class Headers extends ArraySerialized
{
    private const EMPTY_NAME_OR_VALUE_MSG = 'Header name or value cannot be empty.';

    public function beforeSave(): self
    {
        $rows = $this->getValue();

        foreach ($rows as $idx => $row) {
            $name  = trim((string)($row['header_name']  ?? ''));
            $value = trim((string)($row['header_value'] ?? ''));

            if ($name === '' && $value === '') {
                unset($rows[$idx]);
                continue;
            }

            if ($name === '' || $value === '') {
                throw new LocalizedException(
                    __(self::EMPTY_NAME_OR_VALUE_MSG)
                );
            }

            $rows[$idx]['header_name']  = $name;
            $rows[$idx]['header_value'] = $value;
        }

        $this->setValue($rows);
        return parent::beforeSave();
    }

}
