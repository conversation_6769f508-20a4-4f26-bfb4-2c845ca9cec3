<?php
declare(strict_types=1);

namespace Comave\SecurityHeaders\Observer;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Serialize\Serializer\Json;

final class AddSecurityHeaders implements ObserverInterface
{
    private const XML_PATH_CUSTOM_HEADERS = 'comave_securityheaders/headers/custom';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly Json $json,
    ) {}

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $raw = (string) $this->scopeConfig->getValue(
            self::XML_PATH_CUSTOM_HEADERS,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        if ($raw === '') {
            return;
        }

        $rows = $this->json->unserialize($raw);
        $response = $observer->getResponse();
        foreach ($rows as $row) {
            $name  = trim($row['header_name']  ?? '');
            $value = trim($row['header_value'] ?? '');

            if ($name !== '' && $value !== '') {
                $response->setHeader($name, $value, true);
            }
        }
    }
}
