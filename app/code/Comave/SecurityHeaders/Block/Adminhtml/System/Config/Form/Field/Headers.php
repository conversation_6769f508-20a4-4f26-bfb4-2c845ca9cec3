<?php
declare(strict_types=1);

namespace Comave\SecurityHeaders\Block\Adminhtml\System\Config\Form\Field;

use Magento\Config\Block\System\Config\Form\Field\FieldArray\AbstractFieldArray;
use Magento\Framework\DataObject;

class Headers extends AbstractFieldArray
{
    protected function _prepareToRender(): void
    {
        $this->addColumn('header_name', [
            'label' => __('Header'),
            'style' => 'width:200px',
        ]);
        $this->addColumn('header_value', [
            'label' => __('Value'),
            'style' => 'width:400px',
        ]);

        $this->_addAfter       = false;
        $this->_addButtonLabel = __('Add');
    }

    protected function _prepareArrayRow(DataObject $row): void
    {
        $row->setData(
            'option_extra_attrs',
            ['data-row-index' => $row->getId()]
        );
    }
}
