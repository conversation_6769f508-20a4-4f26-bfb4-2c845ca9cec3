<?php
// Just for reference for local ddev env usage, all sensitive/credentials replaced with "whatever"
return [
    'backend' => [
        'frontName' => 'admin_whatever'
    ],
    'crypt' => [
        'key' => 'whateveryourlocalkey'
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'db',
                'dbname' => 'db',
                'username' => 'db',
                'password' => 'db',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1'
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'session' => [
        'save' => 'files'
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'id_prefix' => '40d_'
            ],
            'page_cache' => [
                'id_prefix' => '40d_'
            ]
        ],
        'graphql' => [
            'id_salt' => 'whateversalt'
        ]
    ],
    'lock' => [
        'provider' => 'db',
        'config' => [
            'prefix' => null
        ]
    ],
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 0,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'compiled_config' => 1,
        'eav' => 1,
        'customer_notification' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 0,
        'config_webservice' => 1,
        'translate' => 1,
        'vertex' => 1
    ],
    'install' => [
        'date' => 'Sun, 12 Jan 2020 22:21:37 +0000'
    ],
    'vapid_publickey' => 'whatever',
    'vapid_privatekey' => 'whatever',
    'system' => [
        'default' => [
            'payment' => [
                'payflowpro' => [
                    'partner' => null,
                    'user' => null,
                    'pwd' => null,
                    'sandbox_flag' => '0',
                    'proxy_host' => null,
                    'proxy_port' => null,
                    'debug' => '0'
                ],
                'payflow_link' => [
                    'pwd' => null,
                    'sandbox_flag' => '0',
                    'use_proxy' => '0',
                    'proxy_host' => null,
                    'proxy_port' => null,
                    'debug' => '0',
                    'url_method' => 'GET'
                ],
                'payflow_express' => [
                    'debug' => '0'
                ],
                'paypal_express_bml' => [
                    'publisher_id' => null
                ],
                'paypal_express' => [
                    'debug' => '0',
                    'merchant_id' => null
                ],
                'hosted_pro' => [
                    'debug' => null
                ],
                'paypal_billing_agreement' => [
                    'debug' => '0'
                ],
                'braintree' => [
                    'merchant_id' => null,
                    'public_key' => null,
                    'private_key' => null,
                    'merchant_account_id' => null
                ],
                'braintree_paypal' => [
                    'merchant_name_override' => null
                ],
//                'stripe_payments_basic' => [
//                    'stripe_test_sk' => 'whatever',
//                    'stripe_live_sk' => 'whatever'
//                ],
                'checkmo' => [
                    'mailing_address' => null
                ],
                'payflow_advanced' => [
                    'user' => null,
                    'pwd' => null,
                    'sandbox_flag' => '0',
                    'proxy_host' => null,
                    'proxy_port' => null,
                    'debug' => '0',
                    'url_method' => 'GET'
                ]
            ],
            'payment_all_paypal' => [
                'paypal_payflowpro' => [
                    'settings_paypal_payflow' => [
                        'heading_cc' => null,
                        'settings_paypal_payflow_advanced' => [
                            'paypal_payflow_settlement_report' => [
                                'heading_sftp' => null
                            ]
                        ]
                    ]
                ],
                'payflow_link' => [
                    'settings_payflow_link' => [
                        'settings_payflow_link_advanced' => [
                            'payflow_link_settlement_report' => [
                                'heading_sftp' => null
                            ]
                        ]
                    ]
                ],
                'payments_pro_hosted_solution' => [
                    'pphs_settings' => [
                        'pphs_settings_advanced' => [
                            'pphs_settlement_report' => [
                                'heading_sftp' => null
                            ]
                        ]
                    ]
                ],
                'express_checkout' => [
                    'settings_ec' => [
                        'settings_ec_advanced' => [
                            'express_checkout_settlement_report' => [
                                'heading_sftp' => null
                            ]
                        ]
                    ]
                ]
            ],
            'paypal' => [
                'fetch_reports' => [
                    'ftp_login' => null,
                    'ftp_password' => null,
                    'ftp_sandbox' => '0',
                    'ftp_ip' => null,
                    'ftp_path' => null
                ],
                'general' => [
                    'business_account' => null,
                    'merchant_country' => null
                ],
                'wpp' => [
                    'api_username' => null,
                    'api_password' => null,
                    'api_signature' => null,
                    'api_cert' => null,
                    'sandbox_flag' => '0',
                    'proxy_host' => null,
                    'proxy_port' => null
                ]
            ],
            'admin' => [
                'url' => [
                    'custom' => null,
                    'custom_path' => null
                ]
            ],
            'web' => [
                'unsecure' => [
                    'base_url' => 'https://comave-magento.ddev.site/',
                    'base_link_url' => '{{unsecure_base_url}}',
                    'base_static_url' => null,
                    'base_media_url' => null
                ],
                'secure' => [
                    'base_url' => 'https://comave-magento.ddev.site/',
                    'base_link_url' => '{{secure_base_url}}',
                    'base_static_url' => null,
                    'base_media_url' => null
                ],
                'default' => [
                    'front' => 'cms'
                ],
                'cookie' => [
                    'cookie_path' => null,
                    'cookie_domain' => null
                ]
            ],
            'dev' => [
                'js' => [
                    'session_storage_key' => 'collected_errors'
                ],
                'restrict' => [
                    'allow_ips' => null
                ],
                'swagger' => [
                    'active' => '1'
                ]
            ],
            'catalog' => [
                'productalert_cron' => [
                    'error_email' => null
                ],
                'product_video' => [
                    'youtube_api_key' => null
                ],
                'search' => [
                    'opensearch_server_hostname' => 'localhost',
                    'elasticsearch7_server_hostname' => 'elasticsearch',
                    'opensearch_server_port' => '9200',
                    'elasticsearch7_server_port' => '9200',
                    'opensearch_index_prefix' => 'magento2',
                    'elasticsearch7_index_prefix' => 'magento2_ddev',
                    'opensearch_enable_auth' => '0',
                    'elasticsearch7_enable_auth' => '0',
                    'opensearch_username' => null,
                    'elasticsearch7_username' => null,
                    'opensearch_password' => null,
                    'elasticsearch7_password' => null,
                    'opensearch_server_timeout' => '15',
                    'elasticsearch7_server_timeout' => '15'
                ]
            ],
            'cataloginventory' => [
                'source_selection_distance_based_google' => [
                    'api_key' => null
                ]
            ],
            'currency' => [
                'import' => [
                    'error_email' => null
                ]
            ],
            'sitemap' => [
                'generate' => [
                    'error_email' => null
                ]
            ],
            'trans_email' => [
                'ident_general' => [
                    'name' => 'Owner',
                    'email' => '<EMAIL>'
                ],
                'ident_sales' => [
                    'name' => 'Sales',
                    'email' => '<EMAIL>'
                ],
                'ident_support' => [
                    'name' => 'CustomerSupport',
                    'email' => '<EMAIL>'
                ],
                'ident_custom1' => [
                    'name' => 'Custom 1',
                    'email' => '<EMAIL>'
                ],
                'ident_custom2' => [
                    'name' => 'Custom 2',
                    'email' => '<EMAIL>'
                ]
            ],
            'contact' => [
                'email' => [
                    'recipient_email' => '<EMAIL>'
                ]
            ],
            'sales_email' => [
                'order' => [
                    'copy_to' => null
                ],
                'order_comment' => [
                    'copy_to' => null
                ],
                'invoice' => [
                    'copy_to' => null
                ],
                'invoice_comment' => [
                    'copy_to' => null
                ],
                'shipment' => [
                    'copy_to' => null
                ],
                'shipment_comment' => [
                    'copy_to' => null
                ],
                'creditmemo' => [
                    'copy_to' => null
                ],
                'creditmemo_comment' => [
                    'copy_to' => null
                ],
                'magento_rma' => [
                    'copy_to' => null
                ],
                'magento_rma_auth' => [
                    'copy_to' => null
                ],
                'magento_rma_comment' => [
                    'copy_to' => null
                ],
                'magento_rma_customer_comment' => [
                    'copy_to' => null
                ]
            ],
            'checkout' => [
                'payment_failed' => [
                    'copy_to' => null
                ]
            ],
            'carriers' => [
                'ups' => [
                    'is_account_live' => '0',
                    'access_license_number' => null,
                    'gateway_xml_url' => 'https://onlinetools.ups.com/ups.app/xml/Rate',
                    'gateway_rest_url' => 'https://wwwcie.ups.com/api/rating/',
                    'password' => null,
                    'username' => null,
                    'gateway_url' => 'https://www.ups.com/using/services/rave/qcostcgi.cgi',
                    'shipper_number' => null,
                    'tracking_url' => 'https://onlinetools.ups.com/ups.app/xml/Track',
                    'tracking_rest_url' => 'https://wwwcie.ups.com/api/track/',
                    'debug' => '0'
                ],
                'usps' => [
                    'gateway_url' => 'https://production.shippingapis.com/ShippingAPI.dll',
                    'gateway_secure_url' => 'https://secure.shippingapis.com/ShippingAPI.dll',
                    'userid' => null,
                    'password' => null
                ],
                'fedex' => [
                    'account' => null,
                    'api_key' => null,
                    'secret_key' => null,
                    'sandbox_mode' => '0',
                    'production_webservices_url' => 'https://apis.fedex.com/',
                    'sandbox_webservices_url' => 'https://apis-sandbox.fedex.com/',
                    'smartpost_hubid' => null
                ],
                'dhl' => [
                    'id' => null,
                    'password' => null,
                    'account' => null,
                    'debug' => '0',
                    'gateway_url' => 'https://xmlpi-ea.dhl.com/XMLShippingServlet'
                ]
            ],
            'google' => [
                'analytics' => [
                    'account' => null,
                    'container_id' => null
                ],
                'gtag' => [
                    'analytics4' => [
                        'measurement_id' => null,
                        'container_id' => null
                    ],
                    'adwords' => [
                        'conversion_id' => null
                    ]
                ]
            ],
            'promo' => [
                'magento_reminder' => [
                    'identity' => 'general'
                ]
            ],
            'recaptcha_backend' => [
                'type_recaptcha' => [
                    'public_key' => 'whatever',
                    'private_key' => 'whatever'
                ],
                'type_invisible' => [
                    'public_key' => null,
                    'private_key' => null
                ],
                'type_recaptcha_v3' => [
                    'public_key' => null,
                    'private_key' => null
                ]
            ],
            'recaptcha_frontend' => [
                'type_recaptcha' => [
                    'public_key' => 'whatever',
                    'private_key' => 'whatever'
                ],
                'type_invisible' => [
                    'public_key' => null,
                    'private_key' => null
                ],
                'type_recaptcha_v3' => [
                    'public_key' => null,
                    'private_key' => null
                ]
            ],
            'system' => [
                'smtp' => [
                    'host' => 'localhost',
                    'port' => '25'
                ],
                'full_page_cache' => [
                    'varnish' => [
                        'access_list' => 'localhost',
                        'backend_host' => 'localhost',
                        'backend_port' => '8080'
                    ]
                ],
                'magento_scheduled_import_export_log' => [
                    'error_email' => null
                ],
                'release_notification' => [
                    'content_url' => null,
                    'use_https' => '1'
                ]
            ],
            'adobe_ims' => [
                'integration' => [
                    'api_key' => null,
                    'private_key' => null
                ]
            ],
            'newrelicreporting' => [
                'general' => [
                    'api_url' => 'https://api.newrelic.com/v2/applications/%s/deployments.json',
                    'insights_api_url' => 'https://insights-collector.newrelic.com/v1/accounts/%s/events',
                    'account_id' => null,
                    'app_id' => null,
                    'api' => null,
                    'insights_insert_key' => null
                ]
            ],
            'analytics' => [
                'general' => [
                    'token' => null
                ],
                'url' => [
                    'signup' => 'https://advancedreporting.rjmetrics.com/signup',
                    'update' => 'https://advancedreporting.rjmetrics.com/update',
                    'bi_essentials' => 'https://dashboard.rjmetrics.com/v2/magento/signup',
                    'otp' => 'https://advancedreporting.rjmetrics.com/otp',
                    'report' => 'https://advancedreporting.rjmetrics.com/report',
                    'notify_data_changed' => 'https://advancedreporting.rjmetrics.com/report'
                ]
            ],
            'crontab' => [
                'default' => [
                    'jobs' => [
                        'analytics_collect_data' => [
                            'schedule' => [
                                'cron_expr' => '00 02 * * *'
                            ]
                        ]
                    ]
                ]
            ]
        ],
        'websites' => [
            'venstation' => [
                'web' => [
                    'unsecure' => [
                        'base_url' => 'https://vensta.ddev.site/',
                        'base_link_url' => 'https://vensta.ddev.site/'
                    ],
                    'secure' => [
                        'base_url' => 'https://vensta.ddev.site/',
                        'base_link_url' => 'https://vensta.ddev.site/'
                    ]
                ]
            ],
            'foodcomave' => [
                'web' => [
                    'unsecure' => [
                        'base_url' => 'https://foodcomave.ddev.site/',
                        'base_link_url' => 'https://foodcomave.ddev.site/'
                    ],
                    'secure' => [
                        'base_url' => 'https://foodcomave.ddev.site/',
                        'base_link_url' => 'https://foodcomave.ddev.site/'
                    ]
                ]
            ]
        ]
    ],
    'db_logger' => [
        'output' => 'disabled',
        'log_everything' => 1,
        'query_time_threshold' => '0.001',
        'include_stacktrace' => 1
    ],
    'queue' => [
        'amqp' => [
            'host' => 'rabbitmq',
            'port' => '5672',
            'user' => 'rabbitmq',
            'password' => 'rabbitmq',
            'virtualhost' => '/'
        ],
    ],
];
