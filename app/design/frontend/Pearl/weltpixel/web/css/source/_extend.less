a {
	outline: none;
	&:focus {
		outline: none;
	}
}

// Navigation - overwrites
.nav-sections {
	margin-bottom: 0;
}

/* Remove box-shadow for all inputs on focus */
._keyfocus *:focus,
input:not([disabled]):focus,
textarea:not([disabled]):focus,
select:not([disabled]):focus {
    /* Visible in the full-colour space */
    box-shadow: 0 0 3px 1px #00699d;

    /* Visible in Windows high-contrast themes */
    outline: transparent dotted 2px;
}

/**
 * fixing image dimensions in order view page
 */
.tooltip-image img
{
    max-width: 80px;
    max-height: 80px;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
}


.navigation {
	font-weight: normal;
	.level0 {
		> .level-top {
			line-height: 45px;
			:root .page-header-v3 &,
			:root .page-header-v4 & {
				line-height: 34px;
			}
		}
		.submenu {
			background-color: white;
			&:hover {
				background-color: white;
			}
			a {
				&.ui-state-focus,
				&:hover {
					background: transparent;
					//color: red;
					text-decoration: none;
				}
			}
		}
	}
}

.cart-summary .checkout{
    position: sticky;
}

.nav-sections-item-title {
	background-color: white;
	border-color: lightgrey;
}
/* COMPARE PRODUCTS */
.theme-pearl{
	&.catalog-product-compare-index{
		.table-comparison{
			overflow: hidden !important;
			tbody{
				border: none !important;
				tr:hover{
					background: #d9edf7;
				}
				td.info{
					background: none;
				}
			}
			.cell.label{
				border-right: none;
			}
			.cell.product.info,
			.cell.product.attribute{
				border-right: 1px solid #ccc;
				border-left: 1px solid #ccc;
			}
			.cell.label,
			.cell.product.info{
				border-bottom: none !important;
			}
		}
	}
}

/* CHECKOUT PAGE */
.checkout-agreements{
	button{
		background: none !important;
		border:none !important;
		span{
			color:#fa583f !important;
			font-size: 16px !important;
			&:hover{
				text-decoration: underline;

			}
		}
	}
}
.checkout-index-index{
	.modal-popup{
		.modal-footer{
			.action-hide-popup{
				padding: 7px 15px;
				&:hover{
					text-decoration: none !important;
				}
			}
		}
	}
}
#checkout {
	.opc-wrapper {
		width: 66.6667%;
		@media (max-width: 767px) {
			width: 100%;
		}
	}
}

// weltpixel_theme_fix

/* pe pagina de produse repara dropdownul de cantitate */
.theme-pearl.catalog-product-view .box-tocart .field.qty select {
	padding: 15px;
	direction: inherit;
}
.theme-pearl {
	.block.block-minicart {
		.quickcart-content-wrapper button.action span,
		button.action.primary span {
			font-size: 12px;
		}
	}
	.header .quickcart-wrapper .block-quickcart .block-content-quickcart .quickcart-items-wrapper .quickcart-items .action.delete:before {
		content: '' !important;
	}
	.modal-custom .action-close, .modal-popup .action-close, .modal-slide .action-close {
		background: none;
		border: none;
		&:hover {
			background: none;
			border: none;
		}
	}
	.swatch-option{
		margin: 0 15px 5px 0;
	}
	.breadcrumbs{
		.items{
			strong,
			a{
				text-transform: uppercase !important;
			}
		}
	}
	#wishlist-sidebar{
		.product-image-container{
			width: 75px !important;
			.product-image-photo{
				width: auto !important;
				height: auto !important;
			}
		}
		.actions-primary{
			a{
				span{
					font-size: 18px !important;
				}
			}
		}
	}
}


/* adds padding to switcher drop down */
:root .page-header #switcher-currency .mage-dropdown-dialog .dropdown.switcher-dropdown,
:root .page-header #switcher-language .mage-dropdown-dialog .dropdown.switcher-dropdown {
	padding: 10px !important;
}

/* header-v2 account icon size style after login */
:root .page-header-v2 .page-header .header.links>.customer-welcome .customer-name:before{
    content:"\e66c";
    font-family:inherit;
}

/* product page related items add to compare and favorite fix */

.secondary-addto-links.actions-secondary {
	display: none;
}

.product-item-info:hover .secondary-addto-links.actions-secondary {
	display: inherit;
}

.owl-carousel .owl-item {
	text-align: center;
}

/* navigation underscore */
@media (min-width: 768px) {
	.navigation .level0.level-top > .level-top, .navigation .level0 > .level-top {
		border-bottom: 1px solid transparent;
	}
	.navigation .level0.active > .level-top, .navigation .level0.has-active > .level-top {
		border-bottom: 1px solid #000000;

	}

	.navigation .level0 .submenu .active > a {
		border-color: #000;
	}

	.navigation .level0 > .level-top {
		display: inline-block;
		line-height: 38px;
		margin-bottom: 0;
	}

}

.weltpixel-quickview-catalog-product-view.catalog-product-view .box-tocart .actions button,
.weltpixel_quickview-catalog_product-view.catalog-product-view .box-tocart .actions button {
	float: none;
}

.weltpixel-quickview-catalog-product-view.catalog-product-view .box-tocart .actions,
.weltpixel_quickview-catalog_product-view.catalog-product-view .box-tocart .actions {
	float: none;
}

.mfp-iframe-holder .mfp-close {
	border: none !important;
	background: none !important;
}

.page-wrapper {
	.page-footer {
		padding: 0;
		.footer.content {
			padding: 0;
			.pre-footer {
				background-color: #f4f4f4;
				display: flex;
				.pre-footer-content {
					max-width: 1400px;
					width: 100%;
					margin: 0 auto;
					padding: 50px 0;
					.pre-footer-subtitle {
						display: block;
						font-weight: bold;
						text-align: center;
					}
					.pre-footer-title {
						text-align: center;
						display: block;
						font-family: 'Source Sans Pro', sans-serif;
						font-style: italic;
						font-weight: 300;
						line-height: 1.1;
						color: #303030;
					}
					.footer-icon {
						font-size: 35px;
						display: block;
						text-align: center;
						color: #aaa;
					}
				}
			}
			.footer-v1-content {
				margin: 0 auto;
				.nopaddingleft{
					padding-left: 0px;
				}
				.mini-logo{
					padding-top: 50px;
					margin-top: 0px;
				}
				p{
					color:#575757;
					margin-bottom: 6px;
				}
				.links-v1 li{
					margin-bottom: 6px;
					a{
						color:#575757;
					}
				}
				.footer-title {
					font-size: 16px;
					padding: 50px 0 10px 0px;
					text-transform: uppercase;
					font-weight: 600;
				}
				.border-v1{
					border-top:1px solid #adadad;
					margin-top: 30px;
					padding-right: 0px;
					padding-left: 0px;
					.small-text{
						margin-top: 15px;
						display: block;
					}
					.social-icons-v1{
						margin-top: 10px;
						.social-icons{
							margin: 0px;
							i{
								font-size: 15px;
							}
						}
					}
				}
			}
			.footer-section2-content {
				margin: 0 auto;
			}
			.footer-v2 {
				padding-top: 60px;
				.social-icons{
					margin: 0 20px 5px 0;
				}
				.footer-nav {
					a {
						padding: 10px;
						cursor: pointer;
					}
				}
				.footer-nav, .togglet.newsletter {
					font-size: 12px;
					line-height: 2em;
					text-transform: uppercase;
					letter-spacing: .2em;
					font-weight: 400;
					padding: 10px;
				}
				.block.newsletter {
					margin: 0 auto;
					input, .action.subscribe {
						height: 50px;
					}
					.field .control:before {
						line-height: 50px;
						font-size: 20px;
					}
				}
				.block {
					float: initial !important;
				}
			}
			.footer-v3{
				padding-bottom: 10px;
				padding-top: 60px;
				z-index: 0;
				position: relative;
				.footer-v3-list,
				.address-v3{
					line-height: 1.6;
				}
				.address-v3 i{
					padding-right: 5px;
				}
				.white-lnk{
					color: #ccc;
				}
			}
			.footer-v4{
				.footer-nav, .togglet.newsletter {
					font-size: 12px;
					line-height: 2em;
					text-transform: uppercase;
					letter-spacing: .2em;
					font-weight: 400;
					padding: 10px;
				}
			}
		}
	}
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.filter.active .filter-title{
		border-bottom: none;
	}
	.checkout-index-index .authentication-wrapper{
		margin-top: -18px !important;
		position: absolute;
		right: 15px;
	}
	.checkout-index-index {
		.sl-popup {
			.modal-popup {
				.modal-inner-wrap {
					margin: 0 auto;
				}
			}
		}
	}
	.navigation .submenu:not(:first-child) .level1.active > a,
	.navigation .submenu:not(:first-child) .active > a,
	.navigation .level0.active > a:not(.ui-state-active),
	.navigation .level0.has-active > a:not(.ui-state-active) {
		border-color: #000;
	}
	.footer.content {
		.footer-v1{
			.footer-title{
				&:after{
					content: "";
					width: 40%;
					height: 1px;
					display: block;
					margin: 5px auto 0 auto;
					border-top: 1px solid #cecece;
				}
			}
			.links{
				li{
					border-top: none;
					background: transparent;
				}
			}
		}
		.footer-v2,
		.footer-v4{
			.nopadding-left-mob{
				padding-left: 0px;
			}
		}

		.links > li {
			padding: 10px 25px;
			> a {
				padding: 0px;
			}
		}
	}
	.nav-sections-item-title a.nav-sections-item-switch {
		text-transform: uppercase;
	}
}
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.page-wrapper{
		.page-footer{
			.footer.content{
				.footer-v2{
					.block.newsletter{
						.actions{
							display: block;
							text-align: center;
							margin: 10px auto;
							width: auto;
						}
					}
				}
				.footer-v3{
					h4.no-padding-mob{
						padding-bottom: 0px;
					}
					.footer-v3-list{
						line-height: 1.8 !important;
					}
					.mg-mobile{
						a.social-icons{
							margin: 0 20px 5px 0;
						}
					}
					.details-v3{
						margin-bottom: 30px;
						a{
							color: #cccccc;
						}
					}
				}
				.pre-footer{
					.pre-footer-content{
						.pre-footer-subtitle{
							font-size: 14px;
						}
						.pre-footer-title{
							font-size: 20px;
						}
					}
				}
			}
		}
	}
}
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s){
	.review-form{
		max-width: 100%;
	}
}
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
	.page-wrapper{
		.page-footer{
			.footer.content{
				.pre-footer{
					.pre-footer-content{
						.pre-footer-subtitle{
							font-size: 16px;
						}
						.pre-footer-title{
							font-size: 30px;
						}
					}
				}
			}
		}
	}
}
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l){
	.footer-v3{
		.details-v3 {
			margin-bottom: 20px;
		}
		.mg-desk-v3{
			margin-bottom: 30px;
			display: block;
		}
	}
}

/** CMS DEMO PAGES **/

.hp-title {
	font-family: 'Source Sans Pro', sans-serif;
	font-style: italic;
	font-weight: 300;
	line-height: 50px;
	font-size: 40px;
	letter-spacing: 10px;
	padding: 50px 0;
	.subtitle {
		display: block;
		font-style: initial;
		font-size: 25px;
		font-weight: bold;
		letter-spacing: initial;
		line-height: inherit;
	}
}

/* image hover */

.promo {
	position: relative;
	z-index: 0;
}

body:not(.device-touch) .promo-overlay {
	-webkit-transition: opacity .3s ease;
	-o-transition: opacity .3s ease;
	transition: opacity .3s ease;
}

.promo-image {
	position: relative;
	> a, img {
		display: block;
		width: 100%;
	}
	.title {
		color: #fff;
		text-align: center;
		span {
			display: block;
			font-size: 16px;
			text-transform: lowercase;
			.upper {
				text-transform: uppercase;
			}
		}
	}
	.title-v2 {
		color: #000;
		span {
			display: block;
			font-size: 16px;
			text-transform: uppercase;
		}
	}
}

.promo-overlay {
	.title {
		color: #000;
		font-size: 25px;
		font-style: italic;
		text-align: center;
		padding: 20px;
		span {
			display: block;
			font-size: 16px;
		}
	}
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
	/*COMPARE PAGE*/
	.theme-pearl{
		&.catalog-product-compare-index{
			.product-item-actions{
				.actions-primary{
					display: inline-block;
				}
				.secondary-addto-links.actions-secondary{
					display: inline-block;
					width: auto;
				}
			}
		}
	}

	/*HOME PAGE*/
	.theme-pearl{
		&.cms-index-index{
			.product-item-actions {
				.actions-primary,
				.actions-secondary {
					display: table-cell;
				}
			}
			.owl-carousel {
				.product-item-actions {
					.actions-primary {
						display: initial;
					}
					.secondary-addto-links.actions-secondary {
						display: none;
					}
				}
				.product-item-info:hover {
					.product-item-actions {
						.secondary-addto-links.actions-secondary {
							display: table-cell;
						}
					}
				}
			}
		}
	}

	.review-form{
		max-width: 500px;
	}
	.footer.content .links {
		padding-right: 0px;
	}
	.footer-v1-content{
		.pull-right-md{
			float: right;
		}
		.pull-left-md{
			float: left;
		}
	}
	.promo-overlay,
	.promo-overlay:before {
		opacity: 0;
		position: absolute;
		z-index: 2;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
}

	.promo-overlay:before {
		content: '';
		z-index: auto;
		background-color: #FFF;
		opacity: 0.25;
	}

	.brighter-promo .promo-overlay:before {
		opacity: 0.75;
	}

	.promo-overlay .title {
		position: absolute;
		width: 100%;
		bottom: 40%;
		padding: 0;
	}

	.promo-image .title {
		position: absolute;
		width: 100%;
		bottom: 50%;
		padding: 20px 20px 20px 50px;
		text-align: center;
		z-index: 100;
		font-size: 48px;
	}

	.promo-image .title-v2 {
		position: absolute;
		width: 100%;
		bottom: 50%;
		padding: 50px;
		text-align: left;
		z-index: 2;
		font-size: 48px;
	}

}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.promo-image {
		.title {
			font-size: 25px;
			padding: 20px;
		}
		.title-v2 {
			text-align: center;
			font-size: 25px;
			padding: 20px;
		}
	}

	.page-wrapper .page-footer {
		float: left;
	}

	.page-wrapper{
		.page-footer{
			.footer.content{
				.footer-v1-content{
					.nopaddingleft{
						padding-right: 0px;
					}
				}
			}
		}
	}
}

.img-block {
	display: block;
}

.light {
	color: #fff;
}

.header_right {
	z-index: 100;
}

.promo:hover .promo-overlay {
	opacity: 1;
}

.section.parallax {

	.block.newsletter {
		margin: 0 auto;
		input, .action.subscribe {
			height: 50px;
		}
		.field .control:before {
			line-height: 50px;
			font-size: 20px;
		}
	}

}

.social-container {
	.social-icons {
		background-color: rgba(225, 225, 225, 0.5);
		font-size: 20px !important;
	}
}

/* Demo Page v3 */

.heading-title span {
	display: block;
	margin-bottom: 15px;
	text-transform: uppercase;
	font-weight: bold;
	font-size: 16px;
}

.page-footer {
	background-color: #f4f4f4;
	padding-top: 60px;
	display: inline-block;
	width: 100%;
}

#page-footer-section-03,
#page-footer-section-03 a {
	color: #fff;
}

#page-footer-section-03 a:hover {
	text-decoration: none;
}

.footer.content {
	border: none;
}

h1, h2, h3, h4, h5, h6 {
	text-transform: uppercase;
}

h3 strong, h4 strong {
	font-size: 24px !important;
}

.fancy-title.title-bottom-border h1,
.fancy-title.title-bottom-border h2,
.fancy-title.title-bottom-border h3,
.fancy-title.title-bottom-border h4,
.fancy-title.title-bottom-border h5,
.fancy-title.title-bottom-border h6 {
	border-bottom: 2px solid #d83701;
}

.faqlist li {
	a {
		color: #444;
		font-size: 14px;
		font-weight: 900;
		&:hover, &:active {
			color: #d83701 !important;
			text-decoration: none;
		}
	}
}

.contact-index-index {
	.column {
		.contact-container {
			margin-top: 50px;
			h4 {
				padding: 17px 0;
				font-size: 20px;
				text-transform: uppercase;
				font-weight: 400;
			}
			.form.contact {
				width: 100%;
				float: none;
			}
		}
	}
	.form-area {
		padding-bottom: 15px;
		.input-text {
			border: 0px solid #D1D2D4;
			width: 100%;
			border-bottom: 1px solid #ccc;
			-webkit-box-shadow: inset 0px 0px 5px 0px #fff;
			-moz-box-shadow: inset 0px 0px 5px 0px #fff;
			box-shadow: inset 0px 0px 5px 0px #fff;
		}
		:after {
			content: "";
			display: block;
			background-color: rgb(248, 248, 248);
			position: absolute;
			width: 9999px;
			top: 0;
			right: 100%;
			bottom: 0;
			z-index: -1;
		}
	}

	#comment {
		background-image: -webkit-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);
		background-image: -moz-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);
		background-image: -ms-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);
		background-image: -o-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);
		background-image: linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);
		background-size: 100% 37px;
		line-height: 38px;
		height: 250px;
		padding: 0px;
	}

	.page-title h1 {
		font-size: 26px;
	}
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.account .page.messages {
		margin-bottom: 25px;
		margin-top: 35px;
	}
	.contact-index-index .col-contact-info {
		text-align: center;
		padding-bottom: 20px;
	}
	.sales-order-history{
		&.account{
			.table-wrapper{
				border-bottom: none;
			}
		}
	}
}

& when (@media-common = true) {

	.theme-pearl {
        .payment-method-braintree .hosted-control {
            -webkit-box-sizing: initial;
            -moz-box-sizing: initial;
            box-sizing: initial;
        }
		&.weltpixel-quickview-catalog-product-view,
		&.weltpixel_quickview-catalog_product-view {
			.page-wrapper .page-main {
				padding: 20px;
			}
		}
		img, object, video, embed {
			max-height: initial;
		}
        .grecaptcha-badge {
            box-shadow: none !important;
        }
	}

	.page-products .columns {
		z-index: 0;
	}

	.minicart-wrapper .action.showcart .counter.qty {
		background-color: white;
		border: 1px solid black;
		color: black;
		line-height: 23px;
		.counter-number {
			color: black;
			text-shadow: none;
		}
	}

    .minicart-wrapper .product .actions {
        float: right;
    }

	#block-discount-heading,
	#block-giftcard-heading,
	.gift-item-block .title {
		color: black;
		font-size: 1.6rem;
	}

	#discount-coupon-form, #giftcard-form {
		.actions-toolbar {
			.action.primary {
				line-height: 16px;
				span {
					line-height: 16px;
				}
			}
		}
	}

	#giftcard-form .secondary button span {
		color: black;
	}

	.checkout-cart-index {
		.gift-message-summary {
			margin: 10px 0 0;
			padding: 10px 0 0;
		}
		.cart.table-wrapper .actions-toolbar {
			-webkit-box-sizing: content-box;
			-moz-box-sizing: content-box;
			box-sizing: content-box;
		}
		.cart.table-wrapper .gift-summary .actions-toolbar {
			top: 8px;
			position: relative;
			float: left;
		}
		.gift-summary {
			.action-edit:before,
			.action-delete:before {
				font-size: 15px
			}
			.action-edit:before {
				font-family: font-icons;
				content: '\e635';
			}
			.action-delete:before {
				content: '\e616';
			}
		}
		.gift-options-cart-item {
			> a.action.action-gift {
				font-weight: 700;
				&:after {
					font-size: 12px;
				}
			}
		}
		.gift-summary .actions-toolbar > .secondary .action {
			&:first-of-type {
				margin: 0;
			}
		}
		.cart.table-wrapper .actions-toolbar > .action-edit:before,
		.cart.table-wrapper .actions-toolbar > .action-delete:before {
			font-size: 20px;
		}
	}
	.customer-welcome .action.switch {
		font-size: 15px;
		&:after {
			color: #000000;
			line-height: 20px;
		}
	}
	.page-header .switcher strong,
	.page-footer .switcher strong,
	.header.panel > .header.links > li.welcome,
	.header.panel > .header.links > li > a {
		line-height: 1.5;
	}

	//footer

	footer.page-footer {
		padding-top: 60px;
		padding-bottom: 0px !important;
	}

	.footer-nav,
	.togglet.newsletter {
		font-size: 12px;
		line-height: 2em;
		text-transform: uppercase;
		letter-spacing: .2em;
		font-weight: 400;
		padding: 10px;
	}

	.footer-nav a {
		padding: 10px;
	}

	.block.newsletter {
		margin: 0 auto;
	}
	.block.newsletter input{
		padding-left: 60px;
	}
	.block.newsletter input,
	.block.newsletter .action.subscribe {
		height: 50px;
	}

	.block.newsletter .field .control:before {
		line-height: 50px;
		font-size: 20px;
		margin-left: 24px;
	}

	.footer.content .block {
		float: initial!important;
	}

	.page-product-downloadable {
		.product-add-form {
			.product-options-wrapper,
			.product-options-bottom {
				float: none;
				width: 100%;
			}
			.product-options-bottom {
				.price-box {
					display: none;
				}
			}
		}
	}

	.block .block-content {
		ol.product-items {
			font-size: 0px;
		}
	}

}

/* ORDER AND RETURNS STYLE */
.track-order-description{
	max-width: 100%;
	text-align: center;
}
.track-order{
	background: rgba(84,84,84,0.05);
	margin: 0 auto 57px auto;
	position: relative;
	&:before{
		background-image: radial-gradient(closest-side, transparent 9px, rgba(84,84,84,0.05) 100%);
		position: absolute;
		top: -10px;
		left: 0;
		content: '';
		display: block;
		height: 10px;
		width: 100%;
		background-color: transparent;
		-webkit-background-size: 20px 20px;
		background-size: 20px 20px;
		background-position: 0 10px, 40px 40px;
	}
	&:after{
		background-image: radial-gradient(closest-side, transparent 9px, rgba(84,84,84,0.05) 100%);
		position: absolute;
		bottom: -10px;
		left: 0;
		transform: rotate(180deg);
		-ms-transform: rotate(180deg);
		-webkit-transform: rotate(180deg);
		content: "";
		display: block;
		height: 10px;
		width: 100%;
		background-color: transparent;
		-webkit-background-size: 20px 20px;
		background-size: 20px 20px;
		background-position: 0 10px, 40px 40px;
	}
	input{
		height: 40px;
		::-webkit-input-placeholder { /* Chrome/Opera/Safari */
			color: #444 !important;
		}
		::-moz-placeholder { /* Firefox 19+ */
			color: #444 !important;
		}
		:-ms-input-placeholder { /* IE 10+ */
			color: #444 !important;
		}
		:-moz-placeholder { /* Firefox 18- */
			color: #444 !important;
		}
	}

	label{
		line-height: 26px;
		font-weight: 700;
		margin-top: 8px;
		display: block;
	}
	.field.lastname{
		margin-bottom: 21px;
	}
	.primary{
		width: 100%;
		.action.submit.primary{
			width: 100%;
			margin-right: 0px;
			padding: 16px;
		}
	}
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
	.track-order-description{
		width: 718px;
		margin: 57px auto;

	}
	.track-order{
		width: 460px;
		padding: 43px 60px 60px;
		.primary{
			margin-top: 20px;
			&.button{
				margin-top: 20px;
			}
		}
	}
}
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
	.form-orders-search{
		.open-modal-search .track-order{
			padding: 33px 30px 50px;
			margin: 57px auto;
			.primary{
				margin-top: 20px;
			}
		}
	}
	.theme-pearl {
		&.wishlist-index-index {
			.column.main {
				.products-grid.wishlist {
					.product-item {
						padding-left: 0px;
						&:hover {
							padding: 20px 0 20px 0 !important;
							background: none;
						}

						.product-item-info {
							.product-item-photo {
								.photo.image {
									max-width: 80px;
								}
							}
							&:hover {
								background: none !important;
								-webkit-box-shadow: none !important;
								-moz-box-shadow: none !important;
								box-shadow: none !important;
								border: none !important;
								.product-item-inner {
									-webkit-box-shadow: none;
									-moz-box-shadow: none;
									box-shadow: none;
									border: none;
								}
							}
						}
					}
					.product-item-tooltip {
						text-align: center;
					}
				}
			}
		}
	}
}

@import '_static_pages.less';
@import '_wp_custom.less';
