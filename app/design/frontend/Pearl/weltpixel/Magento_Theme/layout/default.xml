<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="theme-pearl"/>
        <referenceBlock name="form.subscribe" remove="true"/>
        <referenceBlock name="footer_links" remove="true"/>
        <move element="store_switcher" destination="header.panel" after="store_language" />
        <referenceBlock name="store.settings">
            <block class="Magento\Store\Block\Switcher" name="store.settings.storeswitcher"
                   template="switch/stores.phtml">
                <arguments>
                    <argument name="id_modifier" xsi:type="string">nav</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceContainer name="footer">
            <block class="Magento\ReCaptchaUi\Block\ReCaptcha"
                   name="pearl-recaptcha-newsletter"
                   after="-"
                   template="Magento_Theme::msp_recaptcha_newsletter_pearl.phtml"
                   ifconfig="recaptcha_frontend/type_for/newsletter">
                <arguments>
                    <argument name="recaptcha_for" xsi:type="string">wpn-recaptcha-newsletter-pearl</argument>
                    <argument name="jsLayout" xsi:type="array">
                        <item name="components" xsi:type="array">
                            <item name="recaptcha" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </block>
        </referenceContainer>
        <referenceContainer name="header.custom">
            <referenceBlock name="my-top-link" remove="true"/>
            <referenceBlock name="top-link" remove="true"/>
            <referenceBlock name="my-ink" remove="true"/>
            <block class="Magento\Framework\View\Element\Text" name="emtpy">
                <arguments>
                    <argument name="text" xsi:type="string">&#160;</argument>
                </arguments>
            </block>
        </referenceContainer>
        <referenceBlock name="currency" remove="true"/>
        <referenceBlock name="wish-list-link-custom" remove="true"/>
        <referenceBlock name="store_language" remove="true"/>
        <referenceContainer name="compare-link-wrapper" remove="true"/>
        <referenceBlock name="header.panel" remove="true"/>
        <referenceBlock name="minicart" remove="true"/>
        <referenceBlock name="top.search" remove="true"/>
        <referenceBlock name="div.sidebar.additional" remove="true"/>
        <referenceBlock name="customer_form_register" remove="true"/>
        <referenceBlock name="bell-notify" remove="true"/>
        <referenceBlock name="marketplace_tab" remove="true"/>
        <referenceBlock name="dashboard_tab" remove="true"/>
        <referenceBlock name="customer_account_dashboard_info" remove="true"/>
        <referenceBlock name="navlink.container" remove="true"/>
        <referenceBlock name="call_onall_pages" remove="true"/>
        <referenceBlock name="view_order_modal" remove="true"/>
        <referenceBlock name="authentication-popup" remove="true"/>
        <referenceBlock name="customer.data.invalidation.rules" remove="true"/>
        <referenceBlock name="order_items" remove="true"/>
        <referenceBlock name="sales.order.items.renderers.default" remove="true"/>
        <referenceBlock name="sales.order.info" remove="true"/>
        <referenceBlock name="sales.order.history" remove="true"/>
        <referenceBlock name="customer_account_navigation" remove="true"/>
        <referenceBlock name="customer_address_edit" remove="true"/>
        <referenceBlock name="page.main.title" remove="true"/>
        <referenceBlock name="customer_edit" remove="true"/>
        <referenceBlock name="customer.wishlist" remove="true"/>
        <referenceBlock name="product.reviews.wrapper" remove="true"/>
        <referenceContainer name="page.bottom" remove="true"/>
        <referenceBlock name="breadcrumbs" remove="true"/>
        <referenceContainer name="div.sidebar.main" remove="true"/>
        <referenceContainer name="product.top.main" remove="true"/>
        <referenceBlock name="product_list_toolbar" remove="true"/>
        <referenceBlock name="category.product.list.additional.wishlist_addto" remove="true"/>
        <referenceBlock name="category.products.list" remove="true"/>
        <referenceBlock name="search_rewsult_list" remove="true"/>
        <referenceBlock name="review_customer_list" remove="true"/>
        <referenceBlock name="invitations_list" remove="true"/>
        <referenceBlock name="sales.order.invoice" remove="true"/>
        <referenceBlock name="seller.orderitem.info" remove="true"/>
        <referenceContainer name="club.custom.block" remove="true"/>
        <referenceBlock name="custom_tab_sk" remove="true"/>
        <referenceBlock name="invitationForm" remove="true"/>
        <referenceBlock name="address_book" remove="true"/>
        <referenceBlock name="address_grid" remove="true"/>
        <referenceBlock name="sales.order.view" remove="true"/>
        <referenceBlock name="play-now" remove="true"/>
    </body>
</page>
