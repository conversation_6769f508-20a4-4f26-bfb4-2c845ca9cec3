<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @var \Magento\Theme\Block\Html\Header $block
 * @var \Magento\Framework\Escaper $escaper
 * @see \Magento\Reward\Block\Customer\Reward\Info
 * @var \Magento\Reward\Block\Customer\Reward\Info $block
 */
$welcomeMessage = $block->getWelcome();

$blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
$blockClub = $block->getLayout()->createBlock('Comave\Club\Block\Banner');

if($blockName->isCustomerLoggedIn()) {
    $fanId = $blockName->getFanId();
}
$clubCollection = 0;
$clubPrefixColor = '';
$clubSecondColor = '';
$clubThirdColor = '';
$clubWatermark = '';
$collection = $blockClub->getClubData();
$website = $blockClub->getWebName();

if($collection->getSize() > 0){
        $clubCollection = 1;

        foreach($collection as $custData){
            $clubPrefixColor = $custData->getWkvClubFirstColor();
            $clubSecondColor = $custData->getWkvClubSecondColor();
            $clubThirdColor = $custData->getWkvClubThirdColor();
            $clubWatermark = $custData->getWkvClubWatermarkImage();
        }
    }
?>

<li class="lixlink" data-bind="scope: 'customer'">
    <!-- ko ifnot: customer().fullname  -->
    <span class="lixlink-label"><?php echo __('LIX Rewards') ?></span>
    <span class="cust-guest"><?php echo __('Guest!') ?></span>
    <!-- /ko -->
    <!-- ko if: customer().fullname  -->
    <div>
    <span class="lix-bal"
              data-bind="text: new String('<?= $escaper->escapeHtml(__('%1', '%1')) ?>').replace('%1', customer().fullname)">
    </span>
    <?php if($blockName->isCustomerLoggedIn()) {
               if($fanId){
       ?>
    <span class="club-count " style="background: linear-gradient(90deg, <?php echo $clubPrefixColor; ?> 50%, <?php echo $clubSecondColor ; ?> 75%, <?php echo $clubThirdColor ; ?> 75%); -webkit-text-fill-color: transparent; -webkit-background-clip: text; ">
        <?php
                echo " " . $fanId;
          ?>
    </span>
    <?php    }
            } ?>
    </div>
        <?php if($blockName->isCustomerLoggedIn()) { ?>
            <a href="/reward/customer/info/" class="lix-balance" id="wallet-balance-placeholder">
                LIX <span class="wallet-points" id="wallet-points"></span>
            </a>
        <?php } ?>
    <!-- /ko -->
</li>
<?php if ($block->getShowPart() == 'welcome') : ?>
    <script type="text/x-magento-init">
    {
        "*": {
            "Magento_Ui/js/core/app": {
                "components": {
                    "customer": {
                        "component": "Magento_Customer/js/view/customer"
                    }
                }
            },
            "clubcolorscheme": {
                "customData": <?= $clubCollection ?>
            }
        }
    }
    </script>
<?php elseif ($block->getShowPart() == 'other') :?>
    <?= $block->getChildHtml() ?>
<?php endif ?>
<script type="text/javascript">
require(['jquery'],function($){

        $("a.shopnow").hover(function(){
            $(this).addClass("custom-acive");
            $(".nav-sections-item-content > .navigation").show("slow");
            $('.category-item.first level-top.label-position-center.ui-menu-item-wrapper').addClass("ui-state-active");
        });

        $(".sections.nav-sections-3.nav-sections").mouseleave(function(){
            $("a.shopnow").removeClass("custom-acive");
            $(".nav-sections-item-content > .navigation").hide();
        });
});
</script>
<script>
require(['jquery'], function ($) {
    $(document).ready(function() {
        var walletBalancePlaceholder = $('#wallet-balance-placeholder');

        if (walletBalancePlaceholder.length) {
            walletBalancePlaceholder.hide();
            $.ajax({
                url: '/lixreward/lixpay/walletbalance',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var points = parseFloat(response.points).toFixed(2);
                        $('.wallet-points').text(response.points);
                        $('.wallet-currency-amount').text(response.currency_amount);

                        walletBalancePlaceholder.show();
                        walletBalancePlaceholder.addClass('wallet-balance-loaded');
                    } else {
                        console.error('Response indicates failure:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX call failed:', status, error);
                    console.error('Response text:', xhr.responseText);
                }
            });
        } else {
            console.log('Placeholder not found');
        }
    });
});
</script>

<style>

        .catalog-category-view .products-grid .product-item .product-item-info.sport-club:hover, .cms-index-index .sport-club:hover, .cms-page-view .sport-club:hover, .customer-account-index .sport-club:hover, .wishlist-index-index .sport-club:hover, .cms-page-view .products-grid .product-item .product-item-info.sport-club:hover, .sportsclub-localproducts-localproducts .products-grid .product-item .product-item-info.sport-club:hover, .sportsclub-sponserproducts-sponserproducts .products-grid .product-item .product-item-info.sport-club:hover{
            box-shadow: -5px 0px 8px 0px <?php echo $clubPrefixColor; ?>, 2px 0px 8px 0px <?php echo $clubSecondColor; ?> !important;
            border-radius: 4px;
            padding: 10px 0px;
        }
        .catalog-category-view .products-grid .product-item-details button.action.tocart.primary.sport-club-tocart:hover, .cms-index-index button.action.tocart.primary.sport-club-tocart:hover, .cms-page-view button.action.tocart.primary.sport-club-tocart:hover, .customer-account-index button.action.tocart.primary.sport-club-tocart:hover, .wp-quickview-popup .box-tocart .action.primary.tocart.sport-club-tocart:hover, .wishlist-index-index button.action.tocart.primary.sport-club-tocart:hover, .catalog-product_compare-index button.action.tocart.primary.sport-club-tocart:hover, .catalog-product-view button.action.tocart.primary.sport-club-tocart:hover, .sportsclub-sponserproducts-sponserproducts a.weltpixel-quickview.weltpixel_quickview_button_v2.sport-club-quickview:hover, .sportsclub-localproducts-localproducts a.weltpixel-quickview.weltpixel_quickview_button_v2.sport-club-quickview:hover{
             background-color: <?php echo $clubThirdColor; ?> !important;
            border: 1px solid <?php echo $clubThirdColor; ?> !important;
        }
        .catalog-category-view .products-grid .product-item-details button.action.tocart.primary.sport-club-tocart:hover span, .cms-index-index button.action.tocart.primary.sport-club-tocart:hover span, .cms-page-view button.action.tocart.primary.sport-club-tocart:hover span, .customer-account-index button.action.tocart.primary.sport-club-tocart:hover span, .wishlist-index-index button.action.tocart.primary.sport-club-tocart:hover span, .catalog-product_compare-index button.action.tocart.primary.sport-club-tocart:hover span, .catalog-product-view button.action.tocart.primary.sport-club-tocart:hover span{
             color: #fff !important;
        }

        .catalog-category-view .products-grid .product-item-details button.action.tocart.primary.sport-club-tocart:hover span:after, .cms-index-index button.action.tocart.primary.sport-club-tocart:hover span:after, .cms-page-view button.action.tocart.primary.sport-club-tocart:hover span:after, .customer-account-index button.action.tocart.primary.sport-club-tocart:hover span:after, .wishlist-index-index button.action.tocart.primary.sport-club-tocart:hover span:after, .catalog-product_compare-index button.action.tocart.primary.sport-club-tocart:hover span:after, .catalog-product-view button.action.tocart.primary.sport-club-tocart:hover span:after{
            color: #fff !important;
        }

        .catalog-category-view .products-grid .product-item-details .weltpixel-quickview.weltpixel_quickview_button_v2.sport-club-quickview:hover{
            background-color: <?php echo $clubThirdColor; ?> !important;
        }

        .cms-comave-retail .custom-slider .owl-controls .owl-prev.sport-club-owlprev, .custom-slider .owl-controls .owl-prev.sport-club-owlprev {
            border-radius: 4px;
            background-color: <?php echo $clubPrefixColor; ?>  !important;
            border: none !important;
            color: #fff;
        }
        .cms-comave-retail .custom-slider .owl-controls .owl-next.sport-club-owlnext, .custom-slider .owl-controls .owl-next.sport-club-owlnext {
            border-radius: 4px;
            background: <?php echo $clubSecondColor; ?> !important;
            border: none !important;
             color: #fff;
        }
        .cms-comave-retail .homeSlider .owl-carousel.sport-club-retailbanner{
            background: linear-gradient(119deg, <?php echo $clubPrefixColor; ?> 30.84%, <?php echo $clubSecondColor; ?> 30.85%);
            padding: 0px 20px 10px 20px;
            border-radius: 10px;
        }
        .cms-comave-retail .content.container-clubproducts.sport-club-productcarousel{
            background: linear-gradient(115deg, <?php echo $clubPrefixColor; ?> 47.84%, <?php echo $clubSecondColor; ?> 47.85%);

        }

        .customer-account-index .RecommendFU .custom-slider .owl-controls .owl-prev.sport-club-owlprev {
            border-radius: 4px;
            background-color: <?php echo $clubPrefixColor; ?> !important;
            border: none !important;
            color: #fff;
        }

        .customer-account-index .RecommendFU .custom-slider .owl-controls .owl-next.sport-club-owlnext {
            border-radius: 4px;
            background: <?php echo $clubSecondColor; ?> !important;
            border: none !important;
            color: #fff;
        }

        button.action.primary.sport-club-action:hover{
            background-color: <?php echo $clubThirdColor; ?> !important;
        }
        button.action.sport-club-action:hover, {
            background-color: <?php echo $clubThirdColor; ?> !important;
        }
        button.action.primary.checkout.sport-checkout:hover, .checkout-index-index.sports-club .action-select-shipping-item:hover, .checkout-index-index.sports-club .action-show-popup:hover, .checkout-index-index.sports-club .button.action.continue.primary:hover, .checkout-index-index.sports-club .modal-inner-wrap .modal-footer .action-save-address:hover, .checkout-index-index.sports-club .modal-inner-wrap .modal-footer .action-hide-popup:hover, .checkout-index-index.sports-club .action.primary.checkout:hover, .checkout-index-index.sports-club .block-minicart .action.primary.checkout:hover, .checkout-index-index.sports-club .opc-payment-additional .action.action-apply:hover, .checkout-index-index.sports-club .form-giftcard-account .actions-toolbar .action-add.primary:hover, .checkout-index-index.sports-club .form-giftcard-account .actions-toolbar .action-check:hover{
            background-color: <?php echo $clubThirdColor; ?> !important;
            border: 1px solid <?php echo $clubThirdColor; ?> !important;
        }
        .qty-button.sport-club-qty:hover{
            background-color: <?php echo $clubThirdColor; ?> !important;
        }

        .container-crystal a.cryviewprod.sport-club-viewmore:hover{
            border-color: <?php echo $clubThirdColor; ?> !important;
            background: <?php echo $clubThirdColor; ?> !important;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
        }
        .cms-comave-retail .content.container-clubproducts.sport-club-productcarousel .custom-slider .crypaltitle a.cryviewprod:hover{
            border: 1px solid border-color: <?php echo $clubThirdColor; ?> !important;
            background: <?php echo $clubThirdColor; ?> !important;
            color: #fff;
            border-radius: 4px;
            padding: 1px 10px;
        }
        body.sports-club {
            background-image: url(<?php echo $blockClub->getMediaUrl().'vendorfiles/image/'. $clubWatermark; ?>);
            background-position: center;
            background-size: cover;
            background-repeat: repeat;
        }

</style>
