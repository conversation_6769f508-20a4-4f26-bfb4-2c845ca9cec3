<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @var \Magento\Customer\Block\Account\Dashboard\Info $block
 * @see \Magento\Reward\Block\Customer\Reward\Info
 * @var \Magento\Reward\Block\Customer\Reward\Info $block
*/
?>
<?php
$helper = $this->helper(\Magento\Reward\Helper\Data::class);
$_helper = $this->helper(\Comave\LixApiConnector\Helper\Data::class);

$blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
$blockUshop = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\TravellerFormData');
$blockPointBal = $block->getLayout()->createBlock('Magento\Reward\Block\Customer\Reward\Info');

$walletblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\WalletData');
$walletBalance = $walletblock->getWalletBalance();


$custEmail = $blockName->getCustomerEmail();
$custWallet = $blockName->getLixWallet();
$lixWallet = 'lix_wallet';

if($custWallet == 'LIXX'){
  $currconv = number_format((float)$_helper->getLixCurrenciesValues($custEmail,$lixWallet), 4, '.', '');
}else{
  $currconv = number_format((float)$_helper->getLixCurrenciesValues($custEmail,$lixWallet), 2, '.', '');
}
$customerId = $blockName->getCustId();

if ($blockName->getRewardPoint() >= 1) {
  $reward = number_format((float)$blockName->getRewardPoint(), 2, '.', '');
  $rreward = ceil($reward);
}else{
  $reward = number_format((float)$blockName->getRewardPoint(), 3, '.', '');
  $rreward = ceil($reward);
}

if ($blockName->getPoints() >= 1) {
  $currencyBalance = number_format((float)$blockName->getPoints(), 2, '.', '');
}else{
  $currencyBalance = number_format((float)$blockName->getPoints(), 3, '.', '');
}


$ref_forms = $blockName->getRefundedForms()->count();
$in_prog = $blockUshop->getInProgressForms($customerId)->count();
$rej_forms = $blockUshop->getRejectedForms($customerId)->count();
$total_forms = $blockUshop->getTotalForm($customerId);

$processing = $blockName->getCustomerOrderCount($customerId);
$complete = $blockName->getCustCompleteOrderCount($customerId);
$orderCount = $blockName->getCustomerTotalOrderCount($customerId);

$refundedForms = $blockName->getRefundedForms();
$sum = 0;
foreach($refundedForms as $data){
    $sum += $data['refund_amount'];
}
$refund_amount = number_format((float)$sum, 2, '.', '');

$helper = $this->helper(\Magento\Reward\Helper\Data::class);

$blockOrder = $block->getLayout()->createBlock('Magento\Sales\Block\Order\Recent');

$_orders = $blockOrder->getOrders();
$count = count($_orders);
$label = $blockName->getOrderCollectionByStatus();

?>
<div class="account-block-info">
  <div class="account-block-parent-div">
    <div class=" col-lg-4 account-block lix-block">
      <div class="row lix-token-row">
        <div class="lix-token-col">
          <a href="<?= $this->getUrl('lixreward/account/lixdashboard') ?>">
              <div class="title" >
                <span class="icon-order-img"><img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/lix.png'; ?>" ><h3>LIX Tokens </h3>
                </span>
              </div>
          </a>
        </div>
      </div>
      <div class="account-details">
        <div class="row lix-default-curren">
          <h4 class="wallet-heading">
                <?php if ($walletBalance['currency'] !== null) { ?>
                    <?= $block->escapeHtml($walletBalance['currency']) ?>
                <?php } ?>
          </h4>
        </div>
        <div class="row lix-curren-conv">
           <?php if (isset($custWallet)) { ?>
                  <p>1 LIX Rewards = <?php echo $currconv; ?></p>
            <?php } ?>
        </div>
        <div class="row lix-detail">
          <div class="lix-rew-bal">
            <div class="entries-details">
              <p>Your LIX Balance</p>
            </div>
            <div class="lix-bal">
              <?php if ($walletBalance['currency'] !== null) { ?>
                <h2><?php echo $walletBalance['points']; ?> </h2>
              <?php } ?>
            </div>
          </div>

          <div class="lix-point-bal">
              <div class="progres-details">
                <p>Reward Values(USD)</p>
              </div>
              <div class="reward-bal">
                <?php if ($walletBalance['currency'] !== null) { ?>
                 <h2>$<?php echo $walletBalance['currency_amount'];?></h2>
                <?php } ?>
                 <!-- <//?= $block->escapeHtml($currencyBalance) ?> -->
              </div>
          </div>

        </div>
      </div>
      <!-- Lix Pay -->
      <div class="row lix-pay-row">
        <div class="lix-pay-col">
           <a href="<?= $this->getUrl('lixreward/lixpay/wallet/') ?>">
              <div class="title" >
                <span class="icon-order-img"><img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/lix.png'; ?>" ><h3>LIX Pay </h3>
                </span>
              </div>
            </a>
        </div>
      </div>
      <!-- Marketplace -->
      <div class="row marketplace-row">
        <div class="marketplace-col">
           <a href="<?= $this->getUrl('lixreward/marketplace/offers/') ?>">
              <div class="title"><h3>Marketplace</h3>
              </div>
            </a>
        </div>
      </div>
      <!-- My Coupons -->
      <div class="row marketplace-row">
        <div class="marketplace-col">
           <a href="<?= $this->getUrl('lixreward/coupon/mycoupon/') ?>">
              <div class="title"><h3>My Coupons</h3>
              </div>
            </a>
        </div>
      </div>
       <!-- Earn Rewards -->
      <div class="row marketplace-row">
        <div class="marketplace-col">
          <a href="<?= $this->getUrl('lixreward/EarnReward/earn/') ?>">
            <div class="title"><h3>Earn Rewards</h3>
             </div>
          </a>
        </div>
      </div>
      <!-- Charity -->
      <div class="row coupons-row">
        <div class="coupons-col">
           <a href="<?= $this->getUrl('lixreward/charity/charity/') ?>">
              <div class="title"><h3>Charity</h3>
              </div>
            </a>
        </div>
      </div>
    </div>

    <div class=" col-lg-4 account-block order-block">
      <div class="row my-order">
        <div class=" my-order-head">
          <a href="<?= $this->getUrl('sales/order/history/') ?>">
              <div class="title">
                <span class="icon-order-img"> <h3>My Orders </h3>
                </span>
              </div>
          </a>
        </div>
      </div>
      <div class="account-details">
        <div class="row my-order-icons">
            <div class="col-one">
              <div class="entries-details">
                 <span class="icon-order-img"><img src='<?php echo $this->getViewFileUrl('images/money_off.png'); ?>' alt="" >
                  <p>To Be Shipped</p>
                </span>
                <h3 class="unpaid_num"><?= $block->escapeHtml($orderCount) ?></h3>
              </div>
            </div>
            <div class="col-two">
              <div class="progres-details">
                <span class="icon-order-img">
                  <img src='<?php echo $this->getViewFileUrl('images/shipped.png'); ?>' alt="" >
                <p>Shipped</p>
                </span>
                <h3 class="shipped_num"><?= $block->escapeHtml($processing) ?></h3>
              </div>
            </div>
            <div class="col-three">
              <div class="refund-details">
                <span class="icon-order-img">
                  <img src='<?php echo $this->getViewFileUrl('images/Vector_cust.png'); ?>' alt="" >
                  <p>Delivered</p>
                </span>
                  <h3 class="shipping_num"><?= $block->escapeHtml($complete) ?></h3>
              </div>
            </div>
        </div>
      </div>
    </div>
    <?php if ($total_forms > 0): ?>
      <div class="col-lg-4 account-block vat-block">
        <div class="row vat-refund-row">
          <div class="vat-refund-col">
             <a href="<?= $this->getUrl('travellerinfo/front/index') ?>">
                <div class="title">
                  <span class="icon-order-img"> <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/Ushop_Brand_-_PNG.png';?>" > <h3>VAT Refund </h3>
                  </span>
                </div>
             </a>
          </div>
        </div>
        <div class="account-details">
          <div class="row vat-refund-row-account">
            <div class="vat-one">
              <div class="entries-details">
                <p>Total Entries</p>
              </div>
              <div class="entries">
                  <h2><?= $block->escapeHtml($total_forms) ?></h2>
              </div>
            </div>
            <div class=" vat-two">
                <div class="progres-details">
                  <p>In Progress</p>
                </div>
                <div class="progres">
                   <h2><?= $block->escapeHtml($in_prog) ?></h2>
                </div>
            </div>
            <div class="vat-three">
              <div class="refund-details">
                  <p>Refunded</p>
              </div>
              <div class="refund">
                   <h2><?= $block->escapeHtml($ref_forms) ?></h2>
              </div>
            </div>
            <div class="vat-four">
              <div class="reject-details">
                <p>Rejected / Expired</p>
              </div>
              <div class="reject">
                  <h2><?= $block->escapeHtml($rej_forms) ?></h2>
              </div>
            </div>
          </div>
        </div>
      </div>
    <?php endif; ?>
  </div>
  <div class="block block-dashboard-info" style="margin-top: 2%;">
    <div class="container-fluid">
      <div class="row heading-row">
        <div class="col-lg-12 heading-col">
            <div>
              <h3>Information</h3>
            </div>
        </div>
      </div>
      <div class="row sub-info-dashboard-one">
        <a class="action edit" href="<?= $block->escapeUrl($block->getUrl('customer/account/edit')) ?>">
          <div class="col-lg-6 sub-one">
            <h6><?= $block->escapeHtml(__('General Profile details')) ?></h6>
            <p><?= $block->escapeHtml(__('Name, email, contact number, profile picture, etc.')) ?></p>
          </div>
          <div class="col-lg-6 sub-two">
            <a class="action edit" href="<?= $block->escapeUrl($block->getUrl('customer/account/edit')) ?>">
              <span style="float: right;"><i class="icon-pencil"></i></span>
            </a>
         </div>
        </a>
      </div>
      <div class="container-fluid">
          <div class="row sub-info-dashboard-one">
              <div class="profile-completion">
                  <div class="progress-bar-container">
                      <div class="progress-bar" style="width: <?php echo $_helper->getProfileCompletionPercentage($customerId); ?>%;"></div>
                  </div>
              </div>
              <div class="status">
                <span>Your profile is <?php echo $_helper->getProfileCompletionPercentage($customerId); ?>% complete</span>
              </div>
          </div>
      </div>
    </div>
  </div>

  <?php if ($block->isNewsletterEnabled()): ?>
    <div class="container-fluid">
      <div class="row unscubscribe">
          <div class="col-lg-6 col-one">
              <div class="box box-newsletter">
                <h3><?= $block->escapeHtml(__('Stay informed with ComAve newsletters.')) ?></h3>
              </div>
              <div class="box-content">
                  <p><?= $block->escapeHtml(__('Be the first to know about the latest arrivals, offers, discounts on ComAve.')) ?></p>
              </div>
          </div>
          <div class="col-lg-6 col-two">
              <a class="action edit cust-unsub-img" href="<?= $block->escapeUrl($block->getUrl('newsletter/manage')) ?>">
                  <button id="subscribeButton">
                      <img src='<?php echo $this->getViewFileUrl('images/unsubscrib.png'); ?>' alt="">
                      <p>Subscribe</p>
                  </button>
              </a>

              <!-- <a class="action edit cust-limit-img" href="<//?= $block->escapeUrl($block->getUrl('newsletter/manage')) ?>"> -->
                <button class="action edit cust-limit-img">
                      <img src='<?php echo $this->getViewFileUrl('images/checklist.png'); ?>' alt="">
                      <p>Limit What I Receive</p>
                  </button>
              <!-- </a> -->
          </div>
      </div>
    </div>
  <?php endif; ?>
</div>



<script>
document.addEventListener("DOMContentLoaded", function() {
  var subscribeButton = document.getElementById('subscribeButton');
  var subscribed = sessionStorage.getItem('subscribed'); // Check if user has subscribed

  // Function to update button text
  function updateButtonText() {
    var buttonText = subscribeButton.querySelector('p');
    if (subscribed === 'true') {
      buttonText.textContent = 'Unsubscribe';
    } else {
      buttonText.textContent = 'Subscribe';
    }
  }

  // Update button text when page loads
  updateButtonText();

  subscribeButton.addEventListener('click', function() {
    // Simulate subscription process
    // Replace this with actual subscription logic
    subscribed = subscribed === 'true' ? 'false' : 'true';
    sessionStorage.setItem('subscribed', subscribed);
    updateButtonText(); // Update button text after subscription status changes
  });

  // Function to get cookie value
  function getCookie(name) {
    var cookies = document.cookie.split(';');
    for (var i = 0; i < cookies.length; i++) {
      var cookie = cookies[i].trim();
      if (cookie.startsWith(name + '=')) {
        return cookie.substring(name.length + 1);
      }
    }
    return '';
  }
});
</script>
