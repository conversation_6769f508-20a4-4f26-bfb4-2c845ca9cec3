<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="page.top">
            <block class="Magento\Cms\Block\Block" name="account_create_page_header">
                <arguments>
                    <argument name="block_id" xsi:type="string">account_create_page_header</argument>
                </arguments>
            </block>
        </referenceContainer>
        <move element="account_create_page_header" destination="page.wrapper" after="top.search"/>
        <referenceBlock name="header.panel" remove="true"/>
        <referenceBlock name="minicart" remove="true"/>
        <referenceContainer name="header.custom">
            <referenceBlock name="my-top-link" remove="true"/>
            <referenceBlock name="top-link" remove="true"/>
            <referenceBlock name="my-ink" remove="true"/>
            <block class="Magento\Framework\View\Element\Text" name="emtpy">
                <arguments>
                    <argument name="text" xsi:type="string">&#160;</argument>
                </arguments>
            </block>
        </referenceContainer>
        <referenceBlock name="customer_form_register" remove="true"/>
        <referenceBlock name="wish-list-link-custom" remove="true"/>
        <referenceBlock name="account_create_page_header" remove="true"/>
    </body>
</page>
