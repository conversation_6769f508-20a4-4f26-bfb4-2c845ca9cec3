<?php

/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>
<?php /** @var $block \Magento\Sales\Block\Order\Info */ ?>

<?php
/** @var  $block \Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer */
$_item = $block->getItem();
$orderData = $block->getOrder();

$orderId = $orderData->getId();
$incrementId = $orderData->getIncrementId();
// $grandTotal = $orderData->getGrandTotal();
$grandTotal = number_format((float)$orderData->getGrandTotal(), 2, '.', '');
$orderStatus = $orderData->getStatus();
// echo "orderStatus:". $orderStatus.'<br>';
$orderState = $orderData->getState();
// echo "OrderState:" . $orderState . '<br>';
$blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
// $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
// $order = $objectManager->create('Magento\Sales\Api\Data\OrderInterface')->load($orderId);

$items = $blockName->getOrderById($orderId);
$baseUrl = $blockName->getBaseUrl();

foreach ($items as $item) {
    $productId = $item->getProductId();
}
$reviewUrl = '';
$sellerData = $blockName->getSellerInfo($productId, $orderData);

if(!empty($sellerData)){
    foreach($sellerData as $sellerInfo){
        $shopUrl = $sellerInfo->getShopUrl();
        $feedUrl = 'marketplace/seller/feedback/shop/' . $shopUrl;
        $reviewUrl = $baseUrl . $feedUrl;
    }
}

$customStatus = "";

$deliveryInfo = array();
if($orderData->getMpDeliveryInformation()){
    $deliveryInfo = json_decode($orderData->getMpDeliveryInformation(),true);
}

$orderStatusCom ="completed";
$orderStatusActive ="";

//$orderst ="completed";
//$orderlevel ="5";
$displayStatus = $block->escapeHtml($orderStatus);

$sellerblockObj= $block->getLayout()->createBlock('Comave\ApiConnector\Block\SellerData');


$orderCol = $sellerblockObj->getMarkateplaceOrder((string)$orderId);


$orderStates = array();

foreach($orderCol as $row) {
    $orderStates[] = $row->getOrderState();

}

if(in_array(null, $orderStates)){
    $displayStatus = "Processing";
}elseif(in_array("In Transit", $orderStates)){
    $orderData->setState("complete")->setStatus("in_transit");
    $orderData->save();
    $orderStatus = $orderData->getStatus();
}elseif(!in_array("In Transit", $orderStates) && in_array("Out Delivery", $orderStates)){
    $orderData->setState("complete")->setStatus("out_delivery");
    $orderData->save();
    $orderStatus = $orderData->getStatus();
}elseif(!in_array("In Transit", $orderStates) && !in_array("Out Delivery", $orderStates) && in_array("Delivered", $orderStates)){
    $orderData->setState("complete")->setStatus("delivered");
    $orderData->save();
    $orderStatus = $orderData->getStatus();
}

if ($orderStatus == 'cancel' && $orderState == 'complete') {

    $orderst ="completed";
    $orderlevel ="7";
    $displayStatus = "Cancelled";
}elseif ($orderStatus == 'failure' && $orderState == 'complete') {

    $orderst ="completed";
    $orderlevel ="6";
    $displayStatus = "Failed";
}elseif ($orderStatus == 'delivered' && $orderState == 'complete') {

    $orderst ="completed";
    $orderlevel ="5";
    $displayStatus = "Delivered";
}elseif ($orderStatus == 'complete' && $orderState == 'complete') {

    $orderst="completed";
    $orderlevel ="4";
    $displayStatus = "Shipped";
}
elseif ($orderStatus == 'in_transit' && $orderState == 'complete') {
    $orderst="completed";
    $orderlevel ="3";
    $displayStatus = "Processing";
}
elseif ($orderStatus == 'pre_transit' && $orderState == 'complete' ) {

    $orderst ="completed";
    $orderlevel ="2";
    $displayStatus = "Processing";
}else{
    $orderst ="completed";
    $orderlevel ="1";
}
?>
<?php if ($orderStatus == 'delivered') { ?>
    <div class="wk-mp-collection-view-btn">
        <a href= "<?php echo $reviewUrl; ?>" title="<?= $escaper->escapeHtml(__('Make a Review'))?>">
            <?= $escaper->escapeHtml(__('Write your Review'))?>
        </a>
    </div>

<?php } ?>


<div class="orderDetails">
    <span class="orderdetail-title">
        Order Details
    </span>
    <div class="order-info">
        <div>
            <?= $block->escapeHtml(
                __(
                    '<span class="label col-5">Order Date</span> %1',
                    '<span class="labelValue">' . $block->formatDate($block->getOrder()->getCreatedAt(), \IntlDateFormatter::LONG) . '</span>'
                ),
                ['span']
            ) ?>
        </div>
        <div>
            <span class="label col-5">Order ID #</span>
            <span class="labelValue"><?= $block->escapeHtml($incrementId); ?></span>

        </div>
        <div>
            <span class="label col-5">Order Total</span>
            <span class="labelValue">$<?= $block->escapeHtml($grandTotal); ?></span>
        </div>
        <?php
        if(isset($deliveryInfo['deliveryDate'])){
            if(!is_null($deliveryInfo['deliveryDate'])){ ?>

        <div>
            <span class="label col-5">Delivery Date</span>
            <span class="labelValue"><?php echo $deliveryInfo['deliveryDate']; ?></span>
        </div>
        <div>
            <span class="label col-5">Delivery Comment</span>
            <span class="labelValue"><?= $deliveryInfo['deliveryComment']; ?></span>
        </div>
        <div>
            <span class="label col-5">Delivery Time</span>
            <span class="labelValue"><?= $deliveryInfo['deliveryTime']; ?></span>
        </div>

        <?php    } }
        ?>
    </div>
</div>

<div class="order-status-block">
    <div class="order-status-text">
        <span>Your Order is</span>
             <span class="order-status"> <?= $block->escapeHtml($displayStatus) ?></span>
    </div>

    <div class="stepper-wrapper ">
        <div id="placed" class="stepper-item  <?php echo  $orderst; ?> ">
            <div class="step-counter"><i class="icon-line-check  "></i></div>
            <div class="step-name">Order Placed</div>
        </div>
        <div id="pending" class="stepper-item <?php if($orderlevel>1){ echo  $orderst; }?> ">
            <div class="step-counter"><i class="icon-line-check"></i></div>
            <div class="step-name">Processing</div>
        </div>
        <!-- <div id="processing" class="stepper-item  <?php //if($orderlevel>2 ){ echo  $orderst; } ?>">
            <div class="step-counter"><i class="icon-line-check "></i></div>
            <div class="step-name">Ready For Shipping</div>
        </div> -->
        <div id="shipped" class="stepper-item <?php if($orderlevel>3){ echo  $orderst;}?>  ">
            <div class="step-counter"><i class="icon-line-check "></i></div>
            <div class="step-name">Shipped</div>
        </div>
        <div id="delivered" class="stepper-item <?php if ($orderlevel>4) {  echo  $orderst;} ?>">
            <div class="step-counter"><i class="icon-line-check "></i></div>
            <div class="step-name">Delivered</div>
        </div>
    </div>
</div>
