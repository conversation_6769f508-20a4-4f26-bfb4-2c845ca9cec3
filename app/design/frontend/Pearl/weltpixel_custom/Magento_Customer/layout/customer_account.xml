<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Customer My Account (All Pages)" design_abstraction="custom">
    <head>
        <title>My Account</title>
    </head>
    <body>
        <referenceContainer name="sidebar.main">
            <block class="Magento\Framework\View\Element\Html\Link\Current"  name="customer-account-navigation-cust-tab"  before="-"/>
        </referenceContainer>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-orders-link">
                <arguments>
                    <argument name="path" xsi:type="string">sales/order/history</argument>
                    <argument name="label" xsi:type="string" translate="true">My Orders</argument>
                    <argument name="sortOrder" xsi:type="number">150</argument>
                    <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">my-orders</item>
                    </argument>
                </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-address-link">
                    <arguments>
                            <argument name="label" xsi:type="string" translate="true">Address Book</argument>
                            <argument name="path" xsi:type="string">customer/address</argument>
                            <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">address-book-icon</item>
                    </argument>
                        </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-account-edit-link">
                    <arguments>
                            <argument name="label" xsi:type="string" translate="true">Account Information</argument>
                            <argument name="path" xsi:type="string">customer/account/edit</argument>
                            <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">account-information-icon</item>
                    </argument>
                        </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\SortLinkInterface" ifconfig="wishlist/general/active" name="customer-account-navigation-wish-list-link">
                <arguments>
                    <argument name="path" xsi:type="string">wishlist</argument>
                    <argument name="label" xsi:type="string" translate="true">My Wish List</argument>
                    <argument name="attributes" xsi:type="array">
                        <item name="class" xsi:type="string">my-wishlist-link</item>
                    </argument>
                </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\SortLinkInterface" ifconfig="newsletter/general/active" name="customer-account-navigation-newsletter-subscriptions-link">
                <arguments>
                    <argument name="path" xsi:type="string">newsletter/manage</argument>
                    <argument name="label" xsi:type="string" translate="true">Newsletter Subscriptions</argument>
                    <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">newsletter-subscription-icon</item>
                    </argument>
                </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-product-reviews-link" ifconfig="catalog/review/active">
                <arguments>
                    <argument name="path" xsi:type="string">review/customer</argument>
                    <argument name="label" xsi:type="string" translate="true">My Product Reviews</argument>
                    <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">my-product-reviews</item>
                </argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface"
                   name="customer-account-navigation-my-credit-cards-link"
            >
                <arguments>
                    <argument name="path" xsi:type="string">vault/cards/listaction</argument>
                    <argument name="label" xsi:type="string" translate="true">Saved Cards (Payment)</argument>
                    <argument name="sortOrder" xsi:type="number">160</argument>
                      <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">saved-cards</item>
                </argument>
                </arguments>
            </block>
        </referenceBlock>


        <referenceBlock name="customer-account-navigation-account-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-giftregistry-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-gift-card-link" remove="true"/>
        <referenceBlock name="social-account" remove="true"/>
        <referenceBlock name="customer-account-navigation-reward-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-magento-invitation-link" remove="true"/>
        <referenceBlock name="stripe-payments-customer-cards" remove="true"/>
        <referenceBlock name="stripe-payments-subscriptions" remove="true"/>
        <referenceBlock name="customer-account-navigation-checkout-sku-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-customer-balance-link" remove="true"/>
        <referenceBlock name="customer-navigation-mpsellerbuyercommunication-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-list-add" remove="true"/>
        <referenceBlock name="customer-account-additional-attribute-link" remove="true"/>
        <referenceBlock name="customer-account-navigation-downloadable-products-link" remove="true"/>

        <referenceBlock name="top.search" remove="true" />
    </body>
</page>
