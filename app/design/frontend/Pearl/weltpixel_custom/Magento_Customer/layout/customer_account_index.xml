<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <move element="page.main.title" destination="page.top" after="-"/>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">My Account</argument>
            </action>
        </referenceBlock>

        <referenceContainer name="content">
            <container name="navlink.container" as="navlinkContainer" label="NavLink Container" htmlTag="div" htmlClass="navlink-container" before="content">
                <block name="customer-account-navigation-cust-tab" before="-"/>
                <block class="Magento\Customer\Block\Account\SortLinkInterface" ifconfig="wishlist/general/active" name="customer-account-navigation-wish-list-link">
                    <arguments>
                        <argument name="path" xsi:type="string">wishlist</argument>
                        <argument name="label" xsi:type="string" translate="true">My Wish List</argument>
                        <argument name="attributes" xsi:type="array">
                            <item name="class" xsi:type="string">my-wishlist-link</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-product-reviews-link" ifconfig="catalog/review/active">
                    <arguments>
                        <argument name="path" xsi:type="string">review/customer</argument>
                        <argument name="label" xsi:type="string" translate="true">My Product Reviews</argument>
                        <argument name="attributes" xsi:type="array">
                              <item name="class" xsi:type="string">my-product-reviews</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-address-link">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Address Book</argument>
                            <argument name="path" xsi:type="string">customer/address</argument>
                            <argument name="attributes" xsi:type="array">
                          <item name="class" xsi:type="string">address-book-icon</item>
                    </argument>
                        </arguments>
                </block>
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-account-edit-link">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Account Information</argument>
                            <argument name="path" xsi:type="string">customer/account/edit</argument>
                            <argument name="attributes" xsi:type="array">
                               <item name="class" xsi:type="string">account-information-icon</item>
                            </argument>
                        </arguments>
                </block>
                <block class="Magento\Framework\View\Element\Html\Link\Current" ifconfig="mprmasystem/settings/status" name="mprmasystem-customer-rma" before="delete-customer">
                    <arguments>
                        <argument name="path" xsi:type="string">mprmasystem/customer/allrma</argument>
                        <argument name="label" xsi:type="string">My Returns</argument>
                        <argument name="attributes" xsi:type="array">
                               <item name="class" xsi:type="string">rma-icon</item>
                            </argument>
                    </arguments>
                </block>
                <block class="Magento\Customer\Block\Account\SortLinkInterface" name="delete-customer">
                    <arguments>
                            <argument name="path" xsi:type="string">removeaccount\index\delete</argument>
                            <argument name="label" xsi:type="string">Delete Account</argument>
                            <argument name="attributes" xsi:type="array">
                               <item name="class" xsi:type="string">delete-account</item>
                            </argument>
                    </arguments>
                </block>
            </container>
           <referenceBlock name="customer_account_dashboard_address" remove="true" />
           <referenceBlock name="customer_account_dashboard_top" remove="true" />
           <referenceBlock name= "customer_account_dashboard_info1" remove="true" />

        </referenceContainer>


    </body>
</page>
