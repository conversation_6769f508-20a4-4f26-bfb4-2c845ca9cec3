<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
    </head>
    <body>
        <referenceBlock name="marketplace-account-switch-link" remove="true"/>
        <referenceContainer name="header.panel">
            <!-- <block class="Magento\Framework\View\Element\Html\Link" name="my-top-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Food ComAve</argument>
                    <argument name="path" xsi:type="string"><![CDATA[https://food.integration-5ojmyuq-pzxhuth22aiuo.us-5.magentosite.cloud/]]></argument>
                    <argument name="class" xsi:type="string">header food links</argument>
                </arguments>
            </block> -->
        </referenceContainer>
        <referenceBlock name="header.links">
            <!-- <block class="Magento\Framework\View\Element\Html\Link" name="marketplace-top-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Sell</argument>
                    <argument name="path" xsi:type="string">marketplace</argument>
                </arguments>
            </block> -->


            <block class="Webkul\Marketplace\Block\View\Html\SwitchLink" name="marketplace-account-switch-link" ifconfig="marketplace/layout_settings/is_separate_panel">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Vendor Dashboard</argument>
                    <argument name="path" xsi:type="string">marketplace\account\dashboard</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="footer_links">
            <block class="Magento\Framework\View\Element\Html\Link" name="marketplace-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Sell</argument>
                    <argument name="path" xsi:type="string">marketplace</argument>
                </arguments>
            </block>
            <block class="Webkul\Marketplace\Block\View\Html\Link" name="marketplace-vendor-login-link" ifconfig="marketplace/layout_settings/is_separate_panel">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Vendor Login</argument>
                    <argument name="path" xsi:type="string">marketplace/account/dashboard</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="catalog.topnav" remove="true"/>
    </body>
</page>
