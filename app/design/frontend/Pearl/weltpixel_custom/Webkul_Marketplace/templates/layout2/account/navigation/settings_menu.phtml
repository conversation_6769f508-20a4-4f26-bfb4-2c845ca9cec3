<?php

$_helper = $block->getMpHelper();
$magentoCurrentUrl = $block->getCurrentUrl();
$isSellerGroup = $_helper->isSellerGroupModuleInstalled();
?>
<li class="wk-mp-item-settings level-0 <?= /* @noEscape */
strpos($magentoCurrentUrl, 'marketplace/account/editprofile' ) ||
strpos($magentoCurrentUrl, 'baseshipping/shipping') ||
strpos($magentoCurrentUrl, 'coditron_customshippingrate/shiptablerates/manage')
? "current active":"";?>" id="wk-mp-menu-settings">
    <a href="#" onclick="return false;" class="">
        <span><?= /* @noEscape */ __('Store Settings')?></span>
    </a>
    <div class="wk-mp-submenu">
        <strong class="wk-mp-submenu-title"><?= /* @noEscape */ __('Settings')?></strong>
        <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
        <ul>
            <li data-ui-id="menu-webkul-marketplace-setting-menu" class="item-menu parent level-1">
                <strong class="wk-mp-submenu-group-title">
                    <span><?= /* @noEscape */ __('Menu')?></span>
                </strong>
                <div class="wk-mp-submenu">
                    <ul>
                        <?php if (($isSellerGroup && $_helper
                        ->isAllowedAction('marketplace/account/editprofile')) || !$isSellerGroup) { ?>
                              <?php if ($_helper->getSellerProfileDisplayFlag()) { ?>
                                <li class="level-2">
                                    <a href="<?= $escaper->escapeUrl($block
                                    ->getUrl('marketplace/account/editprofile', ['_secure' => $block
                                    ->getRequest()->isSecure()])); ?>">
                                        <span><?= /* @noEscape */ __('Profile Settings') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                        <?php } ?>
                    </ul>
                </div>
            </li>
            <?= $block->getChildHtml('layout2_seller_account_navigation_shipping_menu'); ?>
            <?= $block->getChildHtml('layout2_seller_account_navigation_payment_menu'); ?>
            <?= $block->getChildHtml('layout2_seller_account_navigation_return_address_menu'); ?>
        </ul>
    </div>
</li>
